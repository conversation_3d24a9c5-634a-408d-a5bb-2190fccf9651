services:
  web:
    build: 
      context: .
      target: production
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    command: >
      sh -c "cd heibooky && 
      python manage.py migrate &&
      daphne -b 0.0.0.0 -p 8000 heibooky.asgi:application"
    volumes:
      - log_volume:/var/log/heibooky
      - static_volume:/app/heibooky/staticfiles 
      - media_volume:/app/heibooky/media        
    expose:
      - 8000
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - backend
      - monitoring
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  celery-worker:
    build: 
      context: .
      target: production
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    command: >
      sh -c "cd heibooky &&
      celery -A heibooky worker 
      --loglevel=info
      --pool=solo
      --max-tasks-per-child=50"
    volumes:
      - log_volume:/var/log/heibooky
      - /var/run/celery:/var/run/celery
    user: "1000:1000" 
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - backend
      - monitoring
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  celery-beat:
    build: 
      context: .
      target: production
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
    command: >
      sh -c "cd heibooky &&
      celery -A heibooky beat 
      --loglevel=info 
      --scheduler django_celery_beat.schedulers:DatabaseScheduler"
    volumes:
      - log_volume:/var/log/heibooky
      - /var/run/celery:/var/run/celery
    user: "1000:1000"
    depends_on:
      redis:
        condition: service_healthy
      celery-worker:
        condition: service_started
      web:
        condition: service_started
    networks:
      - backend
      - monitoring
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    image: redis:7-alpine
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SETGID
      - SETUID
      - DAC_OVERRIDE
      - CHOWN
    init: true
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
      - ./redis/users.acl:/etc/redis/users.acl:ro
    sysctls:
      - net.core.somaxconn=511
    command: redis-server /etc/redis/redis.conf
    mem_limit: 512m
    cpus: 0.5
    networks:
      - backend
      - monitoring
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  nginx:
    image: nginx:1.25-alpine
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
      - CHOWN
      - SETGID
      - SETUID
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/logs:/var/log/nginx
      - static_volume:/app/heibooky/staticfiles:ro
      - media_volume:/app/heibooky/media:ro
      - ./certbot/conf:/etc/letsencrypt:ro
      - ./certbot/www:/var/www/certbot:ro
      - nginx_cache:/var/cache/nginx
      - nginx_run:/var/run
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - web
    networks:
      - backend
      - monitoring
    restart: unless-stopped

  certbot:
    image: certbot/certbot
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d backend.heibooky.com
    depends_on:
      - nginx

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: prometheus
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    user: "nobody:nobody"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - ./monitoring/prometheus/recording_rules.yml:/etc/prometheus/recording_rules.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=${PROMETHEUS_RETENTION_TIME:-15d}'
      - '--storage.tsdb.retention.size=${PROMETHEUS_RETENTION_SIZE:-10GB}'
      - '--web.max-connections=${PROMETHEUS_WEB_MAX_CONNECTIONS:-512}'
      - '--query.timeout=${PROMETHEUS_QUERY_TIMEOUT:-2m}'
      - '--query.max-concurrency=${PROMETHEUS_QUERY_MAX_CONCURRENCY:-20}'
      - '--web.enable-lifecycle=${PROMETHEUS_WEB_ENABLE_LIFECYCLE:-false}'
      - '--web.enable-admin-api=${PROMETHEUS_WEB_ENABLE_ADMIN_API:-false}'
      - '--web.external-url=http://prometheus:9090'
      - '--web.route-prefix=/'
      - '--log.level=info'
      - '--log.format=json'
    ports:
      - "${PROMETHEUS_EXTERNAL_PORT:-9090}:9090"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: ${PROMETHEUS_MEMORY_LIMIT:-1g}
    cpus: ${PROMETHEUS_CPU_LIMIT:-0.5}
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9090/-/healthy || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  grafana:
    image: grafana/grafana:10.1.0
    container_name: grafana
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    user: "472:472"  # grafana user
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/grafana.ini:/etc/grafana/grafana.ini:ro
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/ssl:/etc/ssl:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD}
      - GF_SECURITY_SECRET_KEY=${GF_SECURITY_SECRET_KEY}
      - GF_SERVER_DOMAIN=${GF_SERVER_DOMAIN:-grafana.heibooky.com}
      - GF_SERVER_ROOT_URL=${GF_SERVER_ROOT_URL:-https://grafana.heibooky.com}
      - GF_SERVER_PROTOCOL=${GF_SERVER_PROTOCOL:-https}
      - GF_SERVER_CERT_FILE=${GF_SERVER_CERT_FILE:-/etc/ssl/certs/grafana.crt}
      - GF_SERVER_CERT_KEY=${GF_SERVER_CERT_KEY:-/etc/ssl/private/grafana.key}
      - GF_SECURITY_COOKIE_SECURE=${GF_SECURITY_COOKIE_SECURE:-true}
      - GF_SECURITY_COOKIE_SAMESITE=${GF_SECURITY_COOKIE_SAMESITE:-strict}
      - GF_USERS_ALLOW_SIGN_UP=${GF_USERS_ALLOW_SIGN_UP:-false}
      - GF_USERS_ALLOW_ORG_CREATE=${GF_USERS_ALLOW_ORG_CREATE:-false}
      - GF_USERS_DEFAULT_ROLE=${GF_USERS_DEFAULT_ROLE:-Viewer}
      - GF_AUTH_ANONYMOUS_ENABLED=${GF_AUTH_ANONYMOUS_ENABLED:-false}
      - GF_ANALYTICS_REPORTING_ENABLED=${GF_ANALYTICS_REPORTING_ENABLED:-false}
      - GF_ANALYTICS_CHECK_FOR_UPDATES=${GF_ANALYTICS_CHECK_FOR_UPDATES:-false}
      - GF_LOG_MODE=${GF_LOG_MODE:-console file}
      - GF_LOG_LEVEL=${GF_LOG_LEVEL:-info}
    ports:
      - "${GRAFANA_EXTERNAL_PORT:-3000}:3000"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: ${GRAFANA_MEMORY_LIMIT:-512m}
    cpus: ${GRAFANA_CPU_LIMIT:-0.5}
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /var/lib/grafana/plugins:noexec,nosuid,size=50m
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: alertmanager
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    user: "nobody:nobody"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - ./monitoring/alertmanager/templates:/etc/alertmanager/templates:ro
      - alertmanager_data:/alertmanager
    environment:
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_FROM=${SMTP_FROM}
      - ALERT_EMAIL_CRITICAL=${ALERT_EMAIL_CRITICAL}
      - ALERT_EMAIL_SECURITY=${ALERT_EMAIL_SECURITY}
      - ALERT_EMAIL_WARNING=${ALERT_EMAIL_WARNING}
      - ALERT_EMAIL_INFO=${ALERT_EMAIL_INFO}
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=${ALERTMANAGER_WEB_EXTERNAL_URL:-http://alertmanager:9093}'
      - '--web.route-prefix=${ALERTMANAGER_WEB_ROUTE_PREFIX:-/}'
      - '--cluster.listen-address=${ALERTMANAGER_CLUSTER_LISTEN_ADDRESS:-0.0.0.0:9094}'
      - '--log.level=info'
      - '--log.format=json'
    ports:
      - "${ALERTMANAGER_EXTERNAL_PORT:-9093}:9093"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: ${ALERTMANAGER_MEMORY_LIMIT:-256m}
    cpus: ${ALERTMANAGER_CPU_LIMIT:-0.25}
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=50m
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9093/-/healthy || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  redis-exporter:
    image: oliver006/redis_exporter:v1.55.0
    container_name: redis-exporter
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    environment:
      - REDIS_ADDR=redis://redis:6379
    ports:
      - "9121:9121"
    networks:
      - backend
      - monitoring
    depends_on:
      - redis
    restart: unless-stopped
    mem_limit: 128m
    cpus: 0.1

  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: node-exporter
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SYS_TIME
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: 128m
    cpus: 0.1

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.2
    container_name: cadvisor
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SYS_ADMIN
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "8080:8080"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: 256m
    cpus: 0.2

  loki:
    image: grafana/loki:2.9.0
    container_name: loki
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    user: "10001:10001"
    volumes:
      - loki_data:/loki
      - ./monitoring/loki/loki-config.yml:/etc/loki/local-config.yaml:ro
    command: -config.file=/etc/loki/local-config.yaml
    ports:
      - "${LOKI_HTTP_LISTEN_PORT:-3100}:3100"
    networks:
      - monitoring
    restart: unless-stopped
    mem_limit: 512m
    cpus: 0.3

  promtail:
    image: grafana/promtail:2.9.0
    container_name: promtail
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    volumes:
      - log_volume:/var/log/heibooky:ro
      - ./monitoring/promtail/promtail-config.yml:/etc/promtail/config.yml:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring
    depends_on:
      - loki
    restart: unless-stopped
    mem_limit: 128m
    cpus: 0.1

volumes:
  redis_data:
  static_volume:
  media_volume:
  log_volume:
  nginx_cache:
  nginx_run:
  prometheus_data:
  grafana_data:
  alertmanager_data:
  loki_data:

networks:
  backend:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16