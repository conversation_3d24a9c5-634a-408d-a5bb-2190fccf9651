# Generated by Django 5.2.4 on 2025-07-30 21:31

import django.db.models.deletion
import services.storage.storage
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("billing", "0002_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Contract",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "pdf_file",
                    models.FileField(
                        help_text="Generated contract PDF file",
                        storage=services.storage.storage.DocumentStorage(
                            bucket_name="heibooky",
                            custom_domain="cdn.heibooky.com",
                            default_acl="public-read",
                            location="media",
                            object_parameters={
                                "CacheControl": "max-age=86400",
                                "ContentDisposition": "attachment",
                                "ContentType": "application/pdf",
                                "Metadata": {"uploaded-by": "heibooky-backend"},
                            },
                        ),
                        upload_to="contracts/%Y/%m/",
                    ),
                ),
                ("generated_at", models.DateTimeField(auto_now_add=True)),
                (
                    "contract_version",
                    models.CharField(
                        default="1.0",
                        help_text="Version of the contract template used",
                        max_length=10,
                    ),
                ),
                (
                    "contract_data",
                    models.JSONField(
                        default=dict,
                        help_text="Snapshot of billing data used to generate the contract",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="contract",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Contract",
                "verbose_name_plural": "Contracts",
            },
        ),
    ]
