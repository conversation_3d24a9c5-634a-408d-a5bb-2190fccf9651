from apps.billing.models import Billing<PERSON>ddress, BillingProfile, Contract, Taxation
from django_countries import countries
from rest_framework import serializers


class BillingProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = BillingProfile
        fields = [
            "id",
            "first_name",
            "last_name",
            "date_of_birth",
            "nationality",
            "gender",
            "recipient_type",
            "company_name",
            "iban",
            "id_document",
            "company_document",
        ]

    def validate(self, data):
        recipient_type = data.get("recipient_type")
        id_document = data.get("id_document")
        company_document = data.get("company_document")

        # Validate `company_name` based on recipient type
        if recipient_type == BillingProfile.COMPANY and not data.get("company_name"):
            raise serializers.ValidationError(
                {"company_name": "Company name is required for company profiles."}
            )

        if recipient_type == BillingProfile.NATURAL_PERSON and data.get("company_name"):
            raise serializers.ValidationError(
                {
                    "company_name": "Company name should not be provided for natural person profiles."
                }
            )

        # Validate required documents based on recipient type
        if recipient_type == BillingProfile.NATURAL_PERSON and not id_document:
            raise serializers.ValidationError(
                {"id_document": "ID document is required for natural persons."}
            )

        if recipient_type == BillingProfile.COMPANY and not company_document:
            raise serializers.ValidationError(
                {"company_document": "Company document is required for companies."}
            )

        # Ensure documents meet file requirements (example: file format validation)
        if id_document and not id_document.name.endswith(
            (".jpg", ".jpeg", ".png", ".pdf")
        ):
            raise serializers.ValidationError(
                {"id_document": "ID document must be a JPG, PNG, or PDF file."}
            )

        if company_document and not company_document.name.endswith(
            (".jpg", ".jpeg", ".png", ".pdf")
        ):
            raise serializers.ValidationError(
                {
                    "company_document": "Company document must be a JPG, PNG, or PDF file."
                }
            )

        return data


class BillingAddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = BillingAddress
        fields = ["id", "street_number", "postcode", "city", "country"]


class TaxationSerializer(serializers.ModelSerializer):
    tin_country = serializers.CharField(required=False)
    tin_number = serializers.CharField(required=False, max_length=20)
    vat_number = serializers.CharField(required=False, max_length=20)

    class Meta:
        model = Taxation
        fields = [
            "id",
            "has_vat_number",
            "vat_number",
            "tin_number",
            "tin_country",
            "rent_more_than_4_properties",
        ]

    def get_tin_country(self, obj):
        # Return tin_country as a string, or None if it’s not set
        return str(obj.tin_country) if obj.tin_country else None

    def validate(self, data):
        has_vat_number = data.get("has_vat_number")

        # If has_vat_number is True, validate VAT-specific requirements
        if has_vat_number:
            if not data.get("vat_number"):
                raise serializers.ValidationError(
                    {
                        "vat_number": "VAT number is required when has_vat_number is true."
                    }
                )
            # Ensure TIN details are not provided
            if data.get("tin_number") or data.get("tin_country"):
                raise serializers.ValidationError(
                    {
                        "tin_number": "TIN details should not be provided when VAT is selected."
                    }
                )

        # If has_vat_number is False, validate TIN-specific requirements
        else:
            if not data.get("tin_number"):
                raise serializers.ValidationError(
                    {"tin_number": "TIN number is required when VAT is not selected."}
                )
            if not data.get("tin_country"):
                raise serializers.ValidationError(
                    {"tin_country": "TIN country is required when VAT is not selected."}
                )
            # Check if tin_country is a valid country code
            if data.get("tin_country") and data["tin_country"] not in dict(countries):
                raise serializers.ValidationError(
                    {"tin_country": "Invalid country code."}
                )

        return data


class ContractSerializer(serializers.ModelSerializer):
    class Meta:
        model = Contract
        fields = ["id", "generated_at", "contract_version", "pdf_file"]
        read_only_fields = ["id", "generated_at", "contract_version", "pdf_file"]
