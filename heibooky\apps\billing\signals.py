import logging

import stripe
from apps.billing.models import Bill<PERSON><PERSON><PERSON>ress, BillingProfile, Contract, Taxation
from apps.integrations.models import StripeCustomer
from django.conf import settings
from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver

logger = logging.getLogger(__name__)


@receiver(post_save, sender=BillingProfile)
@receiver(post_save, sender=BillingAddress)
@receiver(post_save, sender=Taxation)
def update_stripe_account(sender, instance, created, **kwargs):
    """
    Update Stripe account when billing information changes
    """
    try:
        if sender == BillingProfile:
            billing_profile = instance
        else:
            billing_profile = instance.billing_profile

        # Check if user has a Stripe account
        try:
            stripe_customer = StripeCustomer.objects.get(user=billing_profile.owner)
            if not stripe_customer.stripe_customer_id:
                return
        except StripeCustomer.DoesNotExist:
            return

        # Get billing address
        try:
            billing_address = billing_profile.billing_address
        except BillingAddress.DoesNotExist:
            logger.error(f"No billing address found for profile {billing_profile.id}")
            return

        # Validate IBAN format
        try:
            validated_iban = billing_profile.iban.replace(" ", "").upper()
        except AttributeError:
            logger.error(f"Invalid IBAN for profile {billing_profile.id}")
            return

        # Prepare account update data
        account_update_data = {
            "business_type": (
                "company"
                if billing_profile.recipient_type == BillingProfile.COMPANY
                else "individual"
            ),
            "business_profile": {
                "name": (
                    billing_profile.company_name
                    if billing_profile.recipient_type == BillingProfile.COMPANY
                    else f"{billing_profile.first_name} {billing_profile.last_name}"
                ),
            },
            "individual": {
                "first_name": billing_profile.first_name,
                "last_name": billing_profile.last_name,
                "email": billing_profile.owner.email,
                "phone": str(billing_profile.owner.phone),
                "dob": {
                    "day": billing_profile.date_of_birth.day,
                    "month": billing_profile.date_of_birth.month,
                    "year": billing_profile.date_of_birth.year,
                },
                "address": {
                    "line1": billing_address.street_number,
                    "postal_code": billing_address.postcode,
                    "city": billing_address.city,
                    "country": billing_address.country.code,
                },
            },
            "external_account": {
                "object": "bank_account",
                "country": billing_address.country.code,
                "currency": "eur",
                "account_holder_name": f"{billing_profile.first_name} {billing_profile.last_name}",
                "account_holder_type": (
                    "individual"
                    if billing_profile.recipient_type == BillingProfile.NATURAL_PERSON
                    else "company"
                ),
                "account_number": validated_iban,
            },
        }

        # Update Stripe account
        stripe.api_key = settings.STRIPE_SECRET_KEY
        stripe.Account.modify(stripe_customer.stripe_customer_id, **account_update_data)
        logger.info(
            f"Successfully updated Stripe account for user {billing_profile.owner.id}"
        )

    except stripe.error.StripeError as e:
        logger.error(f"Stripe API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error updating Stripe account: {str(e)}")


@receiver(post_save, sender=BillingProfile)
def update_user_profile_billing_status(sender, instance, created, **kwargs):
    """
    Update the user's profile has_billing_profile field when a BillingProfile is created
    """
    try:
        user = instance.owner
        if hasattr(user, "profile"):
            user.profile.has_billing_profile = True
            user.profile.save(update_fields=["has_billing_profile"])
            logger.info(f"Updated has_billing_profile to True for user {user.id}")
    except Exception as e:
        logger.error(f"Error updating user profile billing status: {str(e)}")


@receiver(pre_delete, sender=BillingProfile)
def reset_user_profile_billing_status(sender, instance, **kwargs):
    """
    Reset the user's profile has_billing_profile field when a BillingProfile is deleted
    """
    try:
        user = instance.owner
        if hasattr(user, "profile"):
            user.profile.has_billing_profile = False
            user.profile.save(update_fields=["has_billing_profile"])
            logger.info(f"Reset has_billing_profile to False for user {user.id}")
    except Exception as e:
        logger.error(f"Error resetting user profile billing status: {str(e)}")


@receiver(post_save, sender=Contract)
def update_user_profile_customer_status(sender, instance, created, **kwargs):
    """
    Update the user's profile is_customer field when a Contract is created
    """
    try:
        user = instance.user
        if hasattr(user, "profile"):
            user.profile.is_customer = True
            user.profile.save(update_fields=["is_customer"])
            logger.info(f"Updated is_customer to True for user {user.id}")
    except Exception as e:
        logger.error(f"Error updating user profile customer status: {str(e)}")


@receiver(pre_delete, sender=Contract)
def reset_user_profile_customer_status(sender, instance, **kwargs):
    """
    Reset the user's profile is_customer field when a Contract is deleted
    """
    try:
        user = instance.user
        if hasattr(user, "profile"):
            user.profile.is_customer = False
            user.profile.save(update_fields=["is_customer"])
            logger.info(f"Reset is_customer to False for user {user.id}")
    except Exception as e:
        logger.error(f"Error resetting user profile customer status: {str(e)}")
