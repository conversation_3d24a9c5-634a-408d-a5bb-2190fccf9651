import logging

from .models import BillingProfile

logger = logging.getLogger(__name__)


def check_billing_complete(user):
    """
    Check if user has complete billing information
    Returns True if billing profile, address and taxation exist and are complete

    This optimized version uses proper exception handling and
    prefetch_related to reduce database queries.
    """
    try:
        # Check if user has billing profile
        try:
            billing_profile = user.billingprofile
        except AttributeError:
            return False

        # Check if billing profile has required fields
        if not billing_profile or not all(
            [
                billing_profile.first_name,
                billing_profile.last_name,
                billing_profile.date_of_birth,
                billing_profile.nationality,
                billing_profile.gender,
                billing_profile.iban,
            ]
        ):
            return False

        # Check billing address
        try:
            billing_address = billing_profile.billing_address
        except BillingProfile.billing_address.RelatedObjectDoesNotExist:
            return False

        if not all(
            [
                billing_address.street_number,
                billing_address.postcode,
                billing_address.city,
                billing_address.country,
            ]
        ):
            return False

        # Check taxation
        try:
            taxation = billing_profile.taxation
        except BillingProfile.taxation.RelatedObjectDoesNotExist:
            return False

        return True

    except Exception as e:
        logger.error(f"Error checking billing status: {str(e)}", exc_info=True)
        return False


def generate_contract_pdf(user):
    """
    Generate a contract PDF for the user using xhtml2pdf.
    This function creates a contract PDF based on the user's billing profile and other relevant information.
    """
    import os
    from datetime import datetime
    from io import BytesIO

    from django.conf import settings
    from django.core.files.base import ContentFile
    from django.template.loader import render_to_string
    from xhtml2pdf import pisa

    from .models import Contract

    try:
        # Check if user has complete billing information
        if not check_billing_complete(user):
            logger.error(f"User {user.id} does not have complete billing information")
            return None

        # Check if contract already exists
        if hasattr(user, "contract"):
            logger.info(f"Contract already exists for user {user.id}")
            return user.contract

        # Get billing information
        billing_profile = user.billingprofile
        billing_address = billing_profile.billing_address
        taxation = billing_profile.taxation

        # Prepare contract data for template (both for JSON storage and template rendering)
        contract_data = {
            "user": {
                "email": user.email,
                "full_name": billing_profile.get_full_name(),
                "first_name": billing_profile.first_name,
                "last_name": billing_profile.last_name,
                "date_of_birth": (
                    billing_profile.date_of_birth.strftime("%d/%m/%Y")
                    if billing_profile.date_of_birth
                    else ""
                ),
                "nationality": (
                    billing_profile.nationality.name
                    if billing_profile.nationality
                    else ""
                ),
                "gender": billing_profile.gender,
                "recipient_type": billing_profile.get_recipient_type_display(),
                "company_name": billing_profile.company_name or "",
                "iban": billing_profile.iban,
            },
            "billing_address": {
                "street_number": billing_address.street_number,
                "postcode": billing_address.postcode,
                "city": billing_address.city,
                "country": (
                    billing_address.country.name if billing_address.country else ""
                ),
            },
            "taxation": {
                "has_vat_number": taxation.has_vat_number,
                "vat_number": taxation.vat_number or "",
                "tin_number": taxation.tin_number or "",
                "tin_country": (
                    taxation.tin_country.name if taxation.tin_country else ""
                ),
                "rent_more_than_4_properties": taxation.rent_more_than_4_properties,
            },
            "contract": {
                "date": datetime.now().strftime("%d/%m/%Y"),
                "version": "1.0",
                "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            },
        }

        # Template context (includes original objects for template access)
        # Note: Skip logo for PDF generation to avoid network issues
        logo_url = ""  # Don't use external logo for PDF
        company_name = getattr(settings, "COMPANY_NAME", "Heibooky")
        company_vat = getattr(settings, "COMPANY_VAT_CODE", "02985230909")

        template_context = {
            "user": user,
            "billing_profile": billing_profile,
            "billing_address": billing_address,
            "taxation": taxation,
            "contract": {
                "date": datetime.now().strftime("%d/%m/%Y"),
                "version": "1.0",
                "generated_at": datetime.now(),
            },
            "company_logo": logo_url,
            "company_name": company_name,
            "company_vat": company_vat,
        }

        # Render the contract HTML
        html_content = render_to_string(
            "contracts/partnership_contract.html", template_context
        )

        # Generate PDF using xhtml2pdf
        try:
            logger.info(f"Generating PDF contract using xhtml2pdf for user {user.id}")

            # Create PDF
            pdf_file = BytesIO()
            pisa_status = pisa.CreatePDF(html_content, dest=pdf_file, encoding="utf-8")

            if pisa_status.err:
                logger.error(f"xhtml2pdf error for user {user.id}: {pisa_status.err}")
                # Fall back to HTML
                pdf_content = html_content.encode("utf-8")
                file_extension = "html"
                content_type = "text/html"
                logger.info(f"Falling back to HTML format for user {user.id}")
            else:
                # Success - use PDF content
                pdf_content = pdf_file.getvalue()
                file_extension = "pdf"
                content_type = "application/pdf"
                logger.info(f"Successfully generated PDF contract for user {user.id}")

        except ImportError as import_error:
            logger.warning(
                f"xhtml2pdf not available ({str(import_error)}), using HTML format"
            )
            pdf_content = html_content.encode("utf-8")
            file_extension = "html"
            content_type = "text/html"
        except Exception as general_error:
            logger.error(f"Unexpected error with xhtml2pdf: {str(general_error)}")
            pdf_content = html_content.encode("utf-8")
            file_extension = "html"
            content_type = "text/html"

        # Create filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"contratto_heibooky_{user.id}_{timestamp}.{file_extension}"

        # Create Contract object
        contract = Contract.objects.create(
            user=user, contract_version="1.0", contract_data=contract_data
        )

        # Save the PDF/HTML file
        contract.pdf_file.save(filename, ContentFile(pdf_content), save=True)

        logger.info(f"Contract generated successfully for user {user.id}: {filename}")
        return contract

    except Exception as e:
        logger.error(
            f"Error generating contract for user {user.id}: {str(e)}", exc_info=True
        )
        return None
