from apps.billing.models import Billing<PERSON>ddress, BillingProfile, Contract, Taxation
from apps.billing.serializers import (
    BillingAddressSerializer,
    BillingProfileSerializer,
    ContractSerializer,
    TaxationSerializer,
)
from apps.billing.utils import generate_contract_pdf
from django.db import IntegrityError
from django.http import FileResponse, Http404
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.parsers import <PERSON>Pars<PERSON>, MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.serializers import ValidationError
from rest_framework.views import APIView


class BillingProfileViewSet(viewsets.ModelViewSet):
    serializer_class = BillingProfileSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def get_queryset(self):
        return BillingProfile.objects.filter(owner=self.request.user)

    def perform_create(self, serializer):
        try:
            if BillingProfile.objects.filter(owner=self.request.user).exists():
                raise ValidationError("A billing profile already exists for this user.")
            serializer.save(owner=self.request.user)
        except IntegrityError:
            raise ValidationError(
                "Failed to create billing profile due to data integrity error."
            )
        except Exception as e:
            raise ValidationError(f"Failed to create billing profile: {str(e)}")

    def update(self, request, *args, **kwargs):
        try:
            billing_profile = self.get_queryset().get()
            serializer = self.get_serializer(
                billing_profile, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        except BillingProfile.DoesNotExist:
            return Response(
                {"error": "No billing profile found for this user."},
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BillingAddressViewSet(viewsets.ModelViewSet):
    serializer_class = BillingAddressSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        billing_profile = BillingProfile.objects.filter(owner=self.request.user).first()
        return (
            BillingAddress.objects.filter(billing_profile=billing_profile)
            if billing_profile
            else BillingAddress.objects.none()
        )

    def perform_create(self, serializer):
        try:
            billing_profile = BillingProfile.objects.get(owner=self.request.user)
            if BillingAddress.objects.filter(billing_profile=billing_profile).exists():
                raise ValidationError(
                    "A billing address already exists for this profile."
                )
            serializer.save(billing_profile=billing_profile)
        except BillingProfile.DoesNotExist:
            raise ValidationError("You must create a billing profile first.")
        except IntegrityError:
            raise ValidationError(
                "Failed to create billing address due to data integrity error."
            )


class TaxationViewSet(viewsets.ModelViewSet):
    serializer_class = TaxationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        billing_profile = BillingProfile.objects.filter(owner=self.request.user).first()
        return (
            Taxation.objects.filter(billing_profile=billing_profile)
            if billing_profile
            else Taxation.objects.none()
        )

    def perform_create(self, serializer):
        try:
            billing_profile = BillingProfile.objects.get(owner=self.request.user)
            if Taxation.objects.filter(billing_profile=billing_profile).exists():
                raise ValidationError(
                    "A taxation record already exists for this profile."
                )
            serializer.save(billing_profile=billing_profile)
        except BillingProfile.DoesNotExist:
            raise ValidationError("You must create a billing profile first.")
        except IntegrityError:
            raise ValidationError(
                "Failed to create taxation record due to data integrity error."
            )


class BillingSummaryView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        try:
            billing_profile = BillingProfile.objects.get(owner=request.user)
            billing_address = BillingAddress.objects.filter(
                billing_profile=billing_profile
            ).first()
            taxation = Taxation.objects.filter(billing_profile=billing_profile).first()

            billing_summary = {
                "billing_profile": BillingProfileSerializer(billing_profile).data,
                "billing_address": (
                    BillingAddressSerializer(billing_address).data
                    if billing_address
                    else None
                ),
                "taxation": TaxationSerializer(taxation).data if taxation else None,
            }
            return Response(billing_summary)

        except BillingProfile.DoesNotExist:
            return Response(
                {"error": "No billing profile found."}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ContractView(APIView):
    """Single-resource endpoint for the authenticated user's contract.

    Only supports:
    - GET  : download the user's existing contract PDF
    - POST : generate the contract if it does not already exist

    Any other HTTP method will return 405 (handled by DRF via http_method_names).
    """

    permission_classes = [IsAuthenticated]
    http_method_names = ["get", "post", "options", "head"]  # restrict methods
    serializer_class = ContractSerializer  # kept for consistency / potential reuse

    def get(self, request):
        try:
            contract = request.user.contract
        except Contract.DoesNotExist:
            return Response(
                {"error": "No contract found."}, status=status.HTTP_404_NOT_FOUND
            )

        try:
            if not contract.pdf_file:
                return Response(
                    {"error": "Contract file not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            file_path = contract.pdf_file.name
            actual_extension = file_path.split(".")[-1] if "." in file_path else "pdf"

            return FileResponse(
                contract.pdf_file.open("rb"),
                as_attachment=True,
                filename=f"contratto_heibooky_{contract.user.id}_{contract.generated_at.strftime('%Y%m%d')}.{actual_extension}",
            )
        except Exception as e:  # pragma: no cover - safeguard
            return Response(
                {"error": f"Error downloading contract: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def post(self, request):
        try:
            if hasattr(request.user, "contract"):
                return Response(
                    {
                        "message": "Contract already exists. Each user can only have one contract.",
                        "contract": ContractSerializer(request.user.contract).data,
                    },
                    status=status.HTTP_200_OK,
                )

            contract = generate_contract_pdf(request.user)
            if contract:
                return Response(
                    {
                        "message": "Contract generated successfully",
                        "contract": ContractSerializer(contract).data,
                    },
                    status=status.HTTP_201_CREATED,
                )
            return Response(
                {
                    "error": "Failed to generate contract. Please ensure you have complete billing information."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:  # pragma: no cover - safeguard
            return Response(
                {"error": f"Error generating contract: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
