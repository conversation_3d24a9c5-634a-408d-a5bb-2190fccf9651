from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html

from .models import (
    ActivationFeeConfig,
    Booking,
    BookingBlock,
    Customer,
    PromotionConfig,
    PromotionUsage,
    PropertyActivationFee,
    Reservation,
)


class BookingAdmin(admin.ModelAdmin):
    list_display = (
        "channel_reservation_id",
        "property",
        "formatted_booking_date",
        "status",
        "is_manual",
        "view_reservation_link",
        "formatted_checkin_date",
        "formatted_checkout_date",
    )
    list_filter = ("property", "status", "channel_code", "is_manual")
    search_fields = (
        "id",
        "property__name",
        "channel_code",
        "customer__first_name",
        "customer__last_name",
        "reservation_data__id",
    )
    readonly_fields = (
        "id",
        "booking_date",
        "checkin_date",
        "checkout_date",
        "is_manual",
        "payment_processing_attempts",
        "last_payment_attempt",
        "reservation_link",
    )

    fieldsets = (
        (
            "Booking Information",
            {
                "fields": (
                    "id",
                    "property",
                    "customer",
                    "status",
                    "is_manual",
                    "channel_code",
                )
            },
        ),
        (
            "Dates",
            {
                "fields": ("booking_date", "checkin_date", "checkout_date"),
            },
        ),
        (
            "Payment Information",
            {
                "fields": (
                    "payment_processed",
                    "payment_processing_attempts",
                    "last_payment_attempt",
                ),
            },
        ),
        (
            "Related Information",
            {
                "fields": ("reservation_link",),
            },
        ),
    )

    def formatted_booking_date(self, obj):
        return obj.booking_date.strftime("%Y-%m-%d %H:%M")

    formatted_booking_date.short_description = "Booking Date"

    def channel_reservation_id(self, obj):
        return obj.reservation_data.id

    channel_reservation_id.short_description = "Channel Reservation ID"

    def formatted_checkin_date(self, obj):
        return obj.checkin_date.strftime("%Y-%m-%d")

    formatted_checkin_date.short_description = "Check-in"

    def formatted_checkout_date(self, obj):
        return obj.checkout_date.strftime("%Y-%m-%d")

    formatted_checkout_date.short_description = "Check-out"

    def view_reservation_link(self, obj):
        url = reverse(
            "admin:booking_reservation_change", args=[obj.reservation_data.id]
        )
        return format_html('<a href="{}">View Reservation</a>', url)

    view_reservation_link.short_description = "Reservation"

    def reservation_link(self, obj):
        url = reverse(
            "admin:booking_reservation_change", args=[obj.reservation_data.id]
        )
        return format_html('<a class="button" href="{}">Go to Reservation</a>', url)

    reservation_link.short_description = "Linked Reservation"


class ReservationAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "guest_name",
        "formatted_booked_at",
        "formatted_checkin_date",
        "formatted_checkout_date",
        "formatted_net_price",
        "formatted_total_price",
        "formatted_owner_amount",
        "payment_type",
    )
    list_filter = ("payment_type", "booked_at")
    search_fields = ("id", "guest_name", "reservation_notif_id")
    readonly_fields = (
        "id",
        "reservation_notif_id",
        "booked_at",
        "modified_at",
        "processed_at",
    )

    fieldsets = (
        ("Basic Information", {"fields": ("id", "guest_name", "reservation_notif_id")}),
        (
            "Dates",
            {
                "fields": (
                    "booked_at",
                    "modified_at",
                    "processed_at",
                    "checkin_date",
                    "checkout_date",
                )
            },
        ),
        (
            "Financial Information",
            {
                "fields": (
                    "net_price",
                    "total_price",
                    "total_tax",
                    "deposit",
                    "cancellation_fee",
                    "payment_due",
                    "commission_amount",
                    "payment_type",
                )
            },
        ),
        (
            "Commission Breakdown - Heibooky Properties",
            {
                "fields": (
                    "ota_commission",
                    "heibooky_commission",
                    "payment_charge",
                    "iva_amount",
                    "owner_tax",
                    "net_total_for_owner",
                ),
                "classes": ("collapse",),
                "description": "Detailed commission breakdown for non-Domorent properties",
            },
        ),
        (
            "Commission Breakdown - Domorent Properties",
            {
                "fields": (
                    "cleaning_cost",
                    "taxable_domorent",
                    "tot_platform_commission",
                    "owner_net_transfer",
                ),
                "classes": ("collapse",),
                "description": "Detailed commission breakdown for Domorent properties",
            },
        ),
        (
            "Payment Details - Frontend Display",
            {
                "fields": (
                    "calculation_type",
                    "base_price",
                    "payment_fee_1_5",
                    "vat_22",
                    "subtotal_after_deductions",
                    "flat_tax_21",
                    "net_total",
                ),
                "classes": ("collapse",),
                "description": "Frontend payment details breakdown matching UI display",
            },
        ),
        (
            "Guest Information",
            {
                "fields": (
                    "number_of_guests",
                    "number_of_adults",
                    "number_of_children",
                    "number_of_infants",
                )
            },
        ),
        (
            "Additional Information",
            {"fields": ("remarks", "extra_fees", "taxes", "addons")},
        ),
    )

    def formatted_booked_at(self, obj):
        return obj.booked_at.strftime("%Y-%m-%d %H:%M") if obj.booked_at else "-"

    formatted_booked_at.short_description = "Booked At"

    def formatted_checkin_date(self, obj):
        return obj.checkin_date.strftime("%Y-%m-%d") if obj.checkin_date else "-"

    formatted_checkin_date.short_description = "Check-in"

    def formatted_checkout_date(self, obj):
        return obj.checkout_date.strftime("%Y-%m-%d") if obj.checkout_date else "-"

    formatted_checkout_date.short_description = "Check-out"

    def formatted_net_price(self, obj):
        return f"€{obj.net_price:,.2f}" if obj.net_price else "-"

    formatted_net_price.short_description = "Net Price (Customer Pays)"

    def formatted_total_price(self, obj):
        return f"€{obj.total_price:,.2f}" if obj.total_price else "-"

    formatted_total_price.short_description = "Total Price (After Deductions)"

    def formatted_owner_amount(self, obj):
        # Show either net_total_for_owner (Heibooky) or owner_net_transfer (Domorent)
        if obj.owner_net_transfer and obj.owner_net_transfer > 0:
            return f"€{obj.owner_net_transfer:,.2f}"
        elif obj.net_total_for_owner and obj.net_total_for_owner > 0:
            return f"€{obj.net_total_for_owner:,.2f}"
        else:
            return "-"

    formatted_owner_amount.short_description = "Owner Receives"


class CustomerAdmin(admin.ModelAdmin):
    list_display = ("get_full_name", "email", "telephone", "city", "country")
    search_fields = ("first_name", "last_name", "email", "city", "country")
    list_filter = ("country", "state", "city")
    readonly_fields = ("email", "telephone", "address")

    fieldsets = (
        (
            "Personal Information",
            {"fields": ("first_name", "last_name", "email", "telephone")},
        ),
        (
            "Address Information",
            {"fields": ("address", "city", "state", "country", "zip_code")},
        ),
    )


class BookingBlockAdmin(admin.ModelAdmin):
    list_display = (
        "property",
        "is_active",
        "formatted_start_date",
        "formatted_end_date",
        "reason",
    )
    list_filter = ("property", "start_date", "end_date")
    search_fields = ("property__name", "reason")
    readonly_fields = ("is_active",)

    fieldsets = (
        (
            "Block Information",
            {"fields": ("property", "is_active", "start_date", "end_date", "reason")},
        ),
    )

    def formatted_start_date(self, obj):
        return obj.start_date.strftime("%Y-%m-%d")

    formatted_start_date.short_description = "Start Date"

    def formatted_end_date(self, obj):
        return obj.end_date.strftime("%Y-%m-%d")

    formatted_end_date.short_description = "End Date"


@admin.register(ActivationFeeConfig)
class ActivationFeeConfigAdmin(admin.ModelAdmin):
    """
    Admin configuration for ActivationFeeConfig model.
    """

    list_display = (
        "fee_amount",
        "is_active",
        "description",
        "created_at",
        "created_by",
    )
    list_filter = ("is_active", "created_at")
    search_fields = ("description",)
    readonly_fields = ("created_at", "updated_at", "created_by")
    ordering = ("-created_at",)

    fieldsets = (
        (
            "Fee Configuration",
            {"fields": ("fee_amount", "is_active", "description")},
        ),
        (
            "Metadata",
            {
                "fields": ("created_at", "updated_at", "created_by"),
                "classes": ("collapse",),
            },
        ),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by for new objects
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(PropertyActivationFee)
class PropertyActivationFeeAdmin(admin.ModelAdmin):
    """
    Admin configuration for PropertyActivationFee model.
    """

    list_display = (
        "property_instance",
        "user",
        "original_fee_amount",
        "remaining_fee_amount",
        "recovery_percentage_display",
        "is_fully_recovered",
        "created_at",
    )
    list_filter = ("is_fully_recovered", "created_at", "recovery_started_at")
    search_fields = (
        "property_instance__name",
        "user__email",
        "user__first_name",
        "user__last_name",
    )
    readonly_fields = (
        "created_at",
        "updated_at",
        "recovery_started_at",
        "fully_recovered_at",
        "recovery_percentage_display",
    )
    ordering = ("-created_at",)

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("property_instance", "user")},
        ),
        (
            "Fee Tracking",
            {
                "fields": (
                    "original_fee_amount",
                    "remaining_fee_amount",
                    "total_recovered_amount",
                    "recovery_percentage_display",
                )
            },
        ),
        (
            "Status",
            {
                "fields": (
                    "is_fully_recovered",
                    "recovery_started_at",
                    "fully_recovered_at",
                )
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def recovery_percentage_display(self, obj):
        return f"{obj.recovery_percentage:.1f}%"

    recovery_percentage_display.short_description = "Recovery Progress"


@admin.register(PromotionConfig)
class PromotionConfigAdmin(admin.ModelAdmin):
    """
    Admin configuration for PromotionConfig model.
    """

    list_display = (
        "name",
        "promotion_type",
        "discount_display",
        "target_audience",
        "is_active",
        "validity_period",
        "usage_stats",
        "created_at",
    )
    list_filter = (
        "promotion_type",
        "target_audience",
        "is_active",
        "start_date",
        "end_date",
        "created_at",
    )
    search_fields = ("name", "description", "promotion_code")
    readonly_fields = ("created_at", "updated_at", "created_by", "current_uses")
    ordering = ("-created_at",)

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("name", "description", "promotion_code")},
        ),
        (
            "Promotion Configuration",
            {
                "fields": (
                    "promotion_type",
                    "discount_percentage",
                    "discount_amount",
                    "maximum_discount_amount",
                )
            },
        ),
        (
            "Target Audience & Conditions",
            {
                "fields": (
                    "target_audience",
                    "minimum_booking_amount",
                )
            },
        ),
        (
            "Validity Period",
            {"fields": ("start_date", "end_date")},
        ),
        (
            "Usage Limits",
            {
                "fields": (
                    "max_uses_total",
                    "max_uses_per_user",
                    "current_uses",
                )
            },
        ),
        (
            "Status & Activation Fee Interaction",
            {
                "fields": (
                    "is_active",
                    "applies_before_activation_fee",
                    "waives_activation_fee",
                )
            },
        ),
        (
            "Metadata",
            {
                "fields": ("created_at", "updated_at", "created_by"),
                "classes": ("collapse",),
            },
        ),
    )

    def discount_display(self, obj):
        if obj.promotion_type == PromotionConfig.PromotionType.PERCENTAGE:
            return f"{obj.discount_percentage}%"
        elif obj.promotion_type == PromotionConfig.PromotionType.FIXED_AMOUNT:
            return f"€{obj.discount_amount}"
        else:
            return "Free Activation"

    discount_display.short_description = "Discount"

    def validity_period(self, obj):
        end_date = obj.end_date.strftime("%Y-%m-%d") if obj.end_date else "No expiry"
        return f"{obj.start_date.strftime('%Y-%m-%d')} - {end_date}"

    validity_period.short_description = "Validity Period"

    def usage_stats(self, obj):
        if obj.max_uses_total:
            return f"{obj.current_uses}/{obj.max_uses_total}"
        return f"{obj.current_uses}/∞"

    usage_stats.short_description = "Usage"

    def save_model(self, request, obj, form, change):
        if not change:  # Only set created_by for new objects
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(PromotionUsage)
class PromotionUsageAdmin(admin.ModelAdmin):
    """
    Admin configuration for PromotionUsage model.
    """

    list_display = (
        "promotion",
        "user",
        "reservation",
        "discount_amount",
        "original_amount",
        "final_amount",
        "used_at",
    )
    list_filter = ("used_at", "promotion__promotion_type")
    search_fields = (
        "promotion__name",
        "user__email",
        "user__first_name",
        "user__last_name",
        "reservation__id",
    )
    readonly_fields = ("used_at",)
    ordering = ("-used_at",)

    fieldsets = (
        (
            "Usage Information",
            {"fields": ("promotion", "user", "reservation")},
        ),
        (
            "Financial Details",
            {
                "fields": (
                    "original_amount",
                    "discount_amount",
                    "final_amount",
                )
            },
        ),
        (
            "Timestamp",
            {
                "fields": ("used_at",),
                "classes": ("collapse",),
            },
        ),
    )


# Register the remaining models
admin.site.register(Booking, BookingAdmin)
admin.site.register(Reservation, ReservationAdmin)
admin.site.register(Customer, CustomerAdmin)
admin.site.register(BookingBlock, BookingBlockAdmin)
