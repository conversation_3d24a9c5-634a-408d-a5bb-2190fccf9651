import logging
from datetime import date, datetime, timedelta
from decimal import Decimal, InvalidOperation
from typing import Any, Dict, List, Optional, Tuple

from apps.booking.models import Booking, Customer, Reservation
from apps.stay.models import Property
from django.db import transaction
from django.db.utils import IntegrityError
from django.utils import timezone
from services.notification.handlers import ReservationNotificationHandler

logger = logging.getLogger(__name__)


class ReservationChangeTracker:
    """Tracks changes in reservation details"""

    TRACKED_FIELDS = {
        "checkin_date": "Check-in",
        "checkout_date": "Check-out",
        "total_price": "Total Price",
        "deposit": "Deposit",
        "number_of_guests": "Number of Guests",
        "number_of_adults": "Number of Adults",
        "number_of_children": "Number of Children",
        "number_of_infants": "Number of Infants",
        "payment_type": "Payment Type",
        "guest_name": "Guest Name",
    }

    @staticmethod
    def detect_changes(
        old_reservation: Reservation, new_data: Dict[str, Any]
    ) -> Dict[str, Tuple[Any, Any]]:
        """
        Detect changes between existing reservation and new data
        Returns dict of changed fields with their old and new values
        """
        changes = {}

        for field, display_name in ReservationChangeTracker.TRACKED_FIELDS.items():
            old_value = getattr(old_reservation, field)
            new_value = new_data.get(field)

            # Handle decimal fields
            if isinstance(old_value, Decimal):
                new_value = Decimal(str(to_float(new_value, field)))

            if new_value is not None and old_value != new_value:
                changes[display_name] = (old_value, new_value)

        return changes


def validate_decimal(
    value: Any, field_name: str, max_value: float = 99999999.99
) -> float:
    """Validate decimal values to prevent numeric field overflow."""
    try:
        float_val = float(value) if value not in (None, "") else 0.0
        if float_val > max_value:
            logger.warning(
                f"Value too large for {field_name}: {float_val}. Capping at {max_value}"
            )
            return round(max_value, 2)
        return round(float_val, 2)
    except (ValueError, TypeError):
        logger.error(f"Invalid value for {field_name}: {value}")
        return 0.00


def to_json_field(value: Any, field_name: str = "unnamed field") -> dict:
    """
    Convert a value to a dictionary for JSON fields.
    Handles None, empty lists, and other non-dict values.
    """
    if value is None or value == []:
        return {}
    if isinstance(value, dict):
        return value
    if isinstance(value, (list, tuple)) and len(value) == 0:
        return {}

    logger.warning(
        f"Unexpected type for JSON field {field_name}: {type(value)}. Converting to empty dict."
    )
    return {}


def to_float(value: Any, field_name: str = "unnamed field") -> float:
    """Convert a value to float with overflow protection."""
    return validate_decimal(value, field_name)


def to_int(value: Any) -> int:
    """Convert a value to integer, returning 0 if the value is None or empty."""
    return int(value) if value not in (None, "") else 0


def to_aware_datetime(value: Any) -> Optional[datetime]:
    """Convert a date string or naive datetime to a timezone-aware datetime; returns None if invalid without logging noisy errors."""
    if not value:
        return None

    # Already a datetime
    if isinstance(value, datetime):
        dt = value
    else:
        # Try common formats
        for fmt in ("%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S", "%Y-%m-%d"):
            try:
                dt = datetime.strptime(str(value), fmt)
                break
            except ValueError:
                dt = None
        if dt is None:
            return None

    if timezone.is_naive(dt):
        return timezone.make_aware(dt, timezone.get_current_timezone())
    return dt


def to_date(value: Any) -> Optional[datetime.date]:
    """Convert a value to a date object. Accepts YYYY-MM-DD strings or datetimes; returns None if invalid without logging noisy errors."""
    if not value:
        return None
    if isinstance(value, datetime):
        return value.date()
    # Try YYYY-MM-DD first
    try:
        return datetime.strptime(str(value), "%Y-%m-%d").date()
    except ValueError:
        # Try full timestamp
        try:
            return datetime.strptime(str(value), "%Y-%m-%d %H:%M:%S").date()
        except ValueError:
            # Try ISO format without timezone
            try:
                return datetime.strptime(str(value), "%Y-%m-%dT%H:%M:%S").date()
            except ValueError:
                return None


def derive_stay_dates(
    room_data: Dict[str, Any],
) -> Tuple[Optional[date], Optional[date]]:
    """Derive and validate check-in and check-out dates from a room payload.

    - Prefer arrival_date/departure_date as YYYY-MM-DD or timestamp strings.
    - If missing, use first price.date as check-in and (last price.date + 1 day) as check-out.
    - If only checkout provided, infer checkin = checkout - 1 day.
    - If only checkin provided, infer checkout = checkin + 1 day.
    - If order invalid, attempt minimal corrections or return (None, None).

    Returns: (checkin_date, checkout_date) as date objects or (None, None).
    """
    chk_in = to_date(room_data.get("arrival_date"))
    chk_out = to_date(room_data.get("departure_date"))

    # Derive check-in from price array if missing
    if not chk_in:
        price_entries = room_data.get("price") or []
        if isinstance(price_entries, list) and price_entries:
            sorted_entries = sorted(
                [e for e in price_entries if isinstance(e, dict)],
                key=lambda e: (e.get("date") or ""),
            )
            if sorted_entries:
                first_date = to_date(sorted_entries[0].get("date"))
                if first_date:
                    chk_in = first_date

    # Derive checkout from price array if missing
    if not chk_out and chk_in:
        price_entries = room_data.get("price") or []
        try:
            if isinstance(price_entries, list) and price_entries:
                sorted_entries = sorted(
                    [e for e in price_entries if isinstance(e, dict)],
                    key=lambda e: (e.get("date") or ""),
                )
                if sorted_entries:
                    last_date = to_date(sorted_entries[-1].get("date"))
                    if last_date:
                        chk_out = last_date + timedelta(days=1)
        except Exception:
            pass

    # If only checkout exists
    if chk_out and not chk_in:
        chk_in = chk_out - timedelta(days=1)

    # If only checkin exists
    if chk_in and not chk_out:
        chk_out = chk_in + timedelta(days=1)

    # Validate order
    if chk_in and chk_out and chk_out <= chk_in:
        if chk_out == chk_in:
            chk_out = chk_in + timedelta(days=1)
        else:
            logger.warning("Checkout before checkin in derive_stay_dates - swapping")
            chk_in, chk_out = chk_out, chk_in
        if chk_out <= chk_in:
            logger.error("Invalid date order in derive_stay_dates")
            return None, None

    if not chk_in or not chk_out:
        return None, None

    return chk_in, chk_out


def calculate_heibooky_pricing(
    client_price: float,
    property_instance=None,
    user=None,
    include_activation_fee: bool = True,
    promotion_code: str = None,
) -> Dict[str, float]:
    """
    Calculate Heibooky pricing and commissions for non-Domorent properties.

    Based on the Heibooky Financial Summary structure exactly as shown in the image:
    - Client Price: €1,000.00 (amount customer pays)
    - OTA Commission: €150.00 (15% of Client Price)
    - Heibooky Commission: €80.00 (8% of Client Price)
    - Payment Fee (3.5%): €35.00 (3.5% of Client Price)
    - Activation Fee: €15.00 (configurable, gradual recovery)
    - VAT (22%): €91.30 (22% of OTA + Heibooky Commission)
    - Substitute Tax (21%): €210.00 (21% of Client Price)
    - TOTAL OWNER: €283.70

    Args:
        client_price: Amount customer pays
        property_instance: Property instance for activation fee tracking
        user: User instance for activation fee tracking
        include_activation_fee: Whether to include activation fee in calculation
        promotion_code: Optional promotion code for discounts

    Returns:
        Dictionary with calculated pricing values following exact Heibooky structure
    """

    try:
        # Import required models
        from .models import ActivationFeeConfig, PromotionConfig, PropertyActivationFee

        # Convert client price to Decimal for precise calculations
        client_price_decimal = Decimal(str(client_price))

        # Apply promotional discount if applicable
        final_client_price = client_price_decimal
        promotion_discount = Decimal("0.00")
        promotion_applied = None

        if promotion_code and user:
            try:
                promotion = PromotionConfig.objects.get(
                    promotion_code=promotion_code, is_active=True
                )
                if promotion.can_user_use_promotion(user):
                    promotion_discount = promotion.calculate_discount(
                        client_price_decimal
                    )
                    if promotion.applies_before_activation_fee:
                        final_client_price = client_price_decimal - promotion_discount
                    promotion_applied = promotion
            except PromotionConfig.DoesNotExist:
                pass  # Invalid promotion code, continue without discount

        # Commission rates based on Heibooky Financial Summary
        ota_commission_rate = Decimal("0.15")  # 15% OTA Commission
        heibooky_commission_rate = Decimal("0.08")  # 8% Heibooky Commission
        payment_fee_rate = Decimal("0.035")  # 3.5% Payment Fee
        vat_rate = Decimal("0.22")  # 22% VAT
        substitute_tax_rate = Decimal("0.21")  # 21% Substitute Tax

        # Calculate commissions based on final client price (after promotions if applied before)
        ota_commission = final_client_price * ota_commission_rate
        heibooky_commission = final_client_price * heibooky_commission_rate
        payment_fee = final_client_price * payment_fee_rate

        # Calculate VAT (22% of OTA Commission + Heibooky Commission)
        vat_base = ota_commission + heibooky_commission
        vat_amount = vat_base * vat_rate

        # Calculate Substitute Tax (21% of final client price)
        substitute_tax = final_client_price * substitute_tax_rate

        # Handle activation fee with gradual recovery system
        activation_fee_amount = Decimal("0.00")
        activation_fee_applied = Decimal("0.00")
        remaining_activation_fee = Decimal("0.00")
        activation_fee_waived = False

        # Check if promotion waives activation fee
        if promotion_applied and promotion_applied.waives_activation_fee:
            activation_fee_waived = True
        elif include_activation_fee and property_instance and user:
            # Check if property has existing activation fee tracking
            try:
                activation_fee_status = PropertyActivationFee.objects.get(
                    property_instance=property_instance, user=user
                )
                # Always set the original fee amount for tracking purposes
                activation_fee_amount = activation_fee_status.original_fee_amount
                
                if not activation_fee_status.is_fully_recovered:
                    remaining_activation_fee = (
                        activation_fee_status.remaining_fee_amount
                    )
                else:
                    # Fully recovered, no remaining fee to apply
                    remaining_activation_fee = Decimal("0.00")
            except PropertyActivationFee.DoesNotExist:
                # New property - apply activation fee if active
                if ActivationFeeConfig.is_fee_active():
                    activation_fee_amount = ActivationFeeConfig.get_current_fee()
                    remaining_activation_fee = activation_fee_amount

        # Calculate total deductions before activation fee
        total_deductions_before_activation = (
            ota_commission
            + heibooky_commission
            + payment_fee
            + vat_amount
            + substitute_tax
        )

        # Calculate owner payout before activation fee
        owner_payout_before_activation = (
            final_client_price - total_deductions_before_activation
        )

        # Apply activation fee without making owner payout negative (gradual recovery)
        if remaining_activation_fee > 0 and not activation_fee_waived:
            # Calculate maximum recoverable amount that won't make payout negative
            max_recoverable = max(Decimal("0.00"), owner_payout_before_activation)
            activation_fee_applied = min(remaining_activation_fee, max_recoverable)

        # Apply promotional discount after activation fee if configured
        if promotion_applied and not promotion_applied.applies_before_activation_fee:
            # For promotions applied after activation fee, adjust the final payout
            pass  # This would be handled in a more complex scenario

        # Final owner payout
        final_owner_payout = owner_payout_before_activation - activation_fee_applied

        # Calculate totals for return
        total_price = float(final_client_price)  # Customer pays (after any promotions)
        total_tax = float(vat_amount + substitute_tax)  # VAT + Substitute Tax
        total_commission = float(ota_commission + heibooky_commission + payment_fee)

        return {
            # Frontend display fields matching Heibooky Financial Summary exactly
            "calculation_type": "Heibooky Property",
            "client_price": float(client_price_decimal),  # Original price customer sees
            "final_client_price": total_price,  # Price after promotions
            "base_price": total_price,  # For compatibility
            # Commission breakdown (exactly as shown in image)
            "ota_commission": float(ota_commission),
            "heibooky_commission": float(heibooky_commission),
            "payment_fee_3_5": float(payment_fee),
            "payment_charge": float(payment_fee),  # Alias for compatibility
            "activation_fee": float(activation_fee_applied),
            "vat_22": float(vat_amount),
            "iva_amount": float(vat_amount),  # Alias for compatibility
            "substitute_tax_21": float(substitute_tax),
            "owner_tax": float(substitute_tax),  # Alias for compatibility
            # Final amounts
            "total_owner": float(final_owner_payout),  # Exactly as shown in image
            "net_total_for_owner": float(final_owner_payout),  # Alias for compatibility
            "total_owner_payout": float(final_owner_payout),  # Alias
            # Promotional fields
            "promotion_discount": float(promotion_discount),
            "promotion_applied": promotion_applied.name if promotion_applied else None,
            "promotion_code_used": promotion_code if promotion_applied else None,
            # Backend compatibility fields
            "total_price": total_price,
            "total_tax": total_tax,
            "commission_amount": total_commission,
            # Activation fee tracking
            "activation_fee_amount": float(activation_fee_amount),
            "activation_fee_applied": float(activation_fee_applied),
            "activation_fee_remaining": float(
                remaining_activation_fee - activation_fee_applied
            ),
            "activation_fee_waived": activation_fee_waived,
            "owner_payout_before_activation": float(owner_payout_before_activation),
            # Detailed breakdown for auditing
            "breakdown": {
                "original_client_price": float(client_price_decimal),
                "promotional_discount": float(promotion_discount),
                "final_client_price_after_promotion": float(final_client_price),
                "ota_commission_15_percent": float(ota_commission),
                "heibooky_commission_8_percent": float(heibooky_commission),
                "payment_fee_3_5_percent": float(payment_fee),
                "vat_22_percent_on_commissions": float(vat_amount),
                "substitute_tax_21_percent": float(substitute_tax),
                "total_deductions_before_activation": float(
                    total_deductions_before_activation
                ),
                "owner_payout_before_activation_fee": float(
                    owner_payout_before_activation
                ),
                "activation_fee_deducted": float(activation_fee_applied),
                "final_owner_payout": float(final_owner_payout),
            },
        }

    except (ValueError, TypeError, InvalidOperation) as e:
        logger.error(f"Error calculating Heibooky pricing: {str(e)}")
        # Return fallback values if calculation fails
        return {
            "calculation_type": "Heibooky Property",
            "client_price": client_price,
            "base_price": client_price,
            "ota_commission": 0.0,
            "heibooky_commission": 0.0,
            "payment_fee": 0.0,
            "payment_charge": 0.0,
            "activation_fee": 0.0,
            "vat_22": 0.0,
            "iva_amount": 0.0,
            "substitute_tax_21": 0.0,
            "owner_tax": 0.0,
            "net_total_for_owner": client_price,
            "total_owner_payout": client_price,
            "total_price": client_price,
            "total_tax": 0.0,
            "commission_amount": 0.0,
            "activation_fee_amount": 0.0,
            "activation_fee_applied": 0.0,
            "owner_payout_before_activation": client_price,
        }


def calculate_domorent_pricing(
    original_net_price: float,
    ota_commission_absolute: float = 27.0,
    property_cleaning_cost: float = 0.0,
    property_domorent_percentage: float = 10.0,
    extract_cleaning_from_extras: bool = True,
    room_extras: List[Dict] = None,
) -> Dict[str, float]:
    """
    Calculate Domorent pricing for properties where is_domorent = True.

    Based on the payment details structure:
    - Base Price (from totalprice)
    - OTA Commission (from commissionamount or calculated)
    - Domorent Commission (percentage of base price, typically 10%)
    - Payment Fee 1.5% (1.5% of base price)
    - VAT 22% (22% of total commissions and fees)
    - Subtotal after deductions
    - Flat Tax 21% (21% of subtotal)
    - Net Total for owner

    Args:
        original_net_price: Original net price from SU API (base price)
        ota_commission_absolute: Absolute OTA commission value
        property_cleaning_cost: Property-specific cleaning cost (fallback)
        property_domorent_percentage: Property-specific Domorent percentage
        extract_cleaning_from_extras: Whether to extract cleaning cost from extras
        room_extras: Room extras data containing cleaning costs

    Returns:
        Dictionary with calculated pricing values
    """

    try:
        base_price = Decimal(str(original_net_price))
        ota_commission = Decimal(str(ota_commission_absolute))

        # Deduct 2€ stamp duty from base price
        stamp_duty = Decimal("2.0")
        base_price = base_price - stamp_duty
        if base_price < 0:
            base_price = Decimal("0.0")

        # Extract cleaning cost from room extras if available
        cleaning_cost = Decimal("0.0")
        if extract_cleaning_from_extras and room_extras:
            for extra in room_extras:
                if extra.get("name", "").lower() in [
                    "costo pulizia",
                    "cleaning cost",
                    "pulizia",
                ]:
                    cleaning_cost = Decimal(str(extra.get("value", 0)))
                    break

        # Fallback to property cleaning cost if not found in extras
        if cleaning_cost == 0:
            cleaning_cost = Decimal(str(property_cleaning_cost))

        # Calculate Domorent commission (percentage of base price)
        domorent_rate = Decimal(str(property_domorent_percentage)) / Decimal("100")
        domorent_commission = domorent_rate * base_price

        # Calculate payment fee (1.5% of base price)
        payment_rate = Decimal("0.015")  # 1.5%
        payment_fee = payment_rate * base_price

        # Calculate total commissions and fees for VAT calculation
        total_commissions_and_fees = ota_commission + domorent_commission + payment_fee

        # Calculate VAT (22% of total commissions and fees)
        vat_rate = Decimal("0.22")  # 22%
        vat_amount = vat_rate * total_commissions_and_fees

        # Calculate subtotal after all deductions except flat tax
        total_deductions_before_tax = (
            ota_commission + domorent_commission + payment_fee + vat_amount
        )
        subtotal_after_deductions = base_price - total_deductions_before_tax

        # Calculate Flat Tax (21% of subtotal)
        flat_tax_rate = Decimal("0.21")  # 21%
        flat_tax = flat_tax_rate * subtotal_after_deductions

        # Calculate final net total for owner
        net_total_for_owner = subtotal_after_deductions - flat_tax

        # Total tax includes VAT and Flat Tax
        total_tax = vat_amount + flat_tax

        # Total commission includes all fees except taxes
        commission_amount = ota_commission + domorent_commission + payment_fee

        return {
            # Frontend display fields (matching payment details structure)
            "calculation_type": "Domorent Property",
            "base_price": float(base_price),
            "ota_commission": float(ota_commission),
            "domorent_commission": float(domorent_commission),
            "payment_fee_1_5": float(payment_fee),
            "vat_22": float(vat_amount),
            "subtotal_after_deductions": float(subtotal_after_deductions),
            "flat_tax_21": float(flat_tax),
            "net_total": float(net_total_for_owner),
            "cleaning_cost": float(cleaning_cost),
            # Backend compatibility fields
            "total_price": float(
                base_price
            ),  # Customer pays base price minus stamp duty
            "total_tax": float(total_tax),
            "commission_amount": float(commission_amount),
            "payment_charge": float(payment_fee),
            "iva": float(vat_amount),  # Keep for backward compatibility
            "vat": float(vat_amount),
            "cedolare_tax": float(flat_tax),  # Map flat tax to cedolare tax field
            "owner_net_transfer": float(net_total_for_owner),
            "taxable_domorent": float(base_price),  # Full base price is taxable
            "total_deductions": float(total_deductions_before_tax + flat_tax),
            "stamp_duty": float(stamp_duty),
        }

    except (ValueError, TypeError, InvalidOperation) as e:
        logger.error(f"Error calculating Domorent pricing: {str(e)}")
        # Return safe fallback values
        return {
            "calculation_type": "Domorent Property",
            "base_price": original_net_price,
            "ota_commission": float(ota_commission_absolute),
            "domorent_commission": 0.0,
            "payment_fee_1_5": 0.0,
            "vat_22": 0.0,
            "subtotal_after_deductions": original_net_price,
            "flat_tax_21": 0.0,
            "net_total": original_net_price,
            "cleaning_cost": float(property_cleaning_cost),
            "total_price": original_net_price,
            "total_tax": 0.0,
            "commission_amount": float(ota_commission_absolute),
            "payment_charge": 0.0,
            "iva": 0.0,
            "vat": 0.0,
            "cedolare_tax": 0.0,
            "owner_net_transfer": original_net_price,
            "taxable_domorent": original_net_price,
            "total_deductions": 0.0,
        }


def process_push_reservation(reservations: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Process a single reservation from SU's push notification system.

    This function is designed to handle individual reservations pushed by SU's Channel Manager
    following their Push API method. It processes one reservation at a time and handles
    all the necessary database operations and notifications.

    Args:
        reservations: List of dictionaries containing reservation data from SU push notification

    Returns:
        Dictionary with processing status and details
    """
    try:
        if not reservations:
            logger.warning("No reservations to process")
            return {"status": "warning", "message": "No reservations provided"}

        reservation_data = reservations[0]
        hotel_id = reservation_data.get("hotel_id")

        if not hotel_id:
            logger.error("Missing hotel_id in reservation data")
            return {
                "status": "error",
                "message": "Missing hotel_id in reservation data",
            }

        try:
            property = Property.objects.get(hotel_id=hotel_id)
            logger.info(f"Found property {property.name} for hotel_id {hotel_id}")
        except Property.DoesNotExist:
            logger.error(f"Property with hotel_id {hotel_id} does not exist")
            return {
                "status": "error",
                "message": f"Property with hotel_id {hotel_id} not found in database",
            }

        affiliation = reservation_data.get("affiliation", {})

        with transaction.atomic():
            # Process customer data
            customer_data = reservation_data.get("customer", {})
            rooms = reservation_data.get("rooms", [])

            # Create unique customer lookup using email, name, and phone
            customer_email = customer_data.get("email", "").strip()
            customer_first_name = customer_data.get("first_name", "").strip()
            customer_last_name = customer_data.get("last_name", "").strip()
            customer_telephone = customer_data.get("telephone", "").strip()

            # Use combination of email, name, and phone for unique identification
            customer, _ = Customer.objects.update_or_create(
                email=customer_email,
                first_name=customer_first_name,
                last_name=customer_last_name,
                telephone=customer_telephone,
                defaults={
                    "address": customer_data.get("address"),
                    "city": customer_data.get("city"),
                    "state": customer_data.get("state"),
                    "country": customer_data.get("countrycode"),
                    "zip_code": customer_data.get("zip"),
                },
            )

            for room_data in rooms:
                # Check for existing reservation to track changes
                existing_reservation = Reservation.objects.filter(
                    id=reservation_data["id"]
                ).first()
                # Prepare reservation data
                original_total_tax = to_float(room_data.get("totaltax"), "total_tax")
                original_commission = to_float(
                    reservation_data.get("commissionamount"), "commission_amount"
                )
                original_net_price = (
                    to_float(room_data.get("totalprice"), "net_price")
                    - original_total_tax
                )

                # Apply Domorent pricing logic if property is Domorent and reservation is from SU API
                if property.is_domorent:
                    logger.info(
                        f"Applying Domorent pricing for reservation {reservation_data['id']}"
                    )
                    # Always use the property's configured cleaning_cost & domorent_percentage.
                    # Business rule update: ignore any cleaning cost embedded in room extras to ensure
                    # consistency across all Domorent reservations (requested change).
                    room_extras = room_data.get(
                        "extracomponents", []
                    )  # retained for potential future use

                    # For Domorent, we use the pricing structure with property-specific settings only.
                    # OTA commission is provided from SU; cleaning cost comes strictly from the Property model.
                    try:
                        domorent_pricing = calculate_domorent_pricing(
                            original_net_price=original_net_price,
                            ota_commission_absolute=original_commission,
                            property_cleaning_cost=float(
                                getattr(property, "cleaning_cost", 0)
                            ),
                            property_domorent_percentage=float(
                                getattr(property, "domorent_percentage", 10)
                            ),
                            # Force ignoring extras-specified cleaning cost to honor property settings
                            extract_cleaning_from_extras=False,
                            room_extras=room_extras,
                        )

                        # Use calculated Domorent pricing
                        calculated_total_price = domorent_pricing["total_price"]
                        calculated_total_tax = domorent_pricing["total_tax"]
                        calculated_commission = domorent_pricing["commission_amount"]

                        logger.info(
                            f"Domorent pricing applied - Original: net={original_net_price}, tax={original_total_tax}, "
                            f"commission={original_commission} | "
                            f"Calculated: total={calculated_total_price}, tax={calculated_total_tax}, "
                            f"commission={calculated_commission}, owner_receives={domorent_pricing['owner_net_transfer']}"
                        )
                    except Exception as e:
                        logger.error(
                            f"Error calculating Domorent pricing for reservation {reservation_data['id']}: {str(e)}"
                        )
                        # Fallback to original values
                        calculated_total_price = original_net_price
                        calculated_total_tax = original_total_tax
                        calculated_commission = original_commission
                        domorent_pricing = {
                            "owner_net_transfer": original_net_price,
                            "calculation_type": "Domorent Property (Fallback)",
                        }
                else:
                    # Apply Heibooky pricing for non-Domorent properties
                    logger.info(
                        f"Applying Heibooky pricing for reservation {reservation_data['id']}"
                    )
                    try:
                        heibooky_pricing = calculate_heibooky_pricing(
                            client_price=original_net_price,
                            property_instance=property,
                            user=getattr(property, "user", None),
                            include_activation_fee=True,
                        )

                        # Use calculated Heibooky pricing
                        calculated_total_price = heibooky_pricing["total_price"]
                        calculated_total_tax = heibooky_pricing["total_tax"]
                        calculated_commission = heibooky_pricing["commission_amount"]

                        logger.info(
                            f"Heibooky pricing applied - Original: net={original_net_price}, tax={original_total_tax}, "
                            f"commission={original_commission} | "
                            f"Calculated: total={calculated_total_price}, tax={calculated_total_tax}, "
                            f"commission={calculated_commission}, net_for_owner={heibooky_pricing['net_total_for_owner']}"
                        )
                    except Exception as e:
                        logger.error(
                            f"Error calculating Heibooky pricing for reservation {reservation_data['id']}: {str(e)}"
                        )
                        # Fallback to original values
                        calculated_total_price = original_net_price
                        calculated_total_tax = original_total_tax
                        calculated_commission = original_commission
                        heibooky_pricing = {
                            "net_total_for_owner": original_net_price,
                            "calculation_type": "Heibooky Property (Fallback)",
                        }

                # Determine the total_price based on property type
                if property.is_domorent:
                    # For Domorent: total_price is what owner receives after all deductions
                    final_total_price = domorent_pricing["owner_net_transfer"]
                else:
                    # For Heibooky: total_price is what owner receives after all deductions
                    final_total_price = heibooky_pricing["net_total_for_owner"]

                # Derive and validate stay dates using helper
                chk_in, chk_out = derive_stay_dates(room_data)
                if not chk_in or not chk_out:
                    logger.error(
                        f"Missing or invalid arrival/departure dates for reservation {reservation_data.get('id')} room {room_data.get('id')}; skipping room"
                    )
                    continue

                reservation_defaults = {
                    "guest_name": room_data.get("guest_name"),
                    "booked_at": to_aware_datetime(reservation_data.get("booked_at")),
                    # Reservation model stores dates (DateField); keep these as dates
                    "checkin_date": chk_in,
                    "checkout_date": chk_out,
                    "net_price": original_net_price,  # Original amount from SU API (what customer pays)
                    "total_price": final_total_price,  # Amount after deductions (what owner receives)
                    "total_tax": calculated_total_tax,  # Use calculated tax
                    "deposit": to_float(reservation_data.get("deposit"), "deposit"),
                    "commission_amount": calculated_commission,  # Use calculated commission
                    "payment_due": to_float(
                        reservation_data.get("paymentdue"), "payment_due"
                    ),
                    "cancellation_fee": to_float(
                        reservation_data.get("cancellation_fee"), "cancellation_fee"
                    ),
                    "reservation_notif_id": reservation_data.get(
                        "reservation_notif_id"
                    ),
                    "payment_type": reservation_data.get("paymenttype"),
                    "remarks": customer_data.get("remarks"),
                    "addons": to_json_field(room_data.get("addons"), "addons"),
                    "taxes": to_json_field(room_data.get("taxes"), "taxes"),
                    "number_of_infants": to_int(
                        reservation_data.get("numberofinfants")
                    ),
                    "modified_at": to_aware_datetime(
                        reservation_data.get("modified_at")
                    ),
                    "number_of_guests": to_int(room_data.get("numberofguests")),
                    "number_of_adults": to_int(room_data.get("numberofadults")),
                    "number_of_children": to_int(room_data.get("numberofchildren")),
                    "processed_at": to_aware_datetime(
                        reservation_data.get("processed_at")
                    ),
                }

                # Add detailed commission breakdown for non-Domorent properties
                if not property.is_domorent:
                    try:
                        reservation_defaults.update(
                            {
                                # Backend compatibility fields
                                "ota_commission": heibooky_pricing.get(
                                    "ota_commission", 0.0
                                ),
                                "heibooky_commission": heibooky_pricing.get(
                                    "heibooky_commission", 0.0
                                ),
                                "payment_charge": heibooky_pricing.get(
                                    "payment_charge", 0.0
                                ),
                                "iva_amount": heibooky_pricing.get("iva_amount", 0.0),
                                "owner_tax": heibooky_pricing.get("owner_tax", 0.0),
                                "net_total_for_owner": heibooky_pricing.get(
                                    "net_total_for_owner", 0.0
                                ),
                                # Frontend display fields
                                "calculation_type": heibooky_pricing.get(
                                    "calculation_type", "Heibooky Property"
                                ),
                                "base_price": heibooky_pricing.get("base_price", 0.0),
                                "net_total": heibooky_pricing.get(
                                    "net_total_for_owner", 0.0
                                ),
                            }
                        )
                    except KeyError as e:
                        logger.error(
                            f"Missing key in Heibooky pricing calculation for reservation {reservation_data['id']}: {str(e)}"
                        )
                        # Set safe fallback values
                        reservation_defaults.update(
                            {
                                "ota_commission": 0.0,
                                "heibooky_commission": 0.0,
                                "payment_charge": 0.0,
                                "iva_amount": 0.0,
                                "owner_tax": 0.0,
                                "net_total_for_owner": final_total_price,
                                "calculation_type": "Heibooky Property",
                                "base_price": original_net_price,
                                "net_total": final_total_price,
                            }
                        )
                else:
                    # Add Domorent-specific breakdown
                    try:
                        reservation_defaults.update(
                            {
                                # Backend compatibility fields
                                "ota_commission": domorent_pricing.get(
                                    "ota_commission", 0.0
                                ),
                                "payment_charge": domorent_pricing.get(
                                    "payment_charge", 0.0
                                ),
                                "iva_amount": domorent_pricing.get("iva", 0.0),
                                "owner_tax": domorent_pricing.get(
                                    "cedolare_tax", 0.0
                                ),  # Store cedolare tax as owner_tax
                                "cleaning_cost": domorent_pricing.get(
                                    "cleaning_cost", 0.0
                                ),
                                "taxable_domorent": domorent_pricing.get(
                                    "taxable_domorent", 0.0
                                ),
                                "owner_net_transfer": domorent_pricing.get(
                                    "owner_net_transfer", 0.0
                                ),
                                # Store domorent commission in heibooky_commission field for compatibility
                                "heibooky_commission": domorent_pricing.get(
                                    "domorent_commission", 0.0
                                ),
                                # Frontend display fields
                                "calculation_type": domorent_pricing.get(
                                    "calculation_type", "Domorent Property"
                                ),
                                "base_price": domorent_pricing.get("base_price", 0.0),
                                "payment_fee_1_5": domorent_pricing.get(
                                    "payment_fee_1_5", 0.0
                                ),
                                "vat_22": domorent_pricing.get("vat_22", 0.0),
                                "subtotal_after_deductions": domorent_pricing.get(
                                    "subtotal_after_deductions", 0.0
                                ),
                                "flat_tax_21": domorent_pricing.get("flat_tax_21", 0.0),
                                "net_total": domorent_pricing.get("net_total", 0.0),
                            }
                        )
                    except KeyError as e:
                        logger.error(
                            f"Missing key in Domorent pricing calculation for reservation {reservation_data['id']}: {str(e)}"
                        )
                        # Set safe fallback values
                        reservation_defaults.update(
                            {
                                "ota_commission": original_commission,
                                "payment_charge": 0.0,
                                "iva_amount": 0.0,
                                "owner_tax": 0.0,
                                "cleaning_cost": 0.0,
                                "taxable_domorent": original_net_price,
                                "owner_net_transfer": final_total_price,
                                "heibooky_commission": 0.0,
                                "calculation_type": "Domorent Property",
                                "base_price": original_net_price,
                                "payment_fee_1_5": 0.0,
                                "vat_22": 0.0,
                                "subtotal_after_deductions": original_net_price,
                                "flat_tax_21": 0.0,
                                "net_total": final_total_price,
                            }
                        )

                # Track changes if reservation exists
                changes = {}
                if existing_reservation:
                    changes = ReservationChangeTracker.detect_changes(
                        existing_reservation, reservation_defaults
                    )

                # Update or create reservation
                reservation, is_new = Reservation.objects.update_or_create(
                    id=reservation_data["id"], defaults=reservation_defaults
                )

                # Process booking with duplicate handling
                try:
                    with transaction.atomic():
                        # Try to get existing booking first
                        existing_booking = (
                            Booking.objects.filter(
                                reservation_data__id=reservation_data["id"]
                            )
                            .select_related("property", "customer")
                            .first()
                        )

                        status = room_data.get("roomstaystatus")
                        # Derive and validate dates using helper
                        b_checkin, b_checkout = derive_stay_dates(room_data)
                        if not b_checkin or not b_checkout:
                            logger.error(
                                f"Skipping booking creation due to missing dates for reservation {reservation_data.get('id')} room {room_data.get('id')}"
                            )
                            continue

                        booking_defaults = {
                            "property": property,
                            "customer": customer,
                            "is_manual": False,
                            "status": status,
                            # Booking model stores DateField; assign dates
                            "checkin_date": b_checkin,
                            "checkout_date": b_checkout,
                            "channel_code": (
                                int(affiliation.get("OTA_Code"))
                                if str(affiliation.get("OTA_Code") or "").isdigit()
                                else None
                            ),
                            "booking_date": to_aware_datetime(
                                reservation_data.get("booked_at")
                            ),
                        }
                        if existing_booking:
                            # Update existing booking
                            for key, value in booking_defaults.items():
                                setattr(existing_booking, key, value)
                            existing_booking.save()
                            booking = existing_booking
                            is_new_booking = False
                        else:
                            # Create new booking
                            booking = Booking.objects.create(
                                reservation_data=reservation, **booking_defaults
                            )
                            is_new_booking = True

                        # Handle notifications
                        handler = ReservationNotificationHandler(property, customer)

                        if (
                            is_new_booking
                            or status == Booking.Status.NEW
                            or status == Booking.Status.REQUEST
                        ):
                            handler.new_reservation_handler(booking)
                        elif changes and status == Booking.Status.MODIFIED:
                            handler.modified_reservation_handler(booking, changes)
                        elif status == Booking.Status.CANCELLED:
                            handler.cancelled_reservation_handler(booking)

                except IntegrityError as e:
                    logger.error(
                        f"Integrity error processing booking for reservation {reservation_data['id']}: {str(e)}",
                        exc_info=True,
                    )
                    continue
                except Exception as e:
                    logger.error(
                        f"Error processing booking for reservation {reservation_data['id']}: {str(e)}",
                        exc_info=True,
                    )
                    continue

                # acknowledge the reservation
                # reservation_notification(reservation)

        logger.info("Completed reservation fetch task successfully")
        return {
            "status": "success",
            "message": "Reservation processed successfully",
            "reservation_id": reservations[0].get("id"),
        }

    except KeyError as e:
        logger.error(f"Missing required field in reservation data: {str(e)}")
        return {"status": "error", "message": f"Missing required field: {str(e)}"}
    except ValueError as e:
        logger.error(f"Invalid data format in reservation: {str(e)}")
        return {"status": "error", "message": f"Invalid data format: {str(e)}"}
    except Exception as e:
        logger.error(
            f"Unexpected error processing reservation: {str(e)}", exc_info=True
        )
        hotel_id = (
            reservations[0].get("hotel_id", "unknown") if reservations else "unknown"
        )
        return {
            "status": "error",
            "message": f"Unexpected error processing reservation for hotel_id {hotel_id}: {str(e)}",
        }
