"""
Django management command to initialize and update the Heibooky financial system.

This command:
1. Creates default activation fee configuration
2. Updates existing reservations with proper calculation types
3. Recalculates financial data for Heibooky properties
4. Validates data integrity

Usage:
    python manage.py initialize_financial_system
    python manage.py initialize_financial_system --recalculate-all
    python manage.py initialize_financial_system --dry-run
"""

import logging
from decimal import Decimal

from apps.booking.api import calculate_heibooky_pricing
from apps.booking.models import (
    ActivationFeeConfig,
    Booking,
    PropertyActivationFee,
    Reservation,
)
from apps.stay.models import Property
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

User = get_user_model()
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Initialize and update the Heibooky financial system"

    def add_arguments(self, parser):
        parser.add_argument(
            "--recalculate-all",
            action="store_true",
            help="Recalculate all existing Heibooky reservations",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be updated without making changes",
        )
        parser.add_argument(
            "--activation-fee",
            type=float,
            default=150.00,
            help="Activation fee amount (default: 150.00)",
        )

    def handle(self, *args, **options):
        """Main command handler."""
        self.dry_run = options["dry_run"]
        self.recalculate_all = options["recalculate_all"]
        self.activation_fee_amount = Decimal(str(options["activation_fee"]))

        self.stdout.write(
            self.style.SUCCESS("=== Heibooky Financial System Initialization ===")
        )

        if self.dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No changes will be made")
            )

        try:
            with transaction.atomic():
                # Step 1: Initialize activation fee configuration
                config = self.initialize_activation_fee_config()

                # Step 2: Initialize missing PropertyActivationFee records for Heibooky properties
                new_activation_records, properties_with_new_records = (
                    self.initialize_property_activation_fees(config)
                )

                # Step 3: Update reservation calculation types
                self.update_calculation_types()

                # Step 4: Recalculate activation fees for properties that got new records
                if new_activation_records > 0:
                    self.stdout.write(
                        self.style.WARNING(
                            f"📊 Recalculating reservations due to {new_activation_records} new activation fee records..."
                        )
                    )
                    self.recalculate_activation_fees_for_properties(
                        properties_with_new_records
                    )

                # Step 5: Recalculate all financial data if requested
                if self.recalculate_all:
                    self.recalculate_heibooky_reservations()

                # Step 6: Validate data integrity
                self.validate_data_integrity()

                if self.dry_run:
                    # Rollback transaction in dry run mode
                    transaction.set_rollback(True)
                    self.stdout.write(
                        self.style.WARNING("DRY RUN COMPLETE - No changes made")
                    )
                else:
                    self.stdout.write(
                        self.style.SUCCESS("Financial system initialization complete!")
                    )

        except Exception as e:
            logger.error(f"Error initializing financial system: {str(e)}")
            raise CommandError(f"Initialization failed: {str(e)}")

    def initialize_activation_fee_config(self):
        """Initialize activation fee configuration."""
        self.stdout.write("Initializing activation fee configuration...")

        # Check if configuration already exists
        existing_config = ActivationFeeConfig.objects.filter(is_active=True).first()

        if existing_config:
            self.stdout.write(
                f"  ✓ Active configuration exists: €{existing_config.fee_amount}"
            )
            return existing_config

        if not self.dry_run:
            # Create default admin user if none exists
            admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                admin_user = User.objects.filter(is_staff=True).first()

            config = ActivationFeeConfig.objects.create(
                fee_amount=self.activation_fee_amount,
                is_active=True,
                description="Default activation fee configuration created by initialization script",
                created_by=admin_user,
            )

            self.stdout.write(
                self.style.SUCCESS(
                    f"  ✓ Created activation fee config: €{config.fee_amount}"
                )
            )
            return config
        else:
            self.stdout.write(
                f"  [DRY RUN] Would create activation fee config: €{self.activation_fee_amount}"
            )
            return None

    def initialize_property_activation_fees(self, config):
        """Initialize PropertyActivationFee records for Heibooky properties without them."""
        self.stdout.write("Initializing property activation fee records...")

        if not config:
            self.stdout.write(
                self.style.WARNING(
                    "  ⚠️  Skipping - No activation fee configuration available"
                )
            )
            return 0, []

        # Get all Heibooky properties (is_domorent = False)
        heibooky_properties = Property.objects.filter(is_domorent=False)

        # Find properties without activation fee records
        properties_without_activation = []
        for property_obj in heibooky_properties:
            if not PropertyActivationFee.objects.filter(
                property_instance=property_obj
            ).exists():
                properties_without_activation.append(property_obj)

        if not properties_without_activation:
            self.stdout.write(
                "  ✓ All Heibooky properties already have activation fee records"
            )
            return 0, []

        created_count = 0
        properties_with_new_records = []
        for property_obj in properties_without_activation:
            try:
                # Get the property owner (first staff member)
                user = property_obj.staffs.first()

                if not user:
                    self.stdout.write(
                        self.style.WARNING(
                            f"  ⚠️  No staff found for property {property_obj.name} - skipping"
                        )
                    )
                    continue

                if not self.dry_run:
                    PropertyActivationFee.objects.create(
                        property_instance=property_obj,
                        user=user,
                        original_fee_amount=config.fee_amount,
                        remaining_fee_amount=config.fee_amount,
                        total_recovered_amount=Decimal("0.00"),
                        is_fully_recovered=False,
                    )
                    created_count += 1
                    properties_with_new_records.append(property_obj)
                    self.stdout.write(
                        f"  ✓ Created activation fee record for: {property_obj.name} (€{config.fee_amount})"
                    )
                else:
                    self.stdout.write(
                        f"  [DRY RUN] Would create activation fee record for: {property_obj.name} (€{config.fee_amount})"
                    )
                    created_count += 1
                    properties_with_new_records.append(property_obj)

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"  ❌ Error creating record for {property_obj.name}: {str(e)}"
                    )
                )
                continue

        if not self.dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f"  ✅ Created {created_count} property activation fee records"
                )
            )
        else:
            self.stdout.write(
                f"  [DRY RUN] Would create {created_count} property activation fee records"
            )

        # Summary of what was found
        total_heibooky = heibooky_properties.count()
        existing_records = total_heibooky - len(properties_without_activation)
        self.stdout.write(
            f"  📊 Summary: {total_heibooky} Heibooky properties total, {existing_records} already had records, {created_count} newly created"
        )

        # Return the number of created records and properties for further processing
        return created_count, properties_with_new_records

    def update_calculation_types(self):
        """Update calculation types for existing reservations."""
        self.stdout.write("Updating reservation calculation types...")

        # Find reservations without calculation type
        reservations_to_update = Reservation.objects.filter(
            calculation_type__in=["", None]
        )

        updated_count = 0

        for reservation in reservations_to_update:
            # Determine calculation type based on existing data
            is_domorent = False

            # Check if this is a Domorent reservation
            if (
                (reservation.cleaning_cost and reservation.cleaning_cost > 0)
                or (reservation.taxable_domorent and reservation.taxable_domorent > 0)
                or (
                    reservation.tot_platform_commission
                    and reservation.tot_platform_commission > 0
                )
            ):
                is_domorent = True

            # Check property if booking exists
            try:
                if (
                    hasattr(reservation, "booking")
                    and reservation.booking.property.is_domorent
                ):
                    is_domorent = True
            except:
                pass

            new_calculation_type = (
                "Domorent Property" if is_domorent else "Heibooky Property"
            )

            if not self.dry_run:
                reservation.calculation_type = new_calculation_type
                reservation.save(update_fields=["calculation_type"])
                updated_count += 1
            else:
                self.stdout.write(
                    f"  [DRY RUN] Would update {reservation.id}: {new_calculation_type}"
                )
                updated_count += 1

        self.stdout.write(
            self.style.SUCCESS(f"  ✓ Updated {updated_count} reservations")
        )

    def recalculate_activation_fees_for_properties(self, property_instances):
        """Force recalculation of activation fees for specific properties."""
        self.stdout.write("Recalculating activation fees for updated properties...")

        if not property_instances:
            self.stdout.write("  ⚠️  No properties provided for recalculation")
            return

        # Get all Heibooky reservations for these properties
        property_ids = [prop.pk for prop in property_instances]
        reservations = Reservation.objects.filter(
            booking__property__pk__in=property_ids,
            calculation_type="Heibooky Property",
            net_price__isnull=False,
            net_price__gt=0,
        ).select_related("booking__property")

        recalculated_count = 0

        for reservation in reservations:
            try:
                property_instance = reservation.booking.property
                user = property_instance.staffs.first()

                if not property_instance or not user:
                    continue

                # Always recalculate for activation fee updates
                client_price = float(reservation.net_price)
                pricing_result = calculate_heibooky_pricing(
                    client_price=client_price,
                    property_instance=property_instance,
                    user=user,
                    include_activation_fee=True,
                )

                if not self.dry_run:
                    # Update all calculated fields including activation fee
                    reservation.ota_commission = Decimal(
                        str(pricing_result["ota_commission"])
                    )
                    reservation.heibooky_commission = Decimal(
                        str(pricing_result["heibooky_commission"])
                    )
                    reservation.payment_charge = Decimal(
                        str(pricing_result["payment_charge"])
                    )
                    reservation.iva_amount = Decimal(str(pricing_result["iva_amount"]))
                    reservation.owner_tax = Decimal(str(pricing_result["owner_tax"]))
                    reservation.net_total_for_owner = Decimal(
                        str(pricing_result["net_total_for_owner"])
                    )
                    reservation.activation_fee_applied = Decimal(
                        str(pricing_result["activation_fee_applied"])
                    )

                    # Update activation fee tracking fields if available
                    if "activation_fee_remaining_before" in pricing_result:
                        reservation.activation_fee_remaining_before = Decimal(
                            str(pricing_result["activation_fee_remaining_before"])
                        )
                    if "activation_fee_remaining_after" in pricing_result:
                        reservation.activation_fee_remaining_after = Decimal(
                            str(pricing_result["activation_fee_remaining_after"])
                        )

                    reservation.save(
                        update_fields=[
                            "ota_commission",
                            "heibooky_commission",
                            "payment_charge",
                            "iva_amount",
                            "owner_tax",
                            "net_total_for_owner",
                            "activation_fee_applied",
                            "activation_fee_remaining_before",
                            "activation_fee_remaining_after",
                        ]
                    )

                    recalculated_count += 1
                    self.stdout.write(
                        f"  ✓ Recalculated reservation {reservation.id[:8]}... (€{pricing_result['activation_fee_applied']} activation fee)"
                    )
                else:
                    self.stdout.write(
                        f"  [DRY RUN] Would recalculate reservation {reservation.id[:8]}... (€{pricing_result['activation_fee_applied']} activation fee)"
                    )
                    recalculated_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"  ❌ Error recalculating reservation {reservation.id}: {str(e)}"
                    )
                )
                continue

        if not self.dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f"  ✅ Recalculated {recalculated_count} reservations with activation fees"
                )
            )
        else:
            self.stdout.write(
                f"  [DRY RUN] Would recalculate {recalculated_count} reservations with activation fees"
            )

    def recalculate_heibooky_reservations(self):
        """Recalculate financial data for Heibooky reservations."""
        self.stdout.write("Recalculating Heibooky reservation financial data...")

        # Get Heibooky reservations with pricing data
        heibooky_reservations = Reservation.objects.filter(
            calculation_type="Heibooky Property",
            net_price__isnull=False,
            net_price__gt=0,
        ).select_related("booking__property")

        recalculated_count = 0

        for reservation in heibooky_reservations:
            try:
                # Get property and user
                property_instance = None
                user = None

                if hasattr(reservation, "booking") and reservation.booking.property:
                    property_instance = reservation.booking.property
                    user = property_instance.staffs.first()

                if not property_instance or not user:
                    continue

                # Skip if already has detailed breakdown
                if (
                    reservation.ota_commission
                    and reservation.ota_commission > 0
                    and reservation.heibooky_commission
                    and reservation.heibooky_commission > 0
                ):
                    continue

                # Calculate pricing
                client_price = float(reservation.net_price)
                pricing_result = calculate_heibooky_pricing(
                    client_price=client_price,
                    property_instance=property_instance,
                    user=user,
                    include_activation_fee=True,
                )

                if not self.dry_run:
                    # Update reservation with calculated values
                    reservation.ota_commission = Decimal(
                        str(pricing_result["ota_commission"])
                    )
                    reservation.heibooky_commission = Decimal(
                        str(pricing_result["heibooky_commission"])
                    )
                    reservation.payment_charge = Decimal(
                        str(pricing_result["payment_charge"])
                    )
                    reservation.iva_amount = Decimal(str(pricing_result["iva_amount"]))
                    reservation.owner_tax = Decimal(str(pricing_result["owner_tax"]))
                    reservation.net_total_for_owner = Decimal(
                        str(pricing_result["net_total_for_owner"])
                    )
                    reservation.activation_fee_applied = Decimal(
                        str(pricing_result["activation_fee_applied"])
                    )

                    reservation.save(
                        update_fields=[
                            "ota_commission",
                            "heibooky_commission",
                            "payment_charge",
                            "iva_amount",
                            "owner_tax",
                            "net_total_for_owner",
                            "activation_fee_applied",
                        ]
                    )

                    recalculated_count += 1
                else:
                    self.stdout.write(
                        f'  [DRY RUN] Would recalculate {reservation.id}: €{client_price} -> €{pricing_result["net_total_for_owner"]}'
                    )
                    recalculated_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"  Error recalculating {reservation.id}: {str(e)}"
                    )
                )
                continue

        self.stdout.write(
            self.style.SUCCESS(f"  ✓ Recalculated {recalculated_count} reservations")
        )

    def validate_data_integrity(self):
        """Validate data integrity after updates."""
        self.stdout.write("Validating data integrity...")

        issues = []

        # Check for reservations without calculation type
        missing_calc_type = Reservation.objects.filter(
            calculation_type__in=["", None]
        ).count()

        if missing_calc_type > 0:
            issues.append(f"{missing_calc_type} reservations without calculation_type")

        # Check for Heibooky reservations without commission data
        heibooky_without_commission = Reservation.objects.filter(
            calculation_type="Heibooky Property",
            net_price__gt=0,
            ota_commission__isnull=True,
        ).count()

        if heibooky_without_commission > 0:
            issues.append(
                f"{heibooky_without_commission} Heibooky reservations without commission data"
            )

        # Check for negative owner payouts
        negative_payouts = Reservation.objects.filter(net_total_for_owner__lt=0).count()

        if negative_payouts > 0:
            issues.append(
                f"{negative_payouts} reservations with negative owner payouts"
            )

        # Check activation fee configuration
        active_configs = ActivationFeeConfig.objects.filter(is_active=True).count()

        if active_configs == 0:
            issues.append("No active activation fee configuration")
        elif active_configs > 1:
            issues.append(
                f"{active_configs} active activation fee configurations (should be 1)"
            )

        if issues:
            self.stdout.write(self.style.ERROR("  Data integrity issues found:"))
            for issue in issues:
                self.stdout.write(f"    - {issue}")
        else:
            self.stdout.write(
                self.style.SUCCESS("  ✓ Data integrity validation passed")
            )

    def display_summary(self):
        """Display summary statistics."""
        self.stdout.write("\n=== Summary Statistics ===")

        # Reservation counts by type
        heibooky_count = Reservation.objects.filter(
            calculation_type="Heibooky Property"
        ).count()
        domorent_count = Reservation.objects.filter(
            calculation_type="Domorent Property"
        ).count()

        self.stdout.write(f"Heibooky reservations: {heibooky_count}")
        self.stdout.write(f"Domorent reservations: {domorent_count}")

        # Active configuration
        config = ActivationFeeConfig.objects.filter(is_active=True).first()
        if config:
            self.stdout.write(f"Activation fee: €{config.fee_amount}")

        # Properties with activation fee tracking
        tracking_count = PropertyActivationFee.objects.count()
        self.stdout.write(f"Properties with activation fee tracking: {tracking_count}")
