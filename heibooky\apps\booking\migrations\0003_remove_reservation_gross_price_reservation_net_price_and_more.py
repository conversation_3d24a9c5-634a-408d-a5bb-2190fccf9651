# Generated by Django 5.2.4 on 2025-07-14 04:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("booking", "0002_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="reservation",
            name="gross_price",
        ),
        migrations.AddField(
            model_name="reservation",
            name="net_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Net price of the reservation.",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="reservation",
            name="total_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Total price of the reservation after commission and taxes.",
                max_digits=10,
                null=True,
            ),
        ),
    ]
