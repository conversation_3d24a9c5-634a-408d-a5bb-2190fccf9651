# Generated by Django 5.2.4 on 2025-07-19 13:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "booking",
            "0003_remove_reservation_gross_price_reservation_net_price_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="reservation",
            name="heibooky_commission",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Heibooky commission (8% for non-Domorent properties).",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="iva_amount",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="IVA amount (22% of total commissions for non-Domorent properties).",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="net_total_for_owner",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Net amount for property owner after all deductions.",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="ota_commission",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="OTA commission (15% for non-Domorent properties).",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="owner_tax",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Owner tax amount (21% of price for non-Domorent properties).",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="payment_charge",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Payment processing charge (3% for non-Domorent properties).",
                max_digits=10,
                null=True,
            ),
        ),
    ]
