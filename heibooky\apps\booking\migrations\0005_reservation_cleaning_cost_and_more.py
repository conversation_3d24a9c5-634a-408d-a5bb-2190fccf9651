# Generated by Django 5.2.4 on 2025-07-19 15:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("booking", "0004_reservation_heibooky_commission_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="reservation",
            name="cleaning_cost",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Fixed cleaning cost for Domorent properties (€80).",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="owner_net_transfer",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Final net transfer to owner for Domorent properties (TOT - Owner Tax).",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="taxable_domorent",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Taxable amount for Domorent commission (Price - Cleaning Cost).",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="tot_platform_commission",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Total platform commission for Domorent properties.",
                max_digits=10,
                null=True,
            ),
        ),
    ]
