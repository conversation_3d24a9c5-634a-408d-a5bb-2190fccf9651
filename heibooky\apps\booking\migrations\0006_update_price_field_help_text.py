# Generated by Django 5.2.4 on 2025-07-25 10:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("booking", "0005_reservation_cleaning_cost_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="reservation",
            name="net_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Net price from SU API - amount customer pays.",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="reservation",
            name="total_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Amount owner receives after all deductions (commissions, taxes, etc.).",
                max_digits=10,
                null=True,
            ),
        ),
    ]
