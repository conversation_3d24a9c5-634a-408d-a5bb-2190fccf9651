# Generated by Django 5.2.4 on 2025-08-09 12:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("booking", "0006_update_price_field_help_text"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="reservation",
            options={"ordering": ["-modified_at", "-booked_at"]},
        ),
        migrations.AddField(
            model_name="booking",
            name="chekin_reservation_id",
            field=models.CharField(
                blank=True,
                help_text="Chekin reservation ID for automatic check-in integration",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="reservation",
            name="checkin_date",
            field=models.DateField(
                blank=True, help_text="Expected arrival date", null=True
            ),
        ),
        migrations.AlterField(
            model_name="reservation",
            name="checkout_date",
            field=models.DateField(
                blank=True, help_text="Expected time of departure", null=True
            ),
        ),
    ]
