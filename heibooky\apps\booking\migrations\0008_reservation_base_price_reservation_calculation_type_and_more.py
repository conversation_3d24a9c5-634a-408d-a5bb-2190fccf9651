# Generated by Django 5.2.4 on 2025-08-20 18:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("booking", "0007_alter_reservation_options_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="reservation",
            name="base_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Base price before any deductions",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="calculation_type",
            field=models.CharField(
                blank=True,
                default="",
                help_text="Type of calculation used (Domorent Property, Heibooky Property, etc.)",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="flat_tax_21",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Flat tax 21% amount",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="net_total",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Final net total for owner after all deductions",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="payment_fee_1_5",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Payment fee (1.5% for Domorent properties)",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="subtotal_after_deductions",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Subtotal after commission and fee deductions",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="vat_22",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="VAT 22% amount",
                max_digits=10,
                null=True,
            ),
        ),
    ]
