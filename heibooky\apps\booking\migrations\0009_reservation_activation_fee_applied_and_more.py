# Generated by Django 5.2.4 on 2025-08-27 22:06

import uuid
from decimal import Decimal

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "booking",
            "0008_reservation_base_price_reservation_calculation_type_and_more",
        ),
        ("stay", "0006_property_chekin_housing_id_room_chekin_room_id"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="reservation",
            name="activation_fee_applied",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Activation fee amount deducted from this reservation (for Heibooky properties).",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="activation_fee_remaining_after",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Remaining activation fee amount after this reservation was processed.",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="reservation",
            name="activation_fee_remaining_before",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Remaining activation fee amount before this reservation was processed.",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="reservation",
            name="payment_charge",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                default=0.0,
                help_text="Payment processing charge (3.5% for non-Domorent properties).",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="ActivationFeeConfig",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "fee_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("150.00"),
                        help_text="Activation fee amount in EUR (default: €150.00)",
                        max_digits=10,
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether activation fees are currently being applied to new users",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Optional description for this activation fee configuration",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this configuration",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_activation_fees",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Activation Fee Configuration",
                "verbose_name_plural": "Activation Fee Configurations",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PromotionConfig",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the promotion (e.g., 'New User 10% Discount')",
                        max_length=100,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Detailed description of the promotion",
                        null=True,
                    ),
                ),
                (
                    "promotion_code",
                    models.CharField(
                        blank=True,
                        help_text="Optional promotion code for users to enter",
                        max_length=20,
                        null=True,
                        unique=True,
                    ),
                ),
                (
                    "promotion_type",
                    models.CharField(
                        choices=[
                            ("percentage", "Percentage Discount"),
                            ("fixed_amount", "Fixed Amount Discount"),
                            ("free_activation", "Free Activation Fee"),
                        ],
                        default="percentage",
                        help_text="Type of promotion discount",
                        max_length=20,
                    ),
                ),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Percentage discount (0-100%)",
                        max_digits=5,
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Fixed discount amount in EUR",
                        max_digits=10,
                    ),
                ),
                (
                    "target_audience",
                    models.CharField(
                        choices=[
                            ("new_users", "New Users Only"),
                            ("first_booking", "First Booking Only"),
                            ("all_users", "All Users"),
                            ("returning_users", "Returning Users"),
                        ],
                        default="new_users",
                        help_text="Who can use this promotion",
                        max_length=20,
                    ),
                ),
                (
                    "minimum_booking_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Minimum booking amount required to use promotion",
                        max_digits=10,
                    ),
                ),
                (
                    "maximum_discount_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum discount amount (cap for percentage discounts)",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "start_date",
                    models.DateField(help_text="When the promotion becomes active"),
                ),
                (
                    "end_date",
                    models.DateField(
                        blank=True,
                        help_text="When the promotion expires (leave blank for no expiry)",
                        null=True,
                    ),
                ),
                (
                    "max_uses_total",
                    models.IntegerField(
                        blank=True,
                        help_text="Maximum total uses across all users (leave blank for unlimited)",
                        null=True,
                    ),
                ),
                (
                    "max_uses_per_user",
                    models.IntegerField(default=1, help_text="Maximum uses per user"),
                ),
                (
                    "current_uses",
                    models.IntegerField(
                        default=0,
                        help_text="Current number of times this promotion has been used",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this promotion is currently active",
                    ),
                ),
                (
                    "applies_before_activation_fee",
                    models.BooleanField(
                        default=True,
                        help_text="Whether discount is applied before or after activation fee deduction",
                    ),
                ),
                (
                    "waives_activation_fee",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this promotion waives the activation fee entirely",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this promotion",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_promotions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Promotion Configuration",
                "verbose_name_plural": "Promotion Configurations",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PromotionUsage",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Actual discount amount applied",
                        max_digits=10,
                    ),
                ),
                (
                    "original_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Original booking amount before discount",
                        max_digits=10,
                    ),
                ),
                (
                    "final_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Final booking amount after discount",
                        max_digits=10,
                    ),
                ),
                ("used_at", models.DateTimeField(auto_now_add=True)),
                (
                    "promotion",
                    models.ForeignKey(
                        help_text="Promotion that was used",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="usages",
                        to="booking.promotionconfig",
                    ),
                ),
                (
                    "reservation",
                    models.ForeignKey(
                        help_text="Reservation where promotion was applied",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="promotion_usages",
                        to="booking.reservation",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User who used the promotion",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="promotion_usages",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Promotion Usage",
                "verbose_name_plural": "Promotion Usages",
                "ordering": ["-used_at"],
                "unique_together": {("promotion", "user", "reservation")},
            },
        ),
        migrations.CreateModel(
            name="PropertyActivationFee",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "original_fee_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Original activation fee amount when first applied",
                        max_digits=10,
                    ),
                ),
                (
                    "remaining_fee_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Remaining activation fee amount to be recovered",
                        max_digits=10,
                    ),
                ),
                (
                    "total_recovered_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Total amount recovered so far through reservations",
                        max_digits=10,
                    ),
                ),
                (
                    "is_fully_recovered",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the activation fee has been fully recovered",
                    ),
                ),
                (
                    "recovery_started_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the first recovery deduction was made",
                        null=True,
                    ),
                ),
                (
                    "fully_recovered_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the activation fee was fully recovered",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "property_instance",
                    models.OneToOneField(
                        help_text="Property for which activation fee is being tracked",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="activation_fee_status",
                        to="stay.property",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="User/owner associated with this property",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="property_activation_fees",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Property Activation Fee",
                "verbose_name_plural": "Property Activation Fees",
                "ordering": ["-created_at"],
                "unique_together": {("property_instance", "user")},
            },
        ),
    ]
