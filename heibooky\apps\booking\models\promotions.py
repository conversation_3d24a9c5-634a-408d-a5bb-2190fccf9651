import uuid
from decimal import Decimal

from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _

from .bookings import Booking, Reservation


class ActivationFeeConfig(models.Model):
    """
    Model to store configurable activation fee settings for Heibooky properties.

    This model allows administrators to configure activation fees that are applied
    to new users' first bookings and gradually recovered through subsequent reservations.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Configuration fields
    fee_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal("150.00"),
        help_text="Activation fee amount in EUR (default: €150.00)",
    )

    is_active = models.BooleanField(
        default=True,
        help_text="Whether activation fees are currently being applied to new users",
    )

    description = models.TextField(
        blank=True,
        null=True,
        help_text="Optional description for this activation fee configuration",
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_activation_fees",
        help_text="User who created this configuration",
    )

    class Meta:
        verbose_name = _("Activation Fee Configuration")
        verbose_name_plural = _("Activation Fee Configurations")
        ordering = ["-created_at"]

    def clean(self):
        """Validate activation fee configuration"""
        if self.fee_amount is not None and self.fee_amount < 0:
            raise ValidationError(
                {"fee_amount": _("Activation fee amount cannot be negative.")}
            )

        if self.fee_amount is not None and self.fee_amount > 10000:
            raise ValidationError(
                {"fee_amount": _("Activation fee amount cannot exceed €10,000.")}
            )

    def __str__(self):
        status = "Active" if self.is_active else "Inactive"
        return f"Activation Fee: €{self.fee_amount} ({status})"

    @classmethod
    def get_current_fee(cls):
        """Get the current active activation fee amount"""
        try:
            config = cls.objects.filter(is_active=True).first()
            return config.fee_amount if config else Decimal("0.00")
        except cls.DoesNotExist:
            return Decimal("0.00")

    @classmethod
    def is_fee_active(cls):
        """Check if activation fees are currently active"""
        return cls.objects.filter(is_active=True).exists()


class PropertyActivationFee(models.Model):
    """
    Model to track activation fee status and recovery for individual properties.

    This model tracks the activation fee recovery progress for each property,
    ensuring that fees are gradually deducted without causing negative owner payouts.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Relationships
    property_instance = models.OneToOneField(
        "stay.Property",
        on_delete=models.CASCADE,
        related_name="activation_fee_status",
        help_text="Property for which activation fee is being tracked",
    )

    user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="property_activation_fees",
        help_text="User/owner associated with this property",
    )

    # Fee tracking fields
    original_fee_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Original activation fee amount when first applied",
    )

    remaining_fee_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Remaining activation fee amount to be recovered",
    )

    total_recovered_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal("0.00"),
        help_text="Total amount recovered so far through reservations",
    )

    # Status tracking
    is_fully_recovered = models.BooleanField(
        default=False, help_text="Whether the activation fee has been fully recovered"
    )

    recovery_started_at = models.DateTimeField(
        null=True, blank=True, help_text="When the first recovery deduction was made"
    )

    fully_recovered_at = models.DateTimeField(
        null=True, blank=True, help_text="When the activation fee was fully recovered"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Property Activation Fee")
        verbose_name_plural = _("Property Activation Fees")
        ordering = ["-created_at"]
        unique_together = ["property_instance", "user"]

    def clean(self):
        """Validate activation fee tracking data"""
        if self.original_fee_amount is not None and self.original_fee_amount < 0:
            raise ValidationError(
                {"original_fee_amount": _("Original fee amount cannot be negative.")}
            )

        if self.remaining_fee_amount is not None and self.remaining_fee_amount < 0:
            raise ValidationError(
                {"remaining_fee_amount": _("Remaining fee amount cannot be negative.")}
            )

        if (
            self.total_recovered_amount is not None
            and self.original_fee_amount is not None
            and self.total_recovered_amount > self.original_fee_amount
        ):
            raise ValidationError(
                {
                    "total_recovered_amount": _(
                        "Total recovered amount cannot exceed original fee amount."
                    )
                }
            )

    def __str__(self):
        return f"Activation Fee for {self.property_instance.name} - €{self.remaining_fee_amount} remaining"

    def calculate_recovery_amount(self, available_payout):
        """
        Calculate how much activation fee can be recovered from the given payout
        without making the owner payout negative.

        Args:
            available_payout (Decimal): The owner's payout amount before activation fee deduction

        Returns:
            Decimal: Amount that can be recovered (0 if payout would go negative)
        """
        if self.is_fully_recovered or available_payout <= 0:
            return Decimal("0.00")

        # Don't recover more than what's remaining
        max_recoverable = min(self.remaining_fee_amount, available_payout)

        # Ensure we don't make the payout negative
        return max(Decimal("0.00"), max_recoverable)

    def apply_recovery(self, recovery_amount):
        """
        Apply a recovery amount to the activation fee tracking.

        Args:
            recovery_amount (Decimal): Amount being recovered from this transaction
        """
        if recovery_amount <= 0:
            return

        # Cap recovery amount to not exceed remaining fee amount
        actual_recovery = min(recovery_amount, self.remaining_fee_amount)

        # Update recovery tracking
        if not self.recovery_started_at:
            from django.utils import timezone

            self.recovery_started_at = timezone.now()

        self.total_recovered_amount += actual_recovery
        self.remaining_fee_amount -= actual_recovery

        # Check if fully recovered
        if self.remaining_fee_amount <= 0:
            self.remaining_fee_amount = Decimal("0.00")
            self.is_fully_recovered = True
            if not self.fully_recovered_at:
                from django.utils import timezone

                self.fully_recovered_at = timezone.now()

        self.save()

    @property
    def recovery_percentage(self):
        """Calculate the percentage of activation fee recovered"""
        if self.original_fee_amount <= 0:
            return 100.0
        return float((self.total_recovered_amount / self.original_fee_amount) * 100)


class PromotionConfig(models.Model):
    """
    Model to store configurable promotional discount settings for new users.

    This model allows administrators to configure percentage-based promotions
    that can be applied to new users' bookings.
    """

    class PromotionType(models.TextChoices):
        PERCENTAGE = "percentage", _("Percentage Discount")
        FIXED_AMOUNT = "fixed_amount", _("Fixed Amount Discount")
        FREE_ACTIVATION = "free_activation", _("Free Activation Fee")

    class TargetAudience(models.TextChoices):
        NEW_USERS = "new_users", _("New Users Only")
        FIRST_BOOKING = "first_booking", _("First Booking Only")
        ALL_USERS = "all_users", _("All Users")
        RETURNING_USERS = "returning_users", _("Returning Users")

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Basic promotion information
    name = models.CharField(
        max_length=100,
        help_text="Name of the promotion (e.g., 'New User 10% Discount')",
    )

    description = models.TextField(
        blank=True, null=True, help_text="Detailed description of the promotion"
    )

    promotion_code = models.CharField(
        max_length=20,
        unique=True,
        blank=True,
        null=True,
        help_text="Optional promotion code for users to enter",
    )

    # Promotion configuration
    promotion_type = models.CharField(
        max_length=20,
        choices=PromotionType.choices,
        default=PromotionType.PERCENTAGE,
        help_text="Type of promotion discount",
    )

    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal("0.00"),
        help_text="Percentage discount (0-100%)",
    )

    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal("0.00"),
        help_text="Fixed discount amount in EUR",
    )

    # Target audience and conditions
    target_audience = models.CharField(
        max_length=20,
        choices=TargetAudience.choices,
        default=TargetAudience.NEW_USERS,
        help_text="Who can use this promotion",
    )

    minimum_booking_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal("0.00"),
        help_text="Minimum booking amount required to use promotion",
    )

    maximum_discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Maximum discount amount (cap for percentage discounts)",
    )

    # Validity period
    start_date = models.DateField(help_text="When the promotion becomes active")

    end_date = models.DateField(
        blank=True,
        null=True,
        help_text="When the promotion expires (leave blank for no expiry)",
    )

    # Usage limits
    max_uses_total = models.IntegerField(
        blank=True,
        null=True,
        help_text="Maximum total uses across all users (leave blank for unlimited)",
    )

    max_uses_per_user = models.IntegerField(
        default=1, help_text="Maximum uses per user"
    )

    current_uses = models.IntegerField(
        default=0, help_text="Current number of times this promotion has been used"
    )

    # Status and interaction with activation fees
    is_active = models.BooleanField(
        default=True, help_text="Whether this promotion is currently active"
    )

    applies_before_activation_fee = models.BooleanField(
        default=True,
        help_text="Whether discount is applied before or after activation fee deduction",
    )

    waives_activation_fee = models.BooleanField(
        default=False,
        help_text="Whether this promotion waives the activation fee entirely",
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_promotions",
        help_text="User who created this promotion",
    )

    class Meta:
        verbose_name = _("Promotion Configuration")
        verbose_name_plural = _("Promotion Configurations")
        ordering = ["-created_at"]

    def clean(self):
        """Validate promotion configuration"""
        errors = {}

        # Validate discount values based on promotion type
        if self.promotion_type == self.PromotionType.PERCENTAGE:
            if self.discount_percentage < 0 or self.discount_percentage > 100:
                errors["discount_percentage"] = _(
                    "Percentage must be between 0 and 100."
                )
        elif self.promotion_type == self.PromotionType.FIXED_AMOUNT:
            if self.discount_amount <= 0:
                errors["discount_amount"] = _(
                    "Fixed discount amount must be greater than 0."
                )

        # Validate date range
        if self.end_date and self.start_date and self.end_date <= self.start_date:
            errors["end_date"] = _("End date must be after start date.")

        # Validate usage limits
        if self.max_uses_total is not None and self.max_uses_total <= 0:
            errors["max_uses_total"] = _("Maximum total uses must be greater than 0.")

        if self.max_uses_per_user <= 0:
            errors["max_uses_per_user"] = _(
                "Maximum uses per user must be greater than 0."
            )

        # Validate minimum booking amount
        if self.minimum_booking_amount < 0:
            errors["minimum_booking_amount"] = _(
                "Minimum booking amount cannot be negative."
            )

        if errors:
            raise ValidationError(errors)

    def __str__(self):
        return f"{self.name} ({self.get_promotion_type_display()})"

    def is_valid_now(self):
        """Check if promotion is currently valid"""
        if not self.is_active:
            return False

        from datetime import date

        today = date.today()
        if today < self.start_date:
            return False

        if self.end_date and today > self.end_date:
            return False

        if self.max_uses_total and self.current_uses >= self.max_uses_total:
            return False

        return True

    def calculate_discount(self, booking_amount):
        """
        Calculate discount amount for a given booking amount.

        Args:
            booking_amount (Decimal): The booking amount to calculate discount for

        Returns:
            Decimal: The discount amount
        """
        if not self.is_valid_now():
            return Decimal("0.00")

        if booking_amount < self.minimum_booking_amount:
            return Decimal("0.00")

        if self.promotion_type == self.PromotionType.PERCENTAGE:
            discount = booking_amount * (self.discount_percentage / Decimal("100"))

            # Apply maximum discount cap if set
            if self.maximum_discount_amount:
                discount = min(discount, self.maximum_discount_amount)

            return discount

        elif self.promotion_type == self.PromotionType.FIXED_AMOUNT:
            return min(self.discount_amount, booking_amount)

        return Decimal("0.00")

    def can_user_use_promotion(self, user):
        """
        Check if a specific user can use this promotion.

        Args:
            user: User instance

        Returns:
            bool: Whether user can use this promotion
        """
        if not self.is_valid_now():
            return False

        # Check user usage limit
        user_usage_count = PromotionUsage.objects.filter(
            promotion=self, user=user
        ).count()

        if user_usage_count >= self.max_uses_per_user:
            return False

        # Check target audience
        if self.target_audience == self.TargetAudience.NEW_USERS:
            # Check if user is new (has no completed bookings)
            completed_bookings = Booking.objects.filter(
                property__staffs=user, status=Booking.Status.COMPLETED
            ).count()
            return completed_bookings == 0

        elif self.target_audience == self.TargetAudience.FIRST_BOOKING:
            # Check if this would be user's first booking
            any_bookings = Booking.objects.filter(property__staffs=user).count()
            return any_bookings == 0

        # ALL_USERS and RETURNING_USERS are always allowed for now
        return True


class PromotionUsage(models.Model):
    """
    Model to track promotion usage by users.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    promotion = models.ForeignKey(
        PromotionConfig,
        on_delete=models.CASCADE,
        related_name="usages",
        help_text="Promotion that was used",
    )

    user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="promotion_usages",
        help_text="User who used the promotion",
    )

    reservation = models.ForeignKey(
        Reservation,
        on_delete=models.CASCADE,
        related_name="promotion_usages",
        help_text="Reservation where promotion was applied",
    )

    discount_amount = models.DecimalField(
        max_digits=10, decimal_places=2, help_text="Actual discount amount applied"
    )

    original_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Original booking amount before discount",
    )

    final_amount = models.DecimalField(
        max_digits=10, decimal_places=2, help_text="Final booking amount after discount"
    )

    used_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Promotion Usage")
        verbose_name_plural = _("Promotion Usages")
        ordering = ["-used_at"]
        unique_together = ["promotion", "user", "reservation"]

    def __str__(self):
        return (
            f"{self.promotion.name} used by {self.user.email} - €{self.discount_amount}"
        )
