"""
Unit tests for activation fee functionality.
"""

from decimal import Decimal

from apps.booking.models import ActivationFeeConfig, PropertyActivationFee
from apps.stay.models import Location, Property
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.test import TestCase

User = get_user_model()


class ActivationFeeConfigTestCase(TestCase):
    """Test cases for ActivationFeeConfig model."""

    def setUp(self):
        self.user = User.objects.create_user(
            name="Test User",
            email="<EMAIL>",
            password="testpass123",
        )

    def test_create_activation_fee_config(self):
        """Test creating an activation fee configuration."""
        config = ActivationFeeConfig.objects.create(
            fee_amount=Decimal("150.00"),
            is_active=True,
            description="Default activation fee",
            created_by=self.user,
        )

        self.assertEqual(config.fee_amount, Decimal("150.00"))
        self.assertTrue(config.is_active)
        self.assertEqual(config.created_by, self.user)
        self.assertEqual(str(config), "Activation Fee: €150.00 (Active)")

    def test_get_current_fee(self):
        """Test getting the current active fee amount."""
        # No active config
        self.assertEqual(ActivationFeeConfig.get_current_fee(), Decimal("0.00"))

        # Create inactive config
        ActivationFeeConfig.objects.create(
            fee_amount=Decimal("100.00"), is_active=False, created_by=self.user
        )
        self.assertEqual(ActivationFeeConfig.get_current_fee(), Decimal("0.00"))

        # Create active config
        ActivationFeeConfig.objects.create(
            fee_amount=Decimal("150.00"), is_active=True, created_by=self.user
        )
        self.assertEqual(ActivationFeeConfig.get_current_fee(), Decimal("150.00"))

    def test_is_fee_active(self):
        """Test checking if activation fees are active."""
        self.assertFalse(ActivationFeeConfig.is_fee_active())

        ActivationFeeConfig.objects.create(
            fee_amount=Decimal("150.00"), is_active=True, created_by=self.user
        )
        self.assertTrue(ActivationFeeConfig.is_fee_active())

    def test_validation_negative_fee(self):
        """Test validation prevents negative fee amounts."""
        config = ActivationFeeConfig(
            fee_amount=Decimal("-10.00"), is_active=True, created_by=self.user
        )

        with self.assertRaises(ValidationError):
            config.clean()

    def test_validation_excessive_fee(self):
        """Test validation prevents excessive fee amounts."""
        config = ActivationFeeConfig(
            fee_amount=Decimal("15000.00"), is_active=True, created_by=self.user
        )

        with self.assertRaises(ValidationError):
            config.clean()


class PropertyActivationFeeTestCase(TestCase):
    """Test cases for PropertyActivationFee model."""

    def setUp(self):
        self.user = User.objects.create_user(
            name="Test User", email="<EMAIL>", password="testpass123"
        )
        # Create a test location
        self.location = Location.objects.create(
            street="123 Test Street",
            post_code="12345",
            city="Test City",
            country="Test Country",
            latitude=45.0,
            longitude=9.0,
        )
        self.property = Property.objects.create(
            name="Test Property", is_domorent=False, location=self.location
        )
        self.property.staffs.add(self.user)

    def test_create_property_activation_fee(self):
        """Test creating a property activation fee tracking record."""
        fee_tracking = PropertyActivationFee.objects.create(
            property_instance=self.property,
            user=self.user,
            original_fee_amount=Decimal("150.00"),
            remaining_fee_amount=Decimal("150.00"),
        )

        self.assertEqual(fee_tracking.original_fee_amount, Decimal("150.00"))
        self.assertEqual(fee_tracking.remaining_fee_amount, Decimal("150.00"))
        self.assertEqual(fee_tracking.total_recovered_amount, Decimal("0.00"))
        self.assertFalse(fee_tracking.is_fully_recovered)
        self.assertEqual(fee_tracking.recovery_percentage, 0.0)

    def test_calculate_recovery_amount(self):
        """Test calculating recovery amount without making payout negative."""
        fee_tracking = PropertyActivationFee.objects.create(
            property_instance=self.property,
            user=self.user,
            original_fee_amount=Decimal("150.00"),
            remaining_fee_amount=Decimal("150.00"),
        )

        # Test with sufficient payout
        recovery = fee_tracking.calculate_recovery_amount(Decimal("200.00"))
        self.assertEqual(recovery, Decimal("150.00"))

        # Test with insufficient payout
        recovery = fee_tracking.calculate_recovery_amount(Decimal("50.00"))
        self.assertEqual(recovery, Decimal("50.00"))

        # Test with zero payout
        recovery = fee_tracking.calculate_recovery_amount(Decimal("0.00"))
        self.assertEqual(recovery, Decimal("0.00"))

        # Test with negative payout
        recovery = fee_tracking.calculate_recovery_amount(Decimal("-10.00"))
        self.assertEqual(recovery, Decimal("0.00"))

    def test_apply_recovery(self):
        """Test applying recovery amount to activation fee tracking."""
        fee_tracking = PropertyActivationFee.objects.create(
            property_instance=self.property,
            user=self.user,
            original_fee_amount=Decimal("150.00"),
            remaining_fee_amount=Decimal("150.00"),
        )

        # Apply partial recovery
        fee_tracking.apply_recovery(Decimal("50.00"))
        fee_tracking.refresh_from_db()

        self.assertEqual(fee_tracking.total_recovered_amount, Decimal("50.00"))
        self.assertEqual(fee_tracking.remaining_fee_amount, Decimal("100.00"))
        self.assertFalse(fee_tracking.is_fully_recovered)
        self.assertIsNotNone(fee_tracking.recovery_started_at)
        self.assertIsNone(fee_tracking.fully_recovered_at)
        self.assertAlmostEqual(fee_tracking.recovery_percentage, 33.33, places=1)

        # Apply full recovery
        fee_tracking.apply_recovery(Decimal("100.00"))
        fee_tracking.refresh_from_db()

        self.assertEqual(fee_tracking.total_recovered_amount, Decimal("150.00"))
        self.assertEqual(fee_tracking.remaining_fee_amount, Decimal("0.00"))
        self.assertTrue(fee_tracking.is_fully_recovered)
        self.assertIsNotNone(fee_tracking.fully_recovered_at)
        self.assertEqual(fee_tracking.recovery_percentage, 100.0)

    def test_apply_recovery_over_amount(self):
        """Test applying recovery that exceeds remaining amount."""
        fee_tracking = PropertyActivationFee.objects.create(
            property_instance=self.property,
            user=self.user,
            original_fee_amount=Decimal("150.00"),
            remaining_fee_amount=Decimal("50.00"),
            total_recovered_amount=Decimal("100.00"),
        )

        # Apply recovery that exceeds remaining amount
        fee_tracking.apply_recovery(Decimal("100.00"))
        fee_tracking.refresh_from_db()

        # Should be capped at remaining amount
        # Should be capped at remaining amount
        self.assertEqual(fee_tracking.total_recovered_amount, Decimal("150.00"))
        self.assertEqual(fee_tracking.remaining_fee_amount, Decimal("0.00"))
        self.assertTrue(fee_tracking.is_fully_recovered)

    def test_validation_negative_amounts(self):
        """Test validation prevents negative amounts."""
        fee_tracking = PropertyActivationFee(
            property_instance=self.property,
            user=self.user,
            original_fee_amount=Decimal("-10.00"),
            remaining_fee_amount=Decimal("150.00"),
        )

        with self.assertRaises(ValidationError):
            fee_tracking.clean()

    def test_validation_recovered_exceeds_original(self):
        """Test validation prevents recovered amount exceeding original."""
        fee_tracking = PropertyActivationFee(
            property_instance=self.property,
            user=self.user,
            original_fee_amount=Decimal("150.00"),
            remaining_fee_amount=Decimal("50.00"),
            total_recovered_amount=Decimal("200.00"),
        )

        with self.assertRaises(ValidationError):
            fee_tracking.clean()

    def test_unique_constraint(self):
        """Test unique constraint on property and user combination."""
        PropertyActivationFee.objects.create(
            property_instance=self.property,
            user=self.user,
            original_fee_amount=Decimal("150.00"),
            remaining_fee_amount=Decimal("150.00"),
        )

        # Attempting to create another record for same property and user should fail
        with self.assertRaises(Exception):  # IntegrityError
            PropertyActivationFee.objects.create(
                property_instance=self.property,
                user=self.user,
                original_fee_amount=Decimal("100.00"),
                remaining_fee_amount=Decimal("100.00"),
            )
