"""
Comprehensive unit tests for Heibooky pricing calculations and financial APIs.

This module tests the pricing calculation functions, activation fee management,
and financial API endpoints to ensure accuracy and reliability.
"""

import uuid
from datetime import date, timedelta
from decimal import Decimal

from apps.booking.api import calculate_heibooky_pricing
from apps.booking.models import (
    ActivationFeeConfig,
    Booking,
    Customer,
    PromotionConfig,
    PromotionUsage,
    PropertyActivationFee,
    Reservation,
)
from apps.booking.utils.payment_status import (
    ActivationFeeManager,
    FinancialCalculator,
    PaymentCycleManager,
)
from apps.stay.models import Location, Property
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

User = get_user_model()


class HeibookyPricingCalculationTests(TestCase):
    """Test cases for Heibooky pricing calculations."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

        # Create location and property
        self.location = Location.objects.create(
            city="Test City", state="Test State", country="Italy"
        )

        self.property = Property.objects.create(
            name="Test Property",
            property_type=Property.HOTEL,
            location=self.location,
            is_domorent=False,  # Heibooky property
        )
        self.property.staffs.add(self.user)

        # Create activation fee config
        self.activation_fee_config = ActivationFeeConfig.objects.create(
            fee_amount=Decimal("150.00"), is_active=True, created_by=self.user
        )

    def test_basic_heibooky_pricing_calculation(self):
        """Test basic pricing calculation with €1000 client price."""
        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # Test exact values from the Heibooky Financial Summary image
        self.assertEqual(result["client_price"], 1000.00)
        self.assertEqual(result["ota_commission"], 150.00)  # 15% of 1000
        self.assertEqual(result["heibooky_commission"], 80.00)  # 8% of 1000
        self.assertEqual(result["payment_fee_3_5"], 35.00)  # 3.5% of 1000
        self.assertEqual(result["vat_22"], 50.60)  # 22% of (150 + 80)
        self.assertEqual(result["substitute_tax_21"], 210.00)  # 21% of 1000
        self.assertEqual(result["calculation_type"], "Heibooky Property")

        # Calculate expected total owner payout
        # Client Price - (OTA Commission + Heibooky Commission + Payment Fee + VAT + Substitute Tax + Activation Fee)
        expected_deductions = 150.00 + 80.00 + 35.00 + 50.60 + 210.00  # = 525.60
        expected_before_activation = 1000.00 - 525.60  # = 474.40
        expected_activation_fee_applied = min(
            150.00, 474.40
        )  # = 150.00 (can deduct full amount)
        expected_final_owner = 474.40 - 150.00  # = 324.40

        self.assertAlmostEqual(
            float(result["total_owner"]), expected_final_owner, places=2
        )
        self.assertAlmostEqual(
            float(result["activation_fee"]), expected_activation_fee_applied, places=2
        )

    def test_pricing_calculation_without_activation_fee(self):
        """Test pricing calculation when activation fee is excluded."""
        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=False,
        )

        # Without activation fee, owner should get more
        expected_deductions = 150.00 + 80.00 + 35.00 + 50.60 + 210.00  # = 525.60
        expected_owner_payout = 1000.00 - 525.60  # = 474.40

        self.assertEqual(result["activation_fee"], 0.00)
        self.assertAlmostEqual(
            float(result["total_owner"]), expected_owner_payout, places=2
        )

    def test_pricing_calculation_with_small_amount(self):
        """Test pricing calculation where activation fee would exceed owner payout."""
        client_price = 100.00  # Small amount

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # Calculate expected values
        ota_commission = 100.00 * 0.15  # 15.00
        heibooky_commission = 100.00 * 0.08  # 8.00
        payment_fee = 100.00 * 0.035  # 3.50
        vat = (ota_commission + heibooky_commission) * 0.22  # (15 + 8) * 0.22 = 5.06
        substitute_tax = 100.00 * 0.21  # 21.00

        total_deductions = (
            ota_commission + heibooky_commission + payment_fee + vat + substitute_tax
        )
        owner_before_activation = 100.00 - total_deductions

        # Activation fee should be limited to available payout (no negative owner payout)
        expected_activation_fee = max(0, min(150.00, owner_before_activation))
        expected_final_owner = max(0, owner_before_activation - expected_activation_fee)

        self.assertAlmostEqual(
            float(result["activation_fee"]), expected_activation_fee, places=2
        )
        self.assertAlmostEqual(
            float(result["total_owner"]), expected_final_owner, places=2
        )
        self.assertGreaterEqual(float(result["total_owner"]), 0.00)  # Never negative

    def test_activation_fee_partial_recovery(self):
        """Test gradual activation fee recovery across multiple reservations."""
        # Create activation fee tracking
        activation_fee_status = PropertyActivationFee.objects.create(
            property_instance=self.property,
            user=self.user,
            original_fee_amount=Decimal("150.00"),
            remaining_fee_amount=Decimal("100.00"),  # Already recovered 50
            total_recovered_amount=Decimal("50.00"),
        )

        client_price = 500.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # Should only deduct remaining 100.00
        self.assertLessEqual(float(result["activation_fee"]), 100.00)
        self.assertGreaterEqual(float(result["total_owner"]), 0.00)

    def test_pricing_with_promotion_code(self):
        """Test pricing calculation with promotional discount."""
        # Create a promotion
        promotion = PromotionConfig.objects.create(
            name="New User 10% Discount",
            promotion_code="NEWUSER10",
            promotion_type=PromotionConfig.PromotionType.PERCENTAGE,
            discount_percentage=Decimal("10.00"),
            target_audience=PromotionConfig.TargetAudience.NEW_USERS,
            start_date=date.today() - timedelta(days=1),
            is_active=True,
            created_by=self.user,
        )

        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
            promotion_code="NEWUSER10",
        )

        # Should show both original and discounted prices
        self.assertEqual(result["client_price"], 1000.00)  # Original price
        self.assertEqual(result["promotion_discount"], 100.00)  # 10% discount
        self.assertEqual(result["final_client_price"], 900.00)  # Price after discount
        self.assertIsNotNone(result["promotion_applied"])

    def test_breakdown_calculation_accuracy(self):
        """Test that breakdown calculations are mathematically accurate."""
        client_price = 1234.56  # Random amount

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        breakdown = result["breakdown"]

        # Verify individual calculations
        self.assertAlmostEqual(
            breakdown["ota_commission_15_percent"], client_price * 0.15, places=2
        )
        self.assertAlmostEqual(
            breakdown["heibooky_commission_8_percent"], client_price * 0.08, places=2
        )
        self.assertAlmostEqual(
            breakdown["payment_fee_3_5_percent"], client_price * 0.035, places=2
        )

        # Verify VAT calculation (22% of OTA + Heibooky commissions)
        commission_base = (client_price * 0.15) + (client_price * 0.08)
        expected_vat = commission_base * 0.22
        self.assertAlmostEqual(
            breakdown["vat_22_percent_on_commissions"], expected_vat, places=2
        )

        # Verify substitute tax (21% of client price)
        self.assertAlmostEqual(
            breakdown["substitute_tax_21_percent"], client_price * 0.21, places=2
        )


class PaymentCycleTests(TestCase):
    """Test cases for payment cycle calculations."""

    def test_current_payment_cycle_first_half(self):
        """Test payment cycle calculation for first half of month."""
        # Test date in first half of month
        test_date = date(2024, 1, 10)
        cycle_start, cycle_end = PaymentCycleManager.get_current_payment_cycle(
            test_date
        )

        self.assertEqual(cycle_start, date(2024, 1, 1))
        self.assertEqual(cycle_end, date(2024, 1, 15))

    def test_current_payment_cycle_second_half(self):
        """Test payment cycle calculation for second half of month."""
        # Test date in second half of month
        test_date = date(2024, 1, 20)
        cycle_start, cycle_end = PaymentCycleManager.get_current_payment_cycle(
            test_date
        )

        self.assertEqual(cycle_start, date(2024, 1, 16))
        self.assertEqual(cycle_end, date(2024, 1, 31))

    def test_payment_status_determination(self):
        """Test payment status determination logic."""
        # Test payment in progress (within current cycle)
        today = date.today()
        if today.day <= 15:
            test_checkout = date(today.year, today.month, 10)
        else:
            test_checkout = date(today.year, today.month, 20)

        status = PaymentCycleManager.determine_payment_status(test_checkout)
        self.assertEqual(status, "payment_in_progress")

        # Test future payment (outside current cycle)
        if today.day <= 15:
            future_checkout = date(today.year, today.month, 20)
        else:
            # Next month
            if today.month == 12:
                future_checkout = date(today.year + 1, 1, 5)
            else:
                future_checkout = date(today.year, today.month + 1, 5)

        status = PaymentCycleManager.determine_payment_status(future_checkout)
        self.assertEqual(status, "future_payment")


class FinancialAPITests(APITestCase):
    """Test cases for financial API endpoints."""

    def setUp(self):
        """Set up test data for API tests."""
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

        # Create location and property
        self.location = Location.objects.create(
            city="Test City", state="Test State", country="Italy"
        )

        self.property = Property.objects.create(
            name="Test Property",
            property_type=Property.HOTEL,
            location=self.location,
            is_domorent=False,
        )
        self.property.staffs.add(self.user)

        # Create customer
        self.customer = Customer.objects.create(
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            telephone="+1234567890",
        )

        # Create reservations with different checkout dates
        self.create_test_reservations()

        self.client.force_authenticate(user=self.user)

    def create_test_reservations(self):
        """Create test reservations for financial calculations."""
        # Recent reservation (last 15 days)
        recent_reservation = Reservation.objects.create(
            id="RES001",
            guest_name="John Doe",
            checkin_date=date.today() - timedelta(days=10),
            checkout_date=date.today() - timedelta(days=8),
            net_price=Decimal("1000.00"),
            total_price=Decimal("850.00"),
            net_total_for_owner=Decimal("283.70"),  # From example calculation
            calculation_type="Heibooky Property",
            ota_commission=Decimal("150.00"),
            heibooky_commission=Decimal("80.00"),
            payment_charge=Decimal("35.00"),
            iva_amount=Decimal("50.60"),
            owner_tax=Decimal("210.00"),
            activation_fee_applied=Decimal("150.00"),
        )

        recent_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=recent_reservation,
            checkin_date=recent_reservation.checkin_date,
            checkout_date=recent_reservation.checkout_date,
            status=Booking.Status.COMPLETED,
        )

        # Older reservation (more than 15 days ago)
        old_reservation = Reservation.objects.create(
            id="RES002",
            guest_name="Jane Smith",
            checkin_date=date.today() - timedelta(days=30),
            checkout_date=date.today() - timedelta(days=28),
            net_price=Decimal("500.00"),
            total_price=Decimal("425.00"),
            net_total_for_owner=Decimal("200.00"),
            calculation_type="Heibooky Property",
        )

        old_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=old_reservation,
            checkin_date=old_reservation.checkin_date,
            checkout_date=old_reservation.checkout_date,
            status=Booking.Status.COMPLETED,
        )

    def test_pending_balance_api(self):
        """Test pending balance API endpoint."""
        url = reverse("pending-balance")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("pending_balance", response.data)
        self.assertEqual(response.data["currency"], "EUR")

        # Should only include recent reservation (last 15 days)
        pending_balance = Decimal(response.data["pending_balance"])
        self.assertEqual(pending_balance, Decimal("283.70"))

    def test_total_earnings_api(self):
        """Test total earnings API endpoint."""
        url = reverse("total-earnings")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("total_earnings", response.data)

        # Should include both reservations
        total_earnings = Decimal(response.data["total_earnings"])
        expected_total = Decimal("283.70") + Decimal("200.00")  # Both reservations
        self.assertEqual(total_earnings, expected_total)

    def test_financial_summary_api(self):
        """Test comprehensive financial summary API endpoint."""
        url = reverse("financial-summary")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("pending_balance", response.data)
        self.assertIn("total_earnings", response.data)
        self.assertIn("commission_breakdown", response.data)
        self.assertIn("next_payment_info", response.data)

    def test_activation_fee_status_api(self):
        """Test activation fee status API endpoint."""
        url = reverse("activation-fee-status")
        response = self.client.get(url, {"property_id": str(self.property.id)})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("activation_fee_active", response.data)

    def test_commission_breakdown_api(self):
        """Test commission breakdown API endpoint."""
        url = reverse("commission-breakdown")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("breakdown", response.data)
        self.assertEqual(response.data["currency"], "EUR")

    def test_payment_cycle_info_api(self):
        """Test payment cycle information API endpoint."""
        url = reverse("payment-cycle-info")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("current_cycle", response.data)
        self.assertIn("next_cycle", response.data)
        self.assertIn("days_until_next_payment", response.data)


class ActivationFeeManagerTests(TestCase):
    """Test cases for activation fee management."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

        self.location = Location.objects.create(
            city="Test City", state="Test State", country="Italy"
        )

        self.property = Property.objects.create(
            name="Test Property",
            property_type=Property.HOTEL,
            location=self.location,
            is_domorent=False,
        )
        self.property.staffs.add(self.user)

        # Create activation fee config
        ActivationFeeConfig.objects.create(
            fee_amount=Decimal("150.00"), is_active=True, created_by=self.user
        )

    def test_activation_fee_status_new_property(self):
        """Test activation fee status for new property."""
        status_info = ActivationFeeManager.get_activation_fee_status(
            self.property, self.user
        )

        self.assertTrue(status_info["activation_fee_active"])
        self.assertEqual(status_info["remaining_fee_amount"], "150.00")
        self.assertEqual(status_info["total_recovered_amount"], "0.00")
        self.assertFalse(status_info["is_fully_recovered"])

    def test_activation_fee_recovery_process(self):
        """Test activation fee recovery process."""
        # Create reservation for testing
        reservation = Reservation.objects.create(
            id="TEST001",
            guest_name="Test Guest",
            checkin_date=date.today(),
            checkout_date=date.today() + timedelta(days=1),
            net_price=Decimal("1000.00"),
            net_total_for_owner=Decimal("400.00"),
        )

        # Process recovery
        recovered_amount = ActivationFeeManager.process_activation_fee_recovery(
            reservation, self.property, self.user
        )

        # Should recover some amount
        self.assertGreater(recovered_amount, Decimal("0.00"))
        self.assertLessEqual(recovered_amount, Decimal("150.00"))

        # Check that tracking was created
        tracking = PropertyActivationFee.objects.get(
            property_instance=self.property, user=self.user
        )
        self.assertGreater(tracking.total_recovered_amount, Decimal("0.00"))


if __name__ == "__main__":
    import django

    django.setup()

    import unittest

    from django.test import TextTestRunner

    # Create test suite
    test_suite = unittest.TestSuite()

    # Add test cases
    test_suite.addTest(
        unittest.TestLoader().loadTestsFromTestCase(HeibookyPricingCalculationTests)
    )
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(PaymentCycleTests))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(FinancialAPITests))
    test_suite.addTest(
        unittest.TestLoader().loadTestsFromTestCase(ActivationFeeManagerTests)
    )

    # Run tests
    runner = TextTestRunner(verbosity=2)
    runner.run(test_suite)
