"""
Unit tests for financial API endpoints.
"""

import uuid
from datetime import date, timedelta
from decimal import Decimal

from apps.booking.models import Booking, Customer, Reservation
from apps.stay.models import Location, Property
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

User = get_user_model()


class FinancialAPITestCase(TestCase):
    """Base test case for financial API endpoints."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            name="Test User", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

        # Create a test location
        self.location = Location.objects.create(
            street="123 Test Street",
            post_code="12345",
            city="Test City",
            country="Test Country",
            latitude=45.0,
            longitude=9.0,
        )
        self.property = Property.objects.create(
            name="Test Property", is_domorent=False, location=self.location
        )
        self.property.staffs.add(self.user)

        # Create test bookings and reservations
        self.create_test_reservations()

    def create_test_reservations(self):
        """Create test reservations for financial calculations."""
        # Create a test customer
        self.customer = Customer.objects.create(
            first_name="Test",
            last_name="Customer",
            email="<EMAIL>",
            telephone="+1234567890",
        )

        # Recent completed reservation (within 15 days)
        self.recent_reservation = Reservation.objects.create(
            id=f"recent_{uuid.uuid4().hex[:10]}",
            guest_name="Recent Guest",
            checkout_date=date.today() - timedelta(days=5),
            net_total_for_owner=Decimal("250.00"),
        )

        recent_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=self.recent_reservation,
            status=Booking.Status.COMPLETED,
            checkin_date=date.today() - timedelta(days=7),
            checkout_date=date.today() - timedelta(days=5),
        )

        # Old completed reservation (more than 15 days ago)
        self.old_reservation = Reservation.objects.create(
            id=f"old_{uuid.uuid4().hex[:10]}",
            guest_name="Old Guest",
            checkout_date=date.today() - timedelta(days=30),
            net_total_for_owner=Decimal("300.00"),
        )

        old_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=self.old_reservation,
            status=Booking.Status.COMPLETED,
            checkin_date=date.today() - timedelta(days=32),
            checkout_date=date.today() - timedelta(days=30),
        )


class PendingBalanceAPITestCase(FinancialAPITestCase):
    """Test cases for PendingBalanceAPIView."""

    def test_get_pending_balance(self):
        """Test getting pending balance."""
        url = reverse("booking_financial:pending-balance")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertIn("pending_balance", data)
        self.assertIn("currency", data)
        self.assertIn("calculation_period", data)
        self.assertIn("last_updated", data)
        self.assertIn("reservations_count", data)

        # Verify values
        self.assertEqual(data["pending_balance"], "250.00")
        self.assertEqual(data["currency"], "EUR")
        self.assertEqual(data["calculation_period"], "15 days")
        self.assertEqual(data["reservations_count"], 1)

    def test_get_pending_balance_with_property_filter(self):
        """Test getting pending balance with property filter."""
        url = reverse("booking_financial:pending-balance")
        response = self.client.get(url, {"property_ids": str(self.property.id)})

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertEqual(data["pending_balance"], "250.00")

    def test_get_pending_balance_unauthorized(self):
        """Test getting pending balance without authentication."""
        self.client.force_authenticate(user=None)
        url = reverse("booking_financial:pending-balance")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class TotalEarningsAPITestCase(FinancialAPITestCase):
    """Test cases for TotalEarningsAPIView."""

    def test_get_total_earnings(self):
        """Test getting total earnings."""
        url = reverse("booking_financial:total-earnings")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertIn("total_earnings", data)
        self.assertIn("currency", data)
        self.assertIn("calculation_type", data)
        self.assertIn("last_updated", data)
        self.assertIn("total_reservations", data)

        # Verify values
        self.assertEqual(data["total_earnings"], "550.00")  # 250 + 300
        self.assertEqual(data["currency"], "EUR")
        self.assertEqual(data["calculation_type"], "lifetime")
        self.assertEqual(data["total_reservations"], 2)

    def test_get_total_earnings_with_property_filter(self):
        """Test getting total earnings with property filter."""
        url = reverse("booking_financial:total-earnings")
        response = self.client.get(url, {"property_ids": str(self.property.id)})

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertEqual(data["total_earnings"], "550.00")


class PaymentStatusAPITestCase(FinancialAPITestCase):
    """Test cases for PaymentStatusAPIView."""

    def test_get_payment_status_overview(self):
        """Test getting payment status overview."""
        url = reverse("booking_financial:payment-status")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertIn("current_payment_cycle", data)
        self.assertIn("payment_in_progress", data)
        self.assertIn("future_payment", data)

        # Verify payment cycle structure
        cycle = data["current_payment_cycle"]
        self.assertIn("start_date", cycle)
        self.assertIn("end_date", cycle)
        self.assertIn("payment_date", cycle)

        # Verify status structures
        for status_key in ["payment_in_progress", "future_payment"]:
            status_data = data[status_key]
            self.assertIn("count", status_data)
            self.assertIn("total_amount", status_data)
            self.assertIn("reservations", status_data)

    def test_get_payment_status_filtered(self):
        """Test getting payment status with specific status filter."""
        url = reverse("booking_financial:payment-status")
        response = self.client.get(url, {"status": "payment_in_progress"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertIn("payment_in_progress", data)
        self.assertNotIn("future_payment", data)

    def test_get_payment_status_with_limit(self):
        """Test getting payment status with limit parameter."""
        url = reverse("booking_financial:payment-status")
        response = self.client.get(url, {"limit": "1"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        # Each status should have at most 1 reservation
        for status_key in ["payment_in_progress", "future_payment"]:
            if status_key in data:
                reservations = data[status_key]["reservations"]
                self.assertLessEqual(len(reservations), 1)


class MonthlyEarningsAPITestCase(FinancialAPITestCase):
    """Test cases for MonthlyEarningsAPIView."""

    def test_get_monthly_earnings_current_month(self):
        """Test getting monthly earnings for current month."""
        url = reverse("booking_financial:monthly-earnings")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertIn("year", data)
        self.assertIn("month", data)
        self.assertIn("first_cycle_earnings", data)
        self.assertIn("second_cycle_earnings", data)
        self.assertIn("total_monthly_earnings", data)
        self.assertIn("first_cycle_period", data)
        self.assertIn("second_cycle_period", data)
        self.assertIn("currency", data)
        self.assertIn("last_updated", data)

        # Verify current month/year
        today = date.today()
        self.assertEqual(data["year"], today.year)
        self.assertEqual(data["month"], today.month)
        self.assertEqual(data["currency"], "EUR")

    def test_get_monthly_earnings_specific_month(self):
        """Test getting monthly earnings for specific month."""
        url = reverse("booking_financial:monthly-earnings")
        response = self.client.get(url, {"year": "2024", "month": "1"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertEqual(data["year"], 2024)
        self.assertEqual(data["month"], 1)

    def test_get_monthly_earnings_with_property_filter(self):
        """Test getting monthly earnings with property filter."""
        url = reverse("booking_financial:monthly-earnings")
        response = self.client.get(url, {"property_ids": str(self.property.id)})

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        # Should still return valid structure
        self.assertIn("total_monthly_earnings", data)


class ReservationPaymentInfoAPITestCase(FinancialAPITestCase):
    """Test cases for reservation payment info endpoint."""

    def test_get_reservation_payment_info(self):
        """Test getting payment info for specific reservation."""
        url = reverse(
            "booking_financial:reservation-payment-info",
            kwargs={"reservation_id": str(self.recent_reservation.id)},
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        self.assertIn("reservation_id", data)
        self.assertIn("payment_status", data)
        self.assertIn("payment_status_display", data)
        self.assertIn("payment_date", data)
        self.assertIn("payment_cycle_start", data)
        self.assertIn("payment_cycle_end", data)
        self.assertIn("days_until_payment", data)
        self.assertIn("is_current_cycle", data)
        self.assertIn("net_amount", data)
        self.assertIn("checkout_date", data)

        # Verify values
        self.assertEqual(data["reservation_id"], str(self.recent_reservation.id))
        self.assertEqual(data["net_amount"], "250.00")
        self.assertIn(data["payment_status"], ["payment_in_progress", "future_payment"])

    def test_get_reservation_payment_info_not_found(self):
        """Test getting payment info for non-existent reservation."""
        url = reverse(
            "booking_financial:reservation-payment-info",
            kwargs={"reservation_id": "non-existent-id"},
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_reservation_payment_info_no_checkout_date(self):
        """Test getting payment info for reservation without checkout date."""
        # Create reservation without checkout date
        test_customer = Customer.objects.create(
            first_name="Test",
            last_name="Customer",
            email="<EMAIL>",
            telephone="+1111111111",
        )

        reservation = Reservation.objects.create(
            id=f"nocheckout_{uuid.uuid4().hex[:10]}",
            guest_name="No Checkout Guest",
            checkout_date=None,
            net_total_for_owner=Decimal("100.00"),
        )

        booking = Booking.objects.create(
            property=self.property,
            customer=test_customer,
            reservation_data=reservation,
            status=Booking.Status.COMPLETED,
            checkin_date=date.today() - timedelta(days=2),
            checkout_date=date.today(),
        )

        url = reverse(
            "booking_financial:reservation-payment-info",
            kwargs={"reservation_id": str(reservation.id)},
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        data = response.json()
        self.assertIn("error", data)
        self.assertIn("no checkout date", data["error"].lower())

    def test_get_reservation_payment_info_unauthorized_property(self):
        """Test getting payment info for reservation from unauthorized property."""
        # Create another user and property
        other_user = User.objects.create_user(
            name="Other User", email="<EMAIL>", password="testpass123"
        )
        other_location = Location.objects.create(
            street="456 Other Street",
            post_code="67890",
            city="Other City",
            country="Other Country",
            latitude=46.0,
            longitude=10.0,
        )
        other_property = Property.objects.create(
            name="Other Property", is_domorent=False, location=other_location
        )
        other_property.staffs.add(other_user)

        other_customer = Customer.objects.create(
            first_name="Other",
            last_name="Customer",
            email="<EMAIL>",
            telephone="+9876543210",
        )

        other_reservation = Reservation.objects.create(
            id=f"other_{uuid.uuid4().hex[:10]}",
            guest_name="Other Guest",
            checkout_date=date.today(),
            net_total_for_owner=Decimal("100.00"),
        )


        url = reverse(
            "booking_financial:reservation-payment-info",
            kwargs={"reservation_id": str(other_reservation.id)},
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class FinancialAPIErrorHandlingTestCase(TestCase):
    """Test cases for financial API error handling."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            name="Test User", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

    def test_api_endpoints_handle_no_data(self):
        """Test that API endpoints handle cases with no data gracefully."""
        endpoints = [
            reverse("booking_financial:pending-balance"),
            reverse("booking_financial:total-earnings"),
            reverse("booking_financial:payment-status"),
            reverse("booking_financial:monthly-earnings"),
        ]

        for url in endpoints:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            data = response.json()
            # Should not raise errors even with no data
            self.assertIsInstance(data, dict)

    def test_api_endpoints_handle_invalid_parameters(self):
        """Test that API endpoints handle invalid parameters gracefully."""
        # Test monthly earnings with invalid year/month
        url = reverse("booking_financial:monthly-earnings")
        response = self.client.get(url, {"year": "invalid", "month": "invalid"})

        # Should return error or handle gracefully
        self.assertIn(
            response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
        )

    def test_api_endpoints_require_authentication(self):
        """Test that all financial API endpoints require authentication."""
        self.client.force_authenticate(user=None)

        endpoints = [
            reverse("booking_financial:pending-balance"),
            reverse("booking_financial:total-earnings"),
            reverse("booking_financial:payment-status"),
            reverse("booking_financial:monthly-earnings"),
        ]

        for url in endpoints:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
