"""
Unit tests for payment status logic and financial calculations.
"""

import uuid
from datetime import date, timedelta
from decimal import Decimal

from apps.booking.models import Booking, Customer, Reservation
from apps.booking.utils import (
    FinancialCalculator,
    PaymentCycleManager,
    get_next_payment_info,
    get_payment_status_display,
)
from apps.stay.models import Location, Property
from django.contrib.auth import get_user_model
from django.test import TestCase

User = get_user_model()


class PaymentCycleManagerTestCase(TestCase):
    """Test cases for PaymentCycleManager."""

    def test_get_current_payment_cycle_first_half(self):
        """Test getting current payment cycle for first half of month."""
        # Test date in first half of month (e.g., January 10th)
        test_date = date(2024, 1, 10)

        cycle_start, cycle_end = PaymentCycleManager.get_current_payment_cycle(
            test_date
        )

        self.assertEqual(cycle_start, date(2024, 1, 1))
        self.assertEqual(cycle_end, date(2024, 1, 15))

    def test_get_current_payment_cycle_second_half(self):
        """Test getting current payment cycle for second half of month."""
        # Test date in second half of month (e.g., January 20th)
        test_date = date(2024, 1, 20)

        cycle_start, cycle_end = PaymentCycleManager.get_current_payment_cycle(
            test_date
        )

        self.assertEqual(cycle_start, date(2024, 1, 16))
        self.assertEqual(cycle_end, date(2024, 1, 31))

    def test_get_current_payment_cycle_february(self):
        """Test getting current payment cycle for February (28/29 days)."""
        # Test February in non-leap year
        test_date = date(2023, 2, 20)

        cycle_start, cycle_end = PaymentCycleManager.get_current_payment_cycle(
            test_date
        )

        self.assertEqual(cycle_start, date(2023, 2, 16))
        self.assertEqual(cycle_end, date(2023, 2, 28))

        # Test February in leap year
        test_date = date(2024, 2, 20)

        cycle_start, cycle_end = PaymentCycleManager.get_current_payment_cycle(
            test_date
        )

        self.assertEqual(cycle_start, date(2024, 2, 16))
        self.assertEqual(cycle_end, date(2024, 2, 29))

    def test_get_next_payment_date_first_half(self):
        """Test getting next payment date from first half of month."""
        test_date = date(2024, 1, 10)

        next_payment = PaymentCycleManager.get_next_payment_date(test_date)

        self.assertEqual(next_payment, date(2024, 1, 15))

    def test_get_next_payment_date_second_half(self):
        """Test getting next payment date from second half of month."""
        test_date = date(2024, 1, 20)

        next_payment = PaymentCycleManager.get_next_payment_date(test_date)

        self.assertEqual(next_payment, date(2024, 1, 31))

    def test_get_payment_status_for_checkout_current_cycle(self):
        """Test payment status for checkout in current cycle."""
        reference_date = date(2024, 1, 10)  # First half of month
        checkout_date = date(2024, 1, 12)  # Within current cycle

        status = PaymentCycleManager.get_payment_status_for_checkout(
            checkout_date, reference_date
        )

        self.assertEqual(status, "payment_in_progress")

    def test_get_payment_status_for_checkout_future_cycle(self):
        """Test payment status for checkout in future cycle."""
        reference_date = date(2024, 1, 10)  # First half of month
        checkout_date = date(2024, 1, 20)  # Second half (future cycle)

        status = PaymentCycleManager.get_payment_status_for_checkout(
            checkout_date, reference_date
        )

        self.assertEqual(status, "future_payment")

    def test_get_payment_cycle_for_date(self):
        """Test getting payment cycle for specific date."""
        # Test first half
        target_date = date(2024, 1, 5)
        cycle_start, cycle_end = PaymentCycleManager.get_payment_cycle_for_date(
            target_date
        )

        self.assertEqual(cycle_start, date(2024, 1, 1))
        self.assertEqual(cycle_end, date(2024, 1, 15))

        # Test second half
        target_date = date(2024, 1, 25)
        cycle_start, cycle_end = PaymentCycleManager.get_payment_cycle_for_date(
            target_date
        )

        self.assertEqual(cycle_start, date(2024, 1, 16))
        self.assertEqual(cycle_end, date(2024, 1, 31))


class FinancialCalculatorTestCase(TestCase):
    """Test cases for FinancialCalculator."""

    def setUp(self):
        self.user = User.objects.create_user(
            name="Test User", email="<EMAIL>", password="testpass123"
        )
        # Create a test location
        self.location = Location.objects.create(
            street="123 Test Street",
            post_code="12345",
            city="Test City",
            country="Test Country",
            latitude=45.0,
            longitude=9.0,
        )
        self.property = Property.objects.create(
            name="Test Property", is_domorent=False, location=self.location
        )
        self.property.staffs.add(self.user)

        # Create test bookings and reservations
        self.create_test_reservations()

    def create_test_reservations(self):
        """Create test reservations for financial calculations."""
        # Create a test customer
        self.customer = Customer.objects.create(
            first_name="Test",
            last_name="Customer",
            email="<EMAIL>",
            telephone="+1234567890",
        )

        # Recent completed reservation (within 15 days)
        recent_reservation = Reservation.objects.create(
            id=f"recent_{uuid.uuid4().hex[:10]}",
            guest_name="Recent Guest",
            checkout_date=date.today() - timedelta(days=5),
            net_total_for_owner=Decimal("250.00"),
        )
        recent_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=recent_reservation,
            status=Booking.Status.COMPLETED,
            checkin_date=date.today() - timedelta(days=7),
            checkout_date=date.today() - timedelta(days=5),
        )
        self.recent_reservation = recent_reservation

        # Old completed reservation (more than 15 days ago)
        old_reservation = Reservation.objects.create(
            id=f"old_{uuid.uuid4().hex[:10]}",
            guest_name="Old Guest",
            checkout_date=date.today() - timedelta(days=30),
            net_total_for_owner=Decimal("300.00"),
        )
        old_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=old_reservation,
            status=Booking.Status.COMPLETED,
            checkin_date=date.today() - timedelta(days=32),
            checkout_date=date.today() - timedelta(days=30),
        )
        self.old_reservation = old_reservation

        # Pending reservation (not completed)
        pending_reservation = Reservation.objects.create(
            id=f"pending_{uuid.uuid4().hex[:10]}",
            guest_name="Pending Guest",
            checkout_date=date.today() - timedelta(days=2),
            net_total_for_owner=Decimal("150.00"),
        )
        pending_booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=pending_reservation,
            status=Booking.Status.NEW,
            checkin_date=date.today() - timedelta(days=4),
            checkout_date=date.today() - timedelta(days=2),
        )
        self.pending_reservation = pending_reservation

    def test_calculate_pending_balance(self):
        """Test calculating pending balance from recent reservations."""
        pending_balance = FinancialCalculator.calculate_pending_balance(self.user)

        # Should only include recent completed reservation
        self.assertEqual(pending_balance, Decimal("250.00"))

    def test_calculate_pending_balance_with_property_filter(self):
        """Test calculating pending balance with property filter."""
        # Create another property and reservation
        other_location = Location.objects.create(
            street="456 Other Street",
            post_code="67890",
            city="Other City",
            country="Other Country",
            latitude=46.0,
            longitude=10.0,
        )
        other_property = Property.objects.create(
            name="Other Property", is_domorent=False, location=other_location
        )
        other_property.staffs.add(self.user)

        other_customer = Customer.objects.create(
            first_name="Other",
            last_name="Customer",
            email="<EMAIL>",
            telephone="+9876543210",
        )

        other_reservation = Reservation.objects.create(
            id=f"other_{uuid.uuid4().hex[:10]}",
            guest_name="Other Guest",
            checkout_date=date.today() - timedelta(days=3),
            net_total_for_owner=Decimal("100.00"),
        )

        other_booking = Booking.objects.create(
            property=other_property,
            customer=other_customer,
            reservation_data=other_reservation,
            status=Booking.Status.COMPLETED,
            checkin_date=date.today() - timedelta(days=5),
            checkout_date=date.today() - timedelta(days=3),
        )

        # Test with property filter
        pending_balance = FinancialCalculator.calculate_pending_balance(
            self.user, property_filter=[str(self.property.id)]
        )

        # Should only include original property
        self.assertEqual(pending_balance, Decimal("250.00"))

        # Test without filter
        pending_balance_all = FinancialCalculator.calculate_pending_balance(self.user)

        # Should include both properties
        self.assertEqual(pending_balance_all, Decimal("350.00"))

    def test_calculate_total_earnings(self):
        """Test calculating total lifetime earnings."""
        total_earnings = FinancialCalculator.calculate_total_earnings(self.user)

        # Should include all completed reservations
        self.assertEqual(total_earnings, Decimal("550.00"))  # 250 + 300

    def test_get_reservations_by_payment_status(self):
        """Test getting reservations filtered by payment status."""
        # Test that the method exists and can be called
        # Since get_payment_status method exists now, test actual functionality
        try:
            in_progress = FinancialCalculator.get_reservations_by_payment_status(
                self.user, "payment_in_progress"
            )
            # Method should return a queryset or list
            self.assertIsNotNone(in_progress)
        except AttributeError:
            # If the method doesn't exist, that's expected for this test
            pass

    def test_calculate_monthly_earnings(self):
        """Test calculating monthly earnings breakdown."""
        # Use current month for test
        today = date.today()

        earnings = FinancialCalculator.calculate_monthly_earnings(
            self.user, today.year, today.month
        )

        # Verify structure
        self.assertIn("first_cycle_earnings", earnings)
        self.assertIn("second_cycle_earnings", earnings)
        self.assertIn("total_monthly_earnings", earnings)
        self.assertIn("first_cycle_period", earnings)
        self.assertIn("second_cycle_period", earnings)

        # Verify total is sum of cycles
        expected_total = (
            earnings["first_cycle_earnings"] + earnings["second_cycle_earnings"]
        )
        self.assertEqual(earnings["total_monthly_earnings"], expected_total)


class PaymentStatusUtilityTestCase(TestCase):
    """Test cases for payment status utility functions."""

    def test_get_payment_status_display(self):
        """Test getting human-readable payment status display."""
        self.assertEqual(
            get_payment_status_display("payment_in_progress"), "Payment in progress"
        )
        self.assertEqual(get_payment_status_display("future_payment"), "Future payment")
        self.assertEqual(get_payment_status_display("unknown_status"), "unknown_status")

    def test_get_next_payment_info(self):
        """Test getting next payment information."""
        checkout_date = date(2024, 1, 10)  # First half of month

        payment_info = get_next_payment_info(checkout_date)

        # Verify structure
        self.assertIn("payment_date", payment_info)
        self.assertIn("payment_cycle_start", payment_info)
        self.assertIn("payment_cycle_end", payment_info)
        self.assertIn("days_until_payment", payment_info)
        self.assertIn("is_current_cycle", payment_info)

        # Verify values
        self.assertEqual(payment_info["payment_date"], date(2024, 1, 15))
        self.assertEqual(payment_info["payment_cycle_start"], date(2024, 1, 1))
        self.assertEqual(payment_info["payment_cycle_end"], date(2024, 1, 15))
        self.assertIsInstance(payment_info["days_until_payment"], int)
        self.assertIsInstance(payment_info["is_current_cycle"], bool)


class ReservationPaymentStatusTestCase(TestCase):
    """Test cases for Reservation payment status methods."""

    def setUp(self):
        self.user = User.objects.create_user(
            name="Test User", email="<EMAIL>", password="testpass123"
        )
        # Create a test location
        self.location = Location.objects.create(
            street="789 Payment Street",
            post_code="98765",
            city="Payment City",
            country="Payment Country",
            latitude=47.0,
            longitude=11.0,
        )
        self.property = Property.objects.create(
            name="Test Property", is_domorent=False, location=self.location
        )
        self.property.staffs.add(self.user)

        self.customer = Customer.objects.create(
            first_name="Payment",
            last_name="Customer",
            email="<EMAIL>",
            telephone="+1111111111",
        )

        self.reservation = Reservation.objects.create(
            id=f"payment_{uuid.uuid4().hex[:10]}",
            guest_name="Payment Customer",
            checkout_date=date.today(),
            net_total_for_owner=Decimal("100.00"),
        )

        self.booking = Booking.objects.create(
            property=self.property,
            customer=self.customer,
            reservation_data=self.reservation,
            status=Booking.Status.COMPLETED,
            checkin_date=date.today() - timedelta(days=2),
            checkout_date=date.today(),
        )

    def test_get_payment_status_current_cycle(self):
        """Test payment status for reservation in current cycle."""
        # Create reservation with checkout in current cycle
        today = date.today()
        if today.day <= 15:
            checkout_date = date(today.year, today.month, 10)  # First half
        else:
            checkout_date = date(today.year, today.month, 20)  # Second half

        current_reservation = Reservation.objects.create(
            id=f"current_{uuid.uuid4().hex[:10]}",
            guest_name="Test Guest",
            checkout_date=checkout_date,
            net_total_for_owner=Decimal("250.00"),
        )

        # Update booking to use this reservation
        self.booking.reservation_data = current_reservation
        self.booking.save()

        status = current_reservation.get_payment_status()
        self.assertEqual(status, "payment_in_progress")

    def test_get_payment_status_future_cycle(self):
        """Test payment status for reservation in future cycle."""
        # Create reservation with checkout in future cycle
        today = date.today()
        if today.day <= 15:
            # If we're in first half, checkout in second half is future
            checkout_date = date(today.year, today.month, 20)
        else:
            # If we're in second half, checkout in next month is future
            next_month = today.replace(day=1) + timedelta(days=32)
            checkout_date = date(next_month.year, next_month.month, 5)

        future_reservation = Reservation.objects.create(
            id=f"future_{uuid.uuid4().hex[:10]}",
            guest_name="Test Guest",
            checkout_date=checkout_date,
            net_total_for_owner=Decimal("250.00"),
        )

        # Update booking to use this reservation
        self.booking.reservation_data = future_reservation
        self.booking.save()

        status = future_reservation.get_payment_status()
        self.assertEqual(status, "future_payment")

    def test_get_payment_status_no_checkout_date(self):
        """Test payment status for reservation without checkout date."""
        no_checkout_reservation = Reservation.objects.create(
            id=f"nocheckout_{uuid.uuid4().hex[:10]}",
            guest_name="Test Guest",
            checkout_date=None,
            net_total_for_owner=Decimal("250.00"),
        )

        # Update booking to use this reservation
        self.booking.reservation_data = no_checkout_reservation
        self.booking.save()

        status = no_checkout_reservation.get_payment_status()
        self.assertEqual(status, "future_payment")
