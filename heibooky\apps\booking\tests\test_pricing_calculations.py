"""
Unit tests for Heibooky pricing calculations.
"""

from decimal import Decimal

from apps.booking.api import calculate_heibooky_pricing
from apps.booking.models import ActivationFeeConfig, PropertyActivationFee
from apps.stay.models import Location, Property
from django.contrib.auth import get_user_model
from django.test import TestCase

User = get_user_model()


class HeibookyPricingCalculationTestCase(TestCase):
    """Test cases for Heibooky pricing calculations."""

    def setUp(self):
        self.user = User.objects.create_user(
            name="Test User", email="<EMAIL>", password="testpass123"
        )
        # Create a test location
        self.location = Location.objects.create(
            street="123 Test Street",
            post_code="12345",
            city="Test City",
            country="Test Country",
            latitude=45.0,
            longitude=9.0,
        )
        self.property = Property.objects.create(
            name="Test Property", is_domorent=False, location=self.location
        )
        self.property.staffs.add(self.user)

        # Create activation fee config
        ActivationFeeConfig.objects.create(
            fee_amount=Decimal("150.00"), is_active=True, created_by=self.user
        )

    def test_basic_heibooky_pricing_calculation(self):
        """Test basic Heibooky pricing calculation matching the financial summary."""
        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # Verify calculations match the Heibooky Financial Summary
        self.assertEqual(result["client_price"], 1000.00)
        self.assertEqual(result["ota_commission"], 150.00)  # 15% of 1000
        self.assertEqual(result["heibooky_commission"], 80.00)  # 8% of 1000
        self.assertEqual(result["payment_fee"], 35.00)  # 3.5% of 1000
        self.assertEqual(result["vat_22"], 50.60)  # 22% of (150 + 80)
        self.assertEqual(result["substitute_tax_21"], 210.00)  # 21% of 1000
        self.assertEqual(result["activation_fee"], 150.00)  # Default activation fee

        expected_payout = 1000.00 - 150.00 - 80.00 - 35.00 - 50.60 - 210.00 - 150.00
        # Calculate expected owner payout: 1000 - 150 - 80 - 35 - 50.60 - 210 - 150 = 324.40
        self.assertAlmostEqual(result["total_owner_payout"], expected_payout, places=2)

    def test_pricing_without_activation_fee(self):
        """Test pricing calculation without activation fee."""
        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=False,
        )

        self.assertEqual(result["activation_fee"], 0.00)
        self.assertEqual(result["activation_fee_applied"], 0.00)

        # Owner payout should be higher without activation fee
        expected_payout = 1000.00 - 150.00 - 80.00 - 35.00 - 50.60 - 210.00
        self.assertAlmostEqual(result["total_owner_payout"], expected_payout, places=2)

    def test_pricing_with_existing_activation_fee_tracking(self):
        """Test pricing when property already has activation fee tracking."""
        # Create existing activation fee tracking with partial recovery
        PropertyActivationFee.objects.create(
            property_instance=self.property,
            user=self.user,
            original_fee_amount=Decimal("150.00"),
            remaining_fee_amount=Decimal("75.00"),
            total_recovered_amount=Decimal("75.00"),
        )

        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # Should only apply remaining activation fee amount
        self.assertEqual(result["activation_fee_amount"], 150.00)  # Original amount
        self.assertEqual(
            result["activation_fee_applied"], 75.00
        )  # Only remaining amount

    def test_pricing_with_fully_recovered_activation_fee(self):
        """Test pricing when activation fee is fully recovered."""
        # Create fully recovered activation fee tracking
        PropertyActivationFee.objects.create(
            property_instance=self.property,
            user=self.user,
            original_fee_amount=Decimal("150.00"),
            remaining_fee_amount=Decimal("0.00"),
            total_recovered_amount=Decimal("150.00"),
            is_fully_recovered=True,
        )

        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # Should not apply any activation fee
        self.assertEqual(result["activation_fee"], 0.00)
        self.assertEqual(result["activation_fee_applied"], 0.00)

    def test_pricing_prevents_negative_owner_payout(self):
        """Test that activation fee doesn't make owner payout negative."""
        # Use a small client price that would result in negative payout with full activation fee
        client_price = 100.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # Owner payout should not be negative
        self.assertGreaterEqual(result["total_owner_payout"], 0.00)

        # Activation fee should be reduced to prevent negative payout
        self.assertLess(result["activation_fee_applied"], 150.00)

    def test_pricing_with_inactive_activation_fee(self):
        """Test pricing when activation fee is inactive."""
        # Deactivate activation fee
        ActivationFeeConfig.objects.filter(is_active=True).update(is_active=False)

        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # Should not apply activation fee when inactive
        self.assertEqual(result["activation_fee"], 0.00)
        self.assertEqual(result["activation_fee_applied"], 0.00)

    def test_pricing_calculation_precision(self):
        """Test that pricing calculations maintain decimal precision."""
        client_price = 999.99

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=False,
        )

        # Verify precise calculations
        expected_ota = 999.99 * 0.15  # 149.9985
        expected_heibooky = 999.99 * 0.08  # 79.9992
        expected_payment = 999.99 * 0.035  # 34.99965
        expected_vat = (expected_ota + expected_heibooky) * 0.22  # 50.59969
        expected_substitute = 999.99 * 0.21  # 209.9979

        self.assertAlmostEqual(result["ota_commission"], expected_ota, places=2)
        self.assertAlmostEqual(
            result["heibooky_commission"], expected_heibooky, places=2
        )
        self.assertAlmostEqual(result["payment_fee"], expected_payment, places=2)
        self.assertAlmostEqual(result["vat_22"], expected_vat, places=2)
        self.assertAlmostEqual(
            result["substitute_tax_21"], expected_substitute, places=2
        )

    def test_pricing_with_zero_client_price(self):
        """Test pricing calculation with zero client price."""
        result = calculate_heibooky_pricing(
            client_price=0.00,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # All values should be zero except activation fee handling
        self.assertEqual(result["client_price"], 0.00)
        self.assertEqual(result["ota_commission"], 0.00)
        self.assertEqual(result["heibooky_commission"], 0.00)
        self.assertEqual(result["payment_fee"], 0.00)
        self.assertEqual(result["vat_22"], 0.00)
        self.assertEqual(result["substitute_tax_21"], 0.00)
        self.assertEqual(result["total_owner_payout"], 0.00)
        self.assertEqual(
            result["activation_fee_applied"], 0.00
        )  # Can't deduct from zero

    def test_pricing_error_handling(self):
        """Test pricing calculation error handling."""
        # Test with invalid client price
        result = calculate_heibooky_pricing(
            client_price="invalid",
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # Should return fallback values
        self.assertEqual(result["calculation_type"], "Heibooky Property")
        self.assertEqual(
            result["total_owner_payout"], "invalid"
        )  # Fallback to original

    def test_pricing_field_aliases(self):
        """Test that pricing calculation includes field aliases for compatibility."""
        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        # Verify aliases exist
        self.assertEqual(result["base_price"], result["client_price"])
        self.assertEqual(result["payment_charge"], result["payment_fee"])
        self.assertEqual(result["iva_amount"], result["vat_22"])
        self.assertEqual(result["owner_tax"], result["substitute_tax_21"])
        self.assertEqual(result["net_total_for_owner"], result["total_owner_payout"])

    def test_pricing_return_structure(self):
        """Test that pricing calculation returns all required fields."""
        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        required_fields = [
            "calculation_type",
            "client_price",
            "base_price",
            "ota_commission",
            "heibooky_commission",
            "payment_fee",
            "payment_charge",
            "activation_fee",
            "vat_22",
            "iva_amount",
            "substitute_tax_21",
            "owner_tax",
            "net_total_for_owner",
            "total_owner_payout",
            "total_price",
            "total_tax",
            "commission_amount",
            "activation_fee_amount",
            "activation_fee_applied",
            "owner_payout_before_activation",
        ]

        for field in required_fields:
            self.assertIn(field, result, f"Missing required field: {field}")

    def test_different_activation_fee_amounts(self):
        """Test pricing with different activation fee amounts."""
        # Update activation fee to different amount
        ActivationFeeConfig.objects.filter(is_active=True).update(
            fee_amount=Decimal("200.00")
        )

        client_price = 1000.00

        result = calculate_heibooky_pricing(
            client_price=client_price,
            property_instance=self.property,
            user=self.user,
            include_activation_fee=True,
        )

        self.assertEqual(result["activation_fee_amount"], 200.00)
        self.assertEqual(result["activation_fee_applied"], 200.00)
