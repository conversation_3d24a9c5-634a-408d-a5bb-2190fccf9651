"""
URL patterns for financial API endpoints.
"""

from django.urls import path

from ..views.financial import (
    ActivationFeeStatusAPIView,
    CommissionBreakdownAPIView,
    ComprehensiveFinancialSummaryAPIView,
    MonthlyEarningsAPIView,
    PaymentStatusAPIView,
    PendingBalanceAPIView,
    TotalEarningsAPIView,
    payment_cycle_info,
    reservation_payment_info,
)

urlpatterns = [
    # Core financial dashboard APIs
    path("pending-balance/", PendingBalanceAPIView.as_view(), name="pending-balance"),
    path("total-earnings/", TotalEarningsAPIView.as_view(), name="total-earnings"),
    path("payment-status/", PaymentStatusAPIView.as_view(), name="payment-status"),
    path(
        "monthly-earnings/", MonthlyEarningsAPIView.as_view(), name="monthly-earnings"
    ),
    # Comprehensive financial summary
    path(
        "summary/",
        ComprehensiveFinancialSummaryAPIView.as_view(),
        name="financial-summary",
    ),
    # Commission and fee breakdown
    path(
        "commission-breakdown/",
        CommissionBreakdownAPIView.as_view(),
        name="commission-breakdown",
    ),
    # Activation fee management
    path(
        "activation-fee-status/",
        ActivationFeeStatusAPIView.as_view(),
        name="activation-fee-status",
    ),
    # Payment cycle information
    path("payment-cycle-info/", payment_cycle_info, name="payment-cycle-info"),
    # Individual reservation payment info
    path(
        "reservation/<str:reservation_id>/payment-info/",
        reservation_payment_info,
        name="reservation-payment-info",
    ),
]
