from apps.booking.views import (
    BookingBlockViewSet,
    BookingCancellationRequestView,
    BookingCreateAPIView,
    BookingExportAPIView,
    BookingExportTemplateView,
    BookingTestAPIView,
    PropertyBookingListAPIView,
    PropertyBookingsAPIView,
    SuReservationWebhookView,
)
from django.urls import include, path
from rest_framework.routers import DefaultRouter

from .financial import urlpatterns as financial_urlpatterns

router = DefaultRouter()
router.register(r"booking-blocks", BookingBlockViewSet, basename="bookingblock")

urlpatterns = [
    path("", include(router.urls)),
    path(
        "property/", PropertyBookingListAPIView.as_view(), name="property-bookings-list"
    ),
    path(
        "property/<str:property_id>",
        PropertyBookingsAPIView.as_view(),
        name="property-bookings",
    ),
    path("manual-booking/", BookingCreateAPIView.as_view(), name="manual-booking"),
    path(
        "cancellation-request/",
        BookingCancellationRequestView.as_view(),
        name="booking-cancellation-request",
    ),
    path("test/", BookingTestAPIView.as_view(), name="booking-test"),
    path("export/", BookingExportTemplateView.as_view(), name="booking-export"),
    path("api/export/", BookingExportAPIView.as_view(), name="booking-export-api"),
    path(
        "webhook/reservation-push/",
        SuReservationWebhookView.as_view(),
        name="su-reservation-webhook",
    ),
    # Financial APIs
    path("financial/", include((financial_urlpatterns, "booking_financial"))),
]
