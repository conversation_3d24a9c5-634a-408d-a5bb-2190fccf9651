"""
Payment status and financial calculation utilities for Heibooky booking system.

This module provides comprehensive utilities for calculating financial metrics,
payment statuses, and managing payment cycles for the Heibooky platform.
"""

import calendar
import logging
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple

from django.db.models import Case, CharField, Q, Sum, Value, When
from django.utils import timezone

from ..models import Booking, Reservation

logger = logging.getLogger(__name__)


class PaymentCycleManager:
    """
    Manager class for handling payment cycles and status calculations.

    Payment cycles:
    - 1st-15th of month: Payment on 15th
    - 16th-30th/31st of month: Payment on last day of month
    """

    @staticmethod
    def get_current_payment_cycle(
        reference_date: Optional[date] = None,
    ) -> Tuple[date, date]:
        """
        Get the current payment cycle start and end dates.

        Args:
            reference_date: Date to use as reference (defaults to today)

        Returns:
            Tuple of (cycle_start_date, cycle_end_date)
        """
        if reference_date is None:
            reference_date = date.today()
        elif not isinstance(reference_date, date):
            raise TypeError(
                f"reference_date must be a date object, got {type(reference_date)}"
            )

        if reference_date.day <= 15:
            # Current cycle: 1st-15th
            cycle_start = date(reference_date.year, reference_date.month, 1)
            cycle_end = date(reference_date.year, reference_date.month, 15)
        else:
            # Current cycle: 16th-last day of month
            cycle_start = date(reference_date.year, reference_date.month, 16)
            last_day = calendar.monthrange(reference_date.year, reference_date.month)[1]
            cycle_end = date(reference_date.year, reference_date.month, last_day)
        return cycle_start, cycle_end

    @staticmethod
    def get_next_payment_date(reference_date: Optional[date] = None) -> date:
        """
        Get the next payment date based on current cycle.

        Args:
            reference_date: Date to use as reference (defaults to today)

        Returns:
            Next payment date
        """
        if reference_date is None:
            reference_date = date.today()

        if reference_date.day <= 15:
            # Next payment is on 15th of current month
            return date(reference_date.year, reference_date.month, 15)
        else:
            # Next payment is on last day of current month
            last_day = calendar.monthrange(reference_date.year, reference_date.month)[1]
            return date(reference_date.year, reference_date.month, last_day)

    @staticmethod
    def get_payment_status_for_checkout(
        checkout_date: date, reference_date: Optional[date] = None
    ) -> str:
        """
        Determine payment status for a given checkout date.

        Args:
            checkout_date: The checkout date of the reservation
            reference_date: Date to use as reference (defaults to today)

        Returns:
            'payment_in_progress' or 'future_payment'
        """
        if reference_date is None:
            reference_date = date.today()

        cycle_start, cycle_end = PaymentCycleManager.get_current_payment_cycle(
            reference_date
        )

        # Check if checkout is within current payment cycle
        if cycle_start <= checkout_date <= cycle_end:
            return "payment_in_progress"
        else:
            return "future_payment"

    @staticmethod
    def get_payment_cycle_for_date(target_date: date) -> Tuple[date, date]:
        """
        Get the payment cycle that contains the given date.

        Args:
            target_date: Date to find the payment cycle for

        Returns:
            Tuple of (cycle_start_date, cycle_end_date)
        """
        if target_date.day <= 15:
            # Date is in first half of month
            cycle_start = date(target_date.year, target_date.month, 1)
            cycle_end = date(target_date.year, target_date.month, 15)
        else:
            # Date is in second half of month
            cycle_start = date(target_date.year, target_date.month, 16)
            last_day = calendar.monthrange(target_date.year, target_date.month)[1]
            cycle_end = date(target_date.year, target_date.month, last_day)

        return cycle_start, cycle_end


class FinancialCalculator:
    """
    Calculator class for financial metrics used in the payments dashboard.
    """

    @staticmethod
    def calculate_pending_balance(
        user, property_filter: Optional[List] = None
    ) -> Decimal:
        """
        Calculate total pending balance from reservations completed in last 15 days.

        Args:
            user: User instance to calculate balance for
            property_filter: Optional list of property IDs to filter by

        Returns:
            Total pending balance amount
        """
        # Get date 15 days ago
        fifteen_days_ago = date.today() - timedelta(days=15)

        # Base query for completed reservations
        query = Q(
            booking__property__staffs=user,
            booking__status=Booking.Status.COMPLETED,
            checkout_date__gte=fifteen_days_ago,
            checkout_date__lte=date.today(),
        )

        # Apply property filter if provided
        if property_filter:
            query &= Q(booking__property__id__in=property_filter)

        # Calculate sum of net_total_for_owner for matching reservations
        result = Reservation.objects.filter(query).aggregate(
            total=Sum("net_total_for_owner")
        )

        return result["total"] or Decimal("0.00")

    @staticmethod
    def calculate_total_earnings(
        user, property_filter: Optional[List] = None
    ) -> Decimal:
        """
        Calculate lifetime earnings from the platform.

        Args:
            user: User instance to calculate earnings for
            property_filter: Optional list of property IDs to filter by

        Returns:
            Total lifetime earnings amount
        """
        # Base query for all completed reservations
        query = Q(
            booking__property__staffs=user, booking__status=Booking.Status.COMPLETED
        )

        # Apply property filter if provided
        if property_filter:
            query &= Q(booking__property__id__in=property_filter)

        # Calculate sum of net_total_for_owner for all completed reservations
        result = Reservation.objects.filter(query).aggregate(
            total=Sum("net_total_for_owner")
        )

        return result["total"] or Decimal("0.00")

    @staticmethod
    def get_reservations_by_payment_status(
        user, status: str, property_filter: Optional[List] = None
    ) -> List[Reservation]:
        """
        Get reservations filtered by payment status.

        Args:
            user: User instance to get reservations for
            status: Payment status ('payment_in_progress' or 'future_payment')
            property_filter: Optional list of property IDs to filter by

        Returns:
            List of reservations matching the payment status
        """
        # Base query for completed reservations
        query = Q(
            booking__property__staffs=user, booking__status=Booking.Status.COMPLETED
        )

        # Apply property filter if provided
        if property_filter:
            query &= Q(booking__property__id__in=property_filter)

        # Calculate current payment cycle end date
        today = date.today()
        if today.day <= 15:
            # Current cycle: 1st-15th, payment on 15th
            current_cycle_end = date(today.year, today.month, 15)
        else:
            # Current cycle: 16th-30th/31st, payment on last day of month
            last_day = calendar.monthrange(today.year, today.month)[1]
            current_cycle_end = date(today.year, today.month, last_day)

        # Annotate with payment status based on checkout date and payment cycle
        reservations = (
            Reservation.objects.filter(query)
            .annotate(
                payment_status=Case(
                    When(
                        checkout_date__lte=current_cycle_end,
                        then=Value("payment_in_progress"),
                    ),
                    default=Value("future_payment"),
                    output_field=CharField(),
                )
            )
            .filter(payment_status=status)
        )

        return list(reservations)

    @staticmethod
    def calculate_monthly_earnings(
        user, year: int, month: int, property_filter: Optional[List] = None
    ) -> Dict[str, Decimal]:
        """
        Calculate earnings for a specific month broken down by payment cycles.

        Args:
            user: User instance to calculate earnings for
            year: Year to calculate for
            month: Month to calculate for
            property_filter: Optional list of property IDs to filter by

        Returns:
            Dictionary with earnings breakdown
        """
        # Define the two payment cycles for the month
        first_cycle_start = date(year, month, 1)
        first_cycle_end = date(year, month, 15)

        last_day = calendar.monthrange(year, month)[1]
        second_cycle_start = date(year, month, 16)
        second_cycle_end = date(year, month, last_day)

        # Base query
        base_query = Q(
            booking__property__staffs=user, booking__status=Booking.Status.COMPLETED
        )

        # Apply property filter if provided
        if property_filter:
            base_query &= Q(booking__property__id__in=property_filter)

        # Calculate earnings for first cycle (1st-15th)
        first_cycle_query = base_query & Q(
            checkout_date__gte=first_cycle_start, checkout_date__lte=first_cycle_end
        )
        first_cycle_earnings = Reservation.objects.filter(first_cycle_query).aggregate(
            total=Sum("net_total_for_owner")
        )["total"] or Decimal("0.00")

        # Calculate earnings for second cycle (16th-last day)
        second_cycle_query = base_query & Q(
            checkout_date__gte=second_cycle_start, checkout_date__lte=second_cycle_end
        )
        second_cycle_earnings = Reservation.objects.filter(
            second_cycle_query
        ).aggregate(total=Sum("net_total_for_owner"))["total"] or Decimal("0.00")

        return {
            "first_cycle_earnings": first_cycle_earnings,
            "second_cycle_earnings": second_cycle_earnings,
            "total_monthly_earnings": first_cycle_earnings + second_cycle_earnings,
            "first_cycle_period": f"{first_cycle_start.strftime('%d/%m')} - {first_cycle_end.strftime('%d/%m')}",
            "second_cycle_period": f"{second_cycle_start.strftime('%d/%m')} - {second_cycle_end.strftime('%d/%m')}",
        }


def get_payment_status_display(status: str) -> str:
    """
    Get human-readable display text for payment status.

    Args:
        status: Payment status code

    Returns:
        Human-readable status text
    """
    status_map = {
        "payment_in_progress": "Payment in progress",
        "future_payment": "Future payment",
    }
    return status_map.get(status, status)


def get_next_payment_info(checkout_date: date) -> Dict[str, Any]:
    """
    Get information about when a reservation will be paid.
    ...
    Args:
        checkout_date: Checkout date of the reservation

    Returns:
        Dictionary with payment information
    """
    cycle_start, cycle_end = PaymentCycleManager.get_payment_cycle_for_date(
        checkout_date
    )
    payment_date = cycle_end  # Payment happens on the last day of the cycle

    return {
        "payment_date": payment_date,
        "payment_cycle_start": cycle_start,
        "payment_cycle_end": cycle_end,
        "days_until_payment": (
            (payment_date - date.today()).days if payment_date > date.today() else 0
        ),
        "is_current_cycle": PaymentCycleManager.get_payment_status_for_checkout(
            checkout_date
        )
        == "payment_in_progress",
    }


class FinancialCalculator:
    """
    Comprehensive financial calculations for Heibooky platform.

    Handles pending balance, total earnings, and other financial metrics
    with proper distinction between Heibooky and Domorent properties.
    """

    @staticmethod
    def calculate_pending_balance(user, property_filter: List[str] = None) -> Decimal:
        """
        Calculate pending balance from reservations completed in last 15 days.

        Args:
            user: User instance
            property_filter: Optional list of property IDs to filter by

        Returns:
            Pending balance as Decimal
        """
        try:
            # Calculate date range for last 15 days
            fifteen_days_ago = date.today() - timedelta(days=15)

            # Build query for completed reservations in last 15 days
            query = Q(
                booking__property__staffs=user,
                booking__status=Booking.Status.COMPLETED,
                checkout_date__gte=fifteen_days_ago,
                checkout_date__lte=date.today(),
            )

            if property_filter:
                query &= Q(booking__property__id__in=property_filter)

            reservations = Reservation.objects.filter(query)

            total_pending = Decimal("0.00")

            for reservation in reservations:
                # Get the appropriate owner amount based on property type
                owner_amount = FinancialCalculator._get_owner_amount(reservation)
                total_pending += owner_amount

            return total_pending

        except Exception as e:
            logger.error(
                f"Error calculating pending balance for user {user.id}: {str(e)}"
            )
            return Decimal("0.00")

    @staticmethod
    def calculate_total_earnings(user, property_filter: List[str] = None) -> Decimal:
        """
        Calculate lifetime earnings from the platform.

        Args:
            user: User instance
            property_filter: Optional list of property IDs to filter by

        Returns:
            Total earnings as Decimal
        """
        try:
            # Build query for all completed reservations
            query = Q(
                booking__property__staffs=user,
                booking__status=Booking.Status.COMPLETED,
            )

            if property_filter:
                query &= Q(booking__property__id__in=property_filter)

            reservations = Reservation.objects.filter(query)

            total_earnings = Decimal("0.00")

            for reservation in reservations:
                # Get the appropriate owner amount based on property type
                owner_amount = FinancialCalculator._get_owner_amount(reservation)
                total_earnings += owner_amount

            return total_earnings

        except Exception as e:
            logger.error(
                f"Error calculating total earnings for user {user.id}: {str(e)}"
            )
            return Decimal("0.00")

    @staticmethod
    def _get_owner_amount(reservation) -> Decimal:
        """
        Get the appropriate owner amount based on property type.

        Args:
            reservation: Reservation instance

        Returns:
            Owner amount as Decimal
        """
        try:
            # Check if this is a Domorent property
            if (
                hasattr(reservation.booking, "property")
                and reservation.booking.property.is_domorent
            ):
                # For Domorent properties, use owner_net_transfer
                if reservation.owner_net_transfer:
                    return Decimal(str(reservation.owner_net_transfer))
            else:
                # For Heibooky properties, use net_total_for_owner
                if reservation.net_total_for_owner:
                    return Decimal(str(reservation.net_total_for_owner))

            # Fallback to total_price if specific fields are not available
            if reservation.total_price:
                return Decimal(str(reservation.total_price))

            return Decimal("0.00")

        except Exception as e:
            logger.error(
                f"Error getting owner amount for reservation {reservation.id}: {str(e)}"
            )
            return Decimal("0.00")

    @staticmethod
    def calculate_commission_breakdown(
        user, property_filter: List[str] = None
    ) -> Dict[str, Decimal]:
        """
        Calculate detailed commission breakdown for financial reporting.

        Args:
            user: User instance
            property_filter: Optional list of property IDs to filter by

        Returns:
            Dictionary with commission breakdown
        """
        try:
            query = Q(
                booking__property__staffs=user,
                booking__status=Booking.Status.COMPLETED,
            )

            if property_filter:
                query &= Q(booking__property__id__in=property_filter)

            reservations = Reservation.objects.filter(query)

            breakdown = {
                "total_ota_commission": Decimal("0.00"),
                "total_heibooky_commission": Decimal("0.00"),
                "total_domorent_commission": Decimal("0.00"),
                "total_payment_fees": Decimal("0.00"),
                "total_vat": Decimal("0.00"),
                "total_taxes": Decimal("0.00"),
                "total_activation_fees": Decimal("0.00"),
                "total_cleaning_costs": Decimal("0.00"),
            }

            for reservation in reservations:
                # Add commission amounts based on property type
                if (
                    hasattr(reservation.booking, "property")
                    and reservation.booking.property.is_domorent
                ):
                    # Domorent property commissions
                    if reservation.tot_platform_commission:
                        breakdown["total_domorent_commission"] += Decimal(
                            str(reservation.tot_platform_commission)
                        )
                    if reservation.cleaning_cost:
                        breakdown["total_cleaning_costs"] += Decimal(
                            str(reservation.cleaning_cost)
                        )
                else:
                    # Heibooky property commissions
                    if reservation.ota_commission:
                        breakdown["total_ota_commission"] += Decimal(
                            str(reservation.ota_commission)
                        )
                    if reservation.heibooky_commission:
                        breakdown["total_heibooky_commission"] += Decimal(
                            str(reservation.heibooky_commission)
                        )
                    if reservation.activation_fee_applied:
                        breakdown["total_activation_fees"] += Decimal(
                            str(reservation.activation_fee_applied)
                        )

                # Common fields
                if reservation.payment_charge:
                    breakdown["total_payment_fees"] += Decimal(
                        str(reservation.payment_charge)
                    )
                if reservation.iva_amount:
                    breakdown["total_vat"] += Decimal(str(reservation.iva_amount))
                if reservation.owner_tax:
                    breakdown["total_taxes"] += Decimal(str(reservation.owner_tax))

            return breakdown

        except Exception as e:
            logger.error(
                f"Error calculating commission breakdown for user {user.id}: {str(e)}"
            )
            return {}

    @staticmethod
    def get_financial_summary(user, property_filter: List[str] = None) -> Dict:
        """
        Get comprehensive financial summary for a user.

        Args:
            user: User instance
            property_filter: Optional list of property IDs to filter by

        Returns:
            Dictionary with complete financial summary
        """
        try:
            pending_balance = FinancialCalculator.calculate_pending_balance(
                user, property_filter
            )
            total_earnings = FinancialCalculator.calculate_total_earnings(
                user, property_filter
            )
            commission_breakdown = FinancialCalculator.calculate_commission_breakdown(
                user, property_filter
            )

            # Calculate next payment info
            next_payment_info = get_next_payment_info()

            return {
                "pending_balance": str(pending_balance),
                "total_earnings": str(total_earnings),
                "commission_breakdown": {
                    k: str(v) for k, v in commission_breakdown.items()
                },
                "next_payment_info": next_payment_info,
                "currency": "EUR",
                "last_updated": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(
                f"Error generating financial summary for user {user.id}: {str(e)}"
            )
            return {
                "pending_balance": "0.00",
                "total_earnings": "0.00",
                "commission_breakdown": {},
                "next_payment_info": {},
                "currency": "EUR",
                "last_updated": datetime.now().isoformat(),
                "error": str(e),
            }


class ActivationFeeManager:
    """
    Manages activation fee recovery and tracking for Heibooky properties.
    """

    @staticmethod
    def process_activation_fee_recovery(
        reservation, property_instance, user
    ) -> Decimal:
        """
        Process activation fee recovery for a reservation.

        Args:
            reservation: Reservation instance
            property_instance: Property instance
            user: User instance

        Returns:
            Amount of activation fee recovered
        """
        try:
            from ..models import ActivationFeeConfig, PropertyActivationFee

            # Check if activation fees are active
            if not ActivationFeeConfig.is_fee_active():
                return Decimal("0.00")

            # Get or create activation fee tracking
            activation_fee_status, created = (
                PropertyActivationFee.objects.get_or_create(
                    property_instance=property_instance,
                    user=user,
                    defaults={
                        "original_fee_amount": ActivationFeeConfig.get_current_fee(),
                        "remaining_fee_amount": ActivationFeeConfig.get_current_fee(),
                    },
                )
            )

            if activation_fee_status.is_fully_recovered:
                return Decimal("0.00")

            # Calculate available payout (amount owner would receive before activation fee)
            available_payout = Decimal("0.00")
            if reservation.net_total_for_owner and reservation.activation_fee_applied:
                # Add back the activation fee to get the payout before activation fee
                available_payout = Decimal(
                    str(reservation.net_total_for_owner)
                ) + Decimal(str(reservation.activation_fee_applied))
            elif reservation.net_total_for_owner:
                available_payout = Decimal(str(reservation.net_total_for_owner))

            # Calculate recovery amount
            recovery_amount = activation_fee_status.calculate_recovery_amount(
                available_payout
            )

            # Apply recovery
            if recovery_amount > 0:
                activation_fee_status.apply_recovery(recovery_amount)

                # Update reservation tracking fields
                reservation.activation_fee_remaining_before = (
                    activation_fee_status.remaining_fee_amount + recovery_amount
                )
                reservation.activation_fee_applied = recovery_amount
                reservation.activation_fee_remaining_after = (
                    activation_fee_status.remaining_fee_amount
                )
                reservation.save()

            return recovery_amount

        except Exception as e:
            logger.error(f"Error processing activation fee recovery: {str(e)}")
            return Decimal("0.00")

    @staticmethod
    def get_activation_fee_status(property_instance, user) -> Dict:
        """
        Get activation fee status for a property and user.

        Args:
            property_instance: Property instance
            user: User instance

        Returns:
            Dictionary with activation fee status
        """
        try:
            from ..models import ActivationFeeConfig, PropertyActivationFee

            if not ActivationFeeConfig.is_fee_active():
                return {
                    "activation_fee_active": False,
                    "message": "Activation fees are not currently active",
                }

            try:
                activation_fee_status = PropertyActivationFee.objects.get(
                    property_instance=property_instance, user=user
                )

                return {
                    "activation_fee_active": True,
                    "original_fee_amount": str(
                        activation_fee_status.original_fee_amount
                    ),
                    "remaining_fee_amount": str(
                        activation_fee_status.remaining_fee_amount
                    ),
                    "total_recovered_amount": str(
                        activation_fee_status.total_recovered_amount
                    ),
                    "recovery_percentage": activation_fee_status.recovery_percentage,
                    "is_fully_recovered": activation_fee_status.is_fully_recovered,
                    "recovery_started_at": (
                        activation_fee_status.recovery_started_at.isoformat()
                        if activation_fee_status.recovery_started_at
                        else None
                    ),
                    "fully_recovered_at": (
                        activation_fee_status.fully_recovered_at.isoformat()
                        if activation_fee_status.fully_recovered_at
                        else None
                    ),
                }

            except PropertyActivationFee.DoesNotExist:
                current_fee = ActivationFeeConfig.get_current_fee()
                return {
                    "activation_fee_active": True,
                    "original_fee_amount": str(current_fee),
                    "remaining_fee_amount": str(current_fee),
                    "total_recovered_amount": "0.00",
                    "recovery_percentage": 0.0,
                    "is_fully_recovered": False,
                    "recovery_started_at": None,
                    "fully_recovered_at": None,
                    "message": "New property - activation fee will be applied on first booking",
                }

        except Exception as e:
            logger.error(f"Error getting activation fee status: {str(e)}")
            return {"activation_fee_active": False, "error": str(e)}


def get_next_payment_info(reference_date: date = None) -> Dict[str, str]:
    """
    Get information about the next payment date and cycle.

    Args:
        reference_date: Date to calculate from (defaults to today)

    Returns:
        Dictionary with next payment information
    """
    if reference_date is None:
        reference_date = date.today()

    try:
        cycle_start, cycle_end = PaymentCycleManager.get_current_payment_cycle(
            reference_date
        )

        # Next payment date is the cycle end date
        next_payment_date = cycle_end

        # Calculate days until next payment
        days_until_payment = (next_payment_date - reference_date).days

        # Determine cycle description
        if reference_date.day <= 15:
            cycle_description = f"1st-15th {reference_date.strftime('%B %Y')}"
        else:
            cycle_description = (
                f"16th-{cycle_end.day} {reference_date.strftime('%B %Y')}"
            )

        return {
            "next_payment_date": next_payment_date.strftime("%Y-%m-%d"),
            "days_until_payment": str(max(0, days_until_payment)),
            "current_cycle": cycle_description,
            "cycle_start": cycle_start.strftime("%Y-%m-%d"),
            "cycle_end": cycle_end.strftime("%Y-%m-%d"),
        }

    except Exception as e:
        logger.error(f"Error calculating next payment info: {str(e)}")
        return {
            "next_payment_date": "",
            "days_until_payment": "0",
            "current_cycle": "",
            "cycle_start": "",
            "cycle_end": "",
            "error": str(e),
        }
