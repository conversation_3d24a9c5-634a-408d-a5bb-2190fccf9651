import logging
import uuid

from apps.booking.models import Booking, BookingBlock
from apps.booking.serializers import (
    BookingBlockSerializer,
    BookingCancellationRequestSerializer,
    BookingSerializer,
)
from apps.stay.models import Property
from apps.users.permissions import BookingPermission
from django.db import IntegrityError
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.dateparse import parse_date
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.serializers import ValidationError
from rest_framework.views import APIView
from services.email import EmailService
from services.notification.handlers import GeneralNotificationHandler


class BookingCreateAPIView(APIView):
    permission_classes = [IsAuthenticated, BookingPermission]

    def post(self, request):
        try:
            serializer = BookingSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            booking_instance = serializer.save()

            # Send notification using GeneralNotificationHandler
            handler = GeneralNotificationHandler(
                users=[request.user],
                title="Prenotazione manuale creata con successo",
                message=(
                    f"Una nuova prenotazione manuale è stata aggiunta per la struttura "
                    f"'{booking_instance.property.name}'. Grazie per aver utilizzato Heibooky!"
                ),
            )
            handler.send_notification()

            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except ValidationError as e:
            return Response(
                {
                    "error": "Validation error",
                    "details": (
                        e.detail if hasattr(e, "detail") else "Invalid data provided"
                    ),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        except IntegrityError as e:
            logger = logging.getLogger(__name__)
            logger.error(f"Database integrity error in BookingCreateAPIView: {str(e)}")
            return Response(
                {"error": "Unable to create booking due to a conflict"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(
                f"Unexpected error in BookingCreateAPIView: {str(e)}", exc_info=True
            )
            return Response(
                {"error": "An unexpected error occurred"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PropertyBookingsAPIView(APIView):
    """View to retrieve bookings for a specific property within a specific time."""

    permission_classes = [IsAuthenticated]

    def get(self, request, property_id):
        # Validate UUID format
        try:
            property_uuid = uuid.UUID(str(property_id))
        except (ValueError, TypeError, AttributeError):
            return Response(
                {"error": "Invalid property ID format. Please provide a valid UUID."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Get and validate query parameters
            start_date = parse_date(request.query_params.get("start_date"))
            end_date = parse_date(request.query_params.get("end_date"))

            # Validate that dates are provided
            if not start_date or not end_date:
                return Response(
                    {
                        "error": "Both start_date and end_date are required in the format (yyyy-mm-dd)."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validate date order
            if start_date > end_date:
                return Response(
                    {"error": "start_date cannot be later than end_date."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Check if property exists
            if not Property.objects.filter(id=property_uuid).exists():
                return Response(
                    {"error": "Property not found."}, status=status.HTTP_404_NOT_FOUND
                )

            # Filter bookings by property and overlapping date range
            bookings = Booking.objects.filter(
                property_id=property_uuid,
                checkin_date__lte=end_date,  # Check-ins start before or on end_date
                checkout_date__gte=start_date,  # Check-outs end after or on start_date
            ).order_by(
                "checkin_date"
            )  # Chronological order by check-in date

            # Serialize the bookings data with request in context
            serializer = BookingSerializer(
                bookings, many=True, context={"request": request}
            )
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class BookingPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = "page_size"
    max_page_size = 100


class PropertyBookingListAPIView(APIView):
    """
    View to retrieve all bookings for properties where user is staff.

    Supports filtering by:
    - status: Booking status (new, modified, request, cancelled, completed)
    - type: Booking type based on is_manual field (manual, ota)
    - property: Property ID to filter bookings for specific property
    """

    permission_classes = [IsAuthenticated]
    pagination_class = BookingPagination

    def get(self, request):
        # Start with base queryset for properties where user is staff
        bookings = Booking.objects.filter(property__staffs=request.user)

        # Apply filters based on query parameters
        filters = self._build_filters(request.query_params)
        if filters:
            bookings = bookings.filter(**filters)

        # Apply additional filtering for type parameter
        type_filter = request.query_params.get("type")
        if type_filter:
            bookings = self._apply_type_filter(bookings, type_filter)

        # Apply date range filters (overlap semantics)
        try:
            bookings = self._apply_date_filters(bookings, request.query_params)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        # Order by check-in date (chronological order)
        bookings = bookings.order_by("checkin_date")

        # Initialize paginator
        paginator = self.pagination_class()
        paginated_bookings = paginator.paginate_queryset(bookings, request)

        # Serialize the paginated data with request in context
        serializer = BookingSerializer(
            paginated_bookings, many=True, context={"request": request}
        )

        return paginator.get_paginated_response(serializer.data)

    def _build_filters(self, query_params):
        """Build Django ORM filters from query parameters."""
        filters = {}

        # Status filter
        status = query_params.get("status")
        if status:
            # Validate status value
            valid_statuses = [choice[0] for choice in Booking.Status.choices]
            if status.lower() in valid_statuses:
                filters["status"] = status.lower()

        # Property filter
        property_id = query_params.get("property")
        if property_id:
            try:
                # Validate UUID format
                uuid.UUID(property_id)
                filters["property_id"] = property_id
            except (ValueError, TypeError):
                # Raise validation error for invalid UUID
                raise ValueError(
                    "Invalid property ID format. Please provide a valid UUID."
                )
        return filters

    def _apply_date_filters(self, queryset, query_params):
        """
        Apply date range filtering to the queryset.

        Logic:
        - If both start_date and end_date provided: Return bookings that overlap with the date range (inclusive)
        - If only start_date provided: Return bookings with checkout_date on or after start_date (inclusive)
        - If only end_date provided: Return bookings with checkin_date on or before end_date (inclusive)

        A booking overlaps with a date range if:
        checkin_date <= end_date AND checkout_date >= start_date
        """
        start_date = query_params.get("start_date")
        end_date = query_params.get("end_date")

        # Validate date range first
        if start_date and end_date:
            parsed_start_date = parse_date(start_date)
            parsed_end_date = parse_date(end_date)

            if not parsed_start_date:
                raise ValueError(
                    "Invalid start_date format. Please use YYYY-MM-DD format."
                )
            if not parsed_end_date:
                raise ValueError(
                    "Invalid end_date format. Please use YYYY-MM-DD format."
                )
            if parsed_start_date > parsed_end_date:
                raise ValueError("start_date cannot be later than end_date.")

            # Filter bookings that overlap with the date range (inclusive)
            # Booking overlaps if: checkin_date <= end_date AND checkout_date >= start_date
            queryset = queryset.filter(
                checkin_date__lte=parsed_end_date, checkout_date__gte=parsed_start_date
            )

        elif start_date:
            parsed_start_date = parse_date(start_date)
            if not parsed_start_date:
                raise ValueError(
                    "Invalid start_date format. Please use YYYY-MM-DD format."
                )
            # Return bookings that are active on or after start_date (inclusive)
            # This means checkout_date >= start_date
            queryset = queryset.filter(checkout_date__gte=parsed_start_date)

        elif end_date:
            parsed_end_date = parse_date(end_date)
            if not parsed_end_date:
                raise ValueError(
                    "Invalid end_date format. Please use YYYY-MM-DD format."
                )
            # Return bookings that are active on or before end_date (inclusive)
            # This means checkin_date <= end_date (inclusive)
            queryset = queryset.filter(checkin_date__lte=parsed_end_date)

        return queryset

    def _apply_type_filter(self, queryset, type_filter):
        """Apply type-based filtering using is_manual field."""
        if type_filter.lower() == "manual":
            return queryset.filter(is_manual=True)
        elif type_filter.lower() == "ota":
            return queryset.filter(is_manual=False)
        return queryset


class BookingBlockViewSet(viewsets.ModelViewSet):
    queryset = BookingBlock.objects.all()
    serializer_class = BookingBlockSerializer
    permission_classes = [IsAuthenticated, BookingPermission]

    def get_queryset(self):
        """
        Limit the queryset to booking blocks associated with properties
        where the user is a staff member. Additionally, filter by a specific
        property if a 'property' query parameter is provided.
        """
        user = self.request.user
        queryset = BookingBlock.objects.filter(property__staffs=user)

        # Check for 'property' query parameter to filter by specific property
        property_id = self.request.query_params.get("property")
        if property_id:
            try:
                # Validate UUID format
                property_uuid = uuid.UUID(property_id)
                # Check if the property exists and user is a staff member
                property_obj = get_object_or_404(
                    Property, id=property_uuid, staffs=user
                )
                queryset = queryset.filter(property=property_obj)
            except (ValueError, TypeError):
                raise ValidationError(
                    {
                        "property": "Invalid UUID format. Please provide a valid property ID."
                    }
                )
            except Property.DoesNotExist:
                raise ValidationError(
                    {
                        "property": "Property not found or you don't have permission to access it."
                    }
                )
        return queryset

    def perform_create(self, serializer):
        """
        Ensure no overlap with existing blocks when creating a new block.
        Validate dates, check for existing bookings, and cancel if allowed.
        """
        start_date = serializer.validated_data.get("start_date")
        end_date = serializer.validated_data.get("end_date")
        property = serializer.validated_data.get("property")

        try:
            # Date validations
            if start_date < timezone.now().date():
                raise ValidationError(
                    {"start_date": "The start date must be today or a future date."}
                )
            if end_date < start_date:
                raise ValidationError(
                    {"end_date": "The ending date must be on or after the start date."}
                )

            # Check if the user is a staff member of the property
            if not property.staffs.filter(id=self.request.user.id).exists():
                raise ValidationError(
                    {
                        "property": "You do not have permission to create blocks for this property."
                    }
                )

            serializer.save()

            # Send notification using GeneralNotificationHandler
            handler = GeneralNotificationHandler(
                users=[self.request.user],
                title="Blocco prenotazioni creato con successo",
                message=(
                    f"È stato creato un blocco per la struttura '{property.name}' "
                    f"dal {start_date.strftime('%d/%m/%Y')} al {end_date.strftime('%d/%m/%Y')}. "
                    f"Durante questo periodo, la struttura non sarà prenotabile."
                ),
            )
            handler.send_notification()

        except ValidationError as e:
            raise e
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"Unexpected error in perform_create: {str(e)}", exc_info=True)
            raise ValidationError({"error": "An unexpected error occurred"})
        

    @action(detail=True, methods=["post"])
    def deactivate(self, request, pk=None):
        """
        Endpoint to deactivate a booking block.
        If the block is active (current date is within start_date and end_date),
        it will reactivate all rooms associated with the property.
        """
        try:
            booking_block = self.get_object()

            # Check if the user has permission to deactivate this block
            if not booking_block.property.staffs.filter(id=request.user.id).exists():
                return Response(
                    {
                        "error": "You do not have permission to deactivate this booking block."
                    },
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Check if the block is active
            if booking_block.is_active:
                # Store property and dates before deleting
                property_id = str(booking_block.property.id)
                start_date = booking_block.start_date
                end_date = booking_block.end_date
                property_name = booking_block.property.name

                # Delete the booking block
                booking_block.delete()

                # Create and send notification using GeneralNotificationHandler
                handler = GeneralNotificationHandler(
                    users=[request.user],
                    title="Blocco prenotazioni disattivato con successo",
                    message=(
                        f"Il blocco per la struttura '{property_name}' "
                        f"dal {start_date.strftime('%d/%m/%Y')} al {end_date.strftime('%d/%m/%Y')} "
                        f"è stato disattivato con successo."
                    ),
                )

                # Send notifications through websocket and database
                handler.send_notification()

                return Response(
                    {
                        "message": "Booking block deactivation process started successfully.",
                        "property_id": property_id,
                    },
                    status=status.HTTP_200_OK,
                )
            else:
                # Delete the inactive block
                booking_block.delete()

                return Response(
                    {"message": "Inactive booking block removed successfully."},
                    status=status.HTTP_200_OK,
                )

        except Exception as e:
            return Response(
                {
                    "error": f"An error occurred while deactivating the booking block: {str(e)}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )


class BookingCancellationRequestView(APIView):
    """
    API view for handling booking cancellation requests from users.

    This endpoint allows users to request cancellation of their bookings.
    It validates the booking ID, sends an email notification to the admin,
    and returns a success response.
    """

    permission_classes = [IsAuthenticated, BookingPermission]

    def post(self, request):
        """
        Handle POST requests for booking cancellation.

        For manual bookings (is_manual=True), the booking is automatically cancelled.
        For non-manual bookings, a cancellation request is sent to admin.

        Args:
            request: The HTTP request object containing booking_id and reason

        Returns:
            Response: HTTP response with success message or error details
        """
        try:
            # Initialize serializer with request data and context
            serializer = BookingCancellationRequestSerializer(
                data=request.data, context={"request": request}
            )

            # Validate the request data
            if not serializer.is_valid():
                return Response(
                    {"errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST
                )

            # Get validated data
            booking_id = serializer.validated_data["booking_id"]
            reason = serializer.validated_data.get("reason")

            # Get the booking from context (set during validation)
            booking = serializer.context.get("booking")

            # Handle manual bookings automatically
            if booking.is_manual:
                booking.status = Booking.Status.CANCELLED
                booking.save(update_fields=["status"])

                # Send notification using GeneralNotificationHandler
                handler = GeneralNotificationHandler(
                    users=[request.user],
                    title="Prenotazione Manuale Cancellata",
                    message=f"La prenotazione manuale per {booking.property.name} è stata cancellata con successo.",
                )
                handler.send_notification()

                return Response(
                    {
                        "message": "Manual booking cancelled successfully.",
                        "booking_id": str(booking_id),
                    },
                    status=status.HTTP_200_OK,
                )

            # Send email notification to admin
            email_service = EmailService()
            email_sent = email_service.send_booking_cancellation_request(
                user=request.user, booking=booking, reason=reason
            )

            if not email_sent:
                # Log the error but don't fail the request
                logger = logging.getLogger(__name__)
                logger.error(
                    f"Failed to send cancellation request email for booking {booking_id}"
                )

            # Send notification using GeneralNotificationHandler
            handler = GeneralNotificationHandler(
                users=[request.user],
                title="Richiesta di Cancellazione Inviata",
                message="La richiesta di cancellazione per la prenotazione è stata inviata. Il nostro team la esaminerà a breve.",
            )
            handler.send_notification()

            # Return success response
            return Response(
                {
                    "message": "Cancellation request submitted successfully. Our team will review your request and contact you soon.",
                    "booking_id": str(booking_id),
                },
                status=status.HTTP_200_OK,
            )

        except ValidationError as e:
            return Response({"errors": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            # Log the unexpected error
            logger = logging.getLogger(__name__)
            logger.error(
                f"Unexpected error in booking cancellation request: {str(e)}",
                exc_info=True,
            )

            return Response(
                {"errors": "An unexpected error occurred. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


from apps.booking.tasks import block_rooms_task
from services.su_api import delete_property


class BookingTestAPIView(APIView):
    """
    Test API view for booking-related operations.
    This is a placeholder for testing purposes and should be removed in production.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        # property = Property.objects.filter().first()  # Get the first property for testing
        # response_data = onboarding_task(property.id, request.user.id)
        # response_data = delete_property("99e03695")
        block = BookingBlock.objects.filter(property__staffs=request.user).first()
        response_data = block_rooms_task(block.id)
        return Response({"response": response_data}, status=status.HTTP_200_OK)
