import io
import logging

import openpyxl
from apps.booking.models import Booking
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.db import models
from django.http import HttpResponse
from django.shortcuts import render
from django.utils.dateparse import parse_date
from django.views.generic import TemplateView
from openpyxl.styles import <PERSON><PERSON><PERSON>, <PERSON>ont, PatternFill
from openpyxl.utils import get_column_letter
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

logger = logging.getLogger(__name__)

class BookingExportAPIView(APIView):
    """
    API view to export bookings to Excel.

    This view generates an Excel file with booking details and returns it as a response.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """
        Handle GET requests to generate and download the Excel file.

        The file includes booking details such as ID, property name, user name, dates, and status.
        """
        # Get all bookings for the authenticated user
        bookings = (
            Booking.objects.filter(property__staffs=request.user)
            .select_related("property", "user")
            .order_by("checkin_date")
        )  # Chronological order by check-in date

        # Create a workbook and add a worksheet
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "Bookings"

        # Define the header row
        headers = [
            "ID",
            "Property Name",
            "User Name",
            "Check-in Date",
            "Check-out Date",
            "Status",
            "Total Price",
        ]

        # Add the header row to the worksheet
        worksheet.append(headers)

        # Apply header styles
        for col in range(1, len(headers) + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(
                start_color="4F81BD", end_color="4F81BD", fill_type="solid"
            )
            cell.alignment = Alignment(horizontal="center")

        # Add booking data to the worksheet
        for booking in bookings:
            worksheet.append(
                [
                    str(booking.id),
                    booking.property.name,
                    f"{booking.user.first_name} {booking.user.last_name}",
                    booking.checkin_date.strftime("%Y-%m-%d"),
                    booking.checkout_date.strftime("%Y-%m-%d"),
                    booking.status,
                    booking.total_price,
                ]
            )

        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column = [cell for cell in column]
            max_length = max(len(str(cell.value)) for cell in column)
            adjusted_width = max_length + 2
            worksheet.column_dimensions[get_column_letter(column[0].column)].width = (
                adjusted_width
            )

        # Generate the Excel file in memory
        excel_file = io.BytesIO()
        workbook.save(excel_file)
        excel_file.seek(0)

        # Create the response with the Excel file
        response = HttpResponse(
            excel_file,
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
        response["Content-Disposition"] = 'attachment; filename="bookings.xlsx"'
        return response


class BookingExportTemplateView(TemplateView):
    """
    View for exporting booking data to Excel for a selected date range
    """

    template_name = "booking_export.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context

    def post(self, request, *args, **kwargs):
        start_date = request.POST.get("start_date")
        end_date = request.POST.get("end_date")

        # Validate dates
        try:
            start_date = parse_date(start_date)
            end_date = parse_date(end_date)

            if not start_date or not end_date:
                return render(
                    request, self.template_name, {"error": "Formato data non valido."}
                )

            if start_date > end_date:
                return render(
                    request,
                    self.template_name,
                    {
                        "error": "La data di inizio deve essere precedente alla data di fine."
                    },
                )
        except Exception as e:
            logger.error(f"Error validating dates: {str(e)}", exc_info=True)
            return render(
                request,
                self.template_name,
                {"error": "Errore nella validazione delle date."},
            )
        # Generate Excel file
        try:
            download_url = self.generate_excel_report(start_date, end_date)
            return render(request, self.template_name, {"download_url": download_url})
        except ValueError as e:
            # Handle specific value errors (like no bookings found)
            logger.warning(f"Value error generating Excel report: {str(e)}")
            return render(
                request,
                self.template_name,
                {
                    "error": "Non sono state trovate prenotazioni nel periodo selezionato."
                },
            )
        except Exception as e:
            logger.error(f"Error generating Excel report: {str(e)}", exc_info=True)
            return render(
                request,
                self.template_name,
                {
                    "error": "Si è verificato un errore durante la generazione del report."
                },
            )

    def generate_excel_report(self, start_date, end_date):
        """
        Generate an Excel report for bookings within the specified date range.

        Args:
            start_date: Start date for the report
            end_date: End date for the report

        Returns:
            str: Download URL for the generated report

        Raises:
            ValueError: If no bookings are found in the date range
        """
        # We want bookings that overlap with the selected date range
        bookings = (
            Booking.objects.filter(
                (
                    # Check-in within range
                    (
                        models.Q(checkin_date__gte=start_date)
                        & models.Q(checkin_date__lte=end_date)
                    )
                    |
                    # Check-out within range
                    (
                        models.Q(checkout_date__gte=start_date)
                        & models.Q(checkout_date__lte=end_date)
                    )
                    |
                    # Stay overlapping with range (check-in before range, check-out after range)
                    (
                        models.Q(checkin_date__lt=start_date)
                        & models.Q(checkout_date__gt=end_date)
                    )
                )
            )
            .select_related("property", "customer", "reservation_data")
            .order_by("property__name", "checkin_date")
        )
        # Check if there are any bookings
        if not bookings.exists():
            raise ValueError(
                f"Nessuna prenotazione trovata tra {start_date} e {end_date}"
            )
        # Create workbook
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "Report Prenotazioni"  # Define headers in Italian
        headers = [
            "Nome Proprietà",
            "ID Proprietà",
            "Nome Ospite",
            "Email",
            "Telefono",
            "Data Check-in",
            "Data Check-out",
            "Data Prenotazione",
            "Stato",
            "Canale",
            "Prenotazione Manuale",
            "Prezzo Lordo",
            "Prezzo Totale",
            "Deposito",
            "Tasse Totali",
            "Commissione",
            "Tipo Pagamento",
            "Adulti",
            "Bambini",
            "Neonati",
            "Totale Ospiti",
            "Note",
        ]

        # Apply header styles
        header_font = Font(bold=True)
        header_fill = PatternFill(
            start_color="CCCCCC", end_color="CCCCCC", fill_type="solid"
        )

        # Add headers to worksheet
        for col_num, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill

        # Freeze header row
        worksheet.freeze_panes = "A2"

        # Add data rows
        row_num = 2
        for booking in bookings:
            reservation = booking.reservation_data
            customer = booking.customer
            # Map channel code to readable name
            channel_name = "Unknown"
            if booking.channel_code is not None:
                channel_name = dict(booking.ChannelChoices.choices).get(
                    booking.channel_code, "Unknown"
                )

            # Get booking status display name safely
            try:
                status_display = dict(booking.Status.choices).get(
                    booking.status, str(booking.status)
                )
            except Exception:
                status_display = str(booking.status) if booking.status else ""
            row_data = [
                str(booking.property.name) if booking.property.name else "",
                str(booking.property.hotel_id) if booking.property.hotel_id else "",
                str(customer.get_full_name()) if customer else "",
                str(customer.email) if customer and customer.email else "",
                str(customer.telephone) if customer and customer.telephone else "",
                (
                    booking.checkin_date.strftime("%Y-%m-%d")
                    if booking.checkin_date
                    else ""
                ),
                (
                    booking.checkout_date.strftime("%Y-%m-%d")
                    if booking.checkout_date
                    else ""
                ),
                (
                    booking.booking_date.strftime("%Y-%m-%d %H:%M")
                    if booking.booking_date
                    else ""
                ),
                str(status_display),
                str(channel_name),
                "Sì" if booking.is_manual else "No",  # Manual booking indicator
                (
                    float(reservation.net_price)
                    if reservation and reservation.net_price
                    else 0.00
                ),  # Net price
                (
                    float(reservation.total_price)
                    if reservation and reservation.total_price
                    else 0.00
                ),  # Total price
                (
                    float(reservation.deposit)
                    if reservation and reservation.deposit
                    else 0.00
                ),  # Deposit
                (
                    float(reservation.total_tax)
                    if reservation and reservation.total_tax
                    else 0.00
                ),  # Total taxes
                (
                    float(reservation.commission_amount)
                    if reservation and reservation.commission_amount
                    else 0.00
                ),  # Commission
                (
                    str(reservation.payment_type)
                    if reservation and reservation.payment_type
                    else ""
                ),  # Payment type
                (
                    int(reservation.number_of_adults)
                    if reservation and reservation.number_of_adults is not None
                    else 0
                ),
                (
                    int(reservation.number_of_children)
                    if reservation and reservation.number_of_children is not None
                    else 0
                ),
                (
                    int(reservation.number_of_infants)
                    if reservation and reservation.number_of_infants is not None
                    else 0
                ),
                (
                    int(reservation.number_of_guests)
                    if reservation and reservation.number_of_guests is not None
                    else 0
                ),
                str(reservation.remarks) if reservation and reservation.remarks else "",
            ]

            for col_num, cell_value in enumerate(row_data, 1):
                try:
                    worksheet.cell(row=row_num, column=col_num).value = cell_value
                except ValueError as e:
                    # Handle value errors by converting problematic values to string
                    logger = logging.getLogger(__name__)
                    logger.warning(
                        f"Value error for {headers[col_num-1]}: {cell_value} - {str(e)}"
                    )
                    try:
                        # Last resort - convert to string and escape problematic characters
                        safe_value = (
                            str(cell_value).encode("unicode_escape").decode("utf-8")
                        )
                        worksheet.cell(row=row_num, column=col_num).value = safe_value
                    except Exception:
                        # If all else fails, use a placeholder
                        worksheet.cell(row=row_num, column=col_num).value = (
                            "[data error]"
                        )

            row_num += 1
        # Auto-adjust column widths
        for column in worksheet.columns:
            try:
                max_length = 0
                column_letter = get_column_letter(column[0].column)

                for cell in column:
                    if cell.value:
                        try:
                            cell_length = len(str(cell.value))
                            max_length = max(max_length, cell_length)
                        except Exception:
                            # If we can't get length, use default
                            pass

                # Add a little extra space and set a reasonable default
                adjusted_width = min(
                    max(max_length + 2, 10), 50
                )  # Min 10, max 50 characters
                worksheet.column_dimensions[column_letter].width = adjusted_width
            except Exception as e:
                # Log and continue if there's any error with a specific column
                logger.warning(f"Error adjusting column width: {str(e)}")
                continue
        # Create filename with date range
        filename = f"bookings_{start_date.strftime('%Y-%m-%d')}_to_{end_date.strftime('%Y-%m-%d')}.xlsx"
        file_path = f"booking_reports/{filename}"

        # Save to BytesIO for upload to S3
        excel_data = io.BytesIO()
        workbook.save(excel_data)
        excel_data.seek(0)

        # Ensure booking_reports directory exists in S3
        try:
            # Save to default storage (S3 if configured)
            file_path = default_storage.save(
                file_path, ContentFile(excel_data.getvalue())
            )

            # Generate download URL
            download_url = default_storage.url(file_path)
        except Exception as e:
            logger.error(f"Error saving file to S3: {str(e)}", exc_info=True)
            raise

        return download_url
