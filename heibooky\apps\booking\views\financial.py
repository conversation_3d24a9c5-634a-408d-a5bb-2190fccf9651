"""
Financial API endpoints for Heibooky payments dashboard.

This module provides API endpoints for calculating financial metrics including
pending balance, total earnings, and payment status information.
"""

import logging
from datetime import date, datetime
from decimal import Decimal

from django.db.models import Q
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from ..models import Booking, Reservation
from ..serializers import ReservationSerializer
from ..utils.payment_status import (
    ActivationFeeManager,
    FinancialCalculator,
    PaymentCycleManager,
    get_next_payment_info,
)

logger = logging.getLogger(__name__)


class PendingBalanceAPIView(APIView):
    """
    API endpoint to calculate pending balance from reservations completed in last 15 days.

    GET /booking/financial/pending-balance/

    Query Parameters:
    - property_ids: Optional comma-separated list of property IDs to filter by

    Response:
    {
        "pending_balance": "1250.75",
        "currency": "EUR",
        "calculation_period": "15 days",
        "last_updated": "2024-01-15T10:30:00Z",
        "reservations_count": 8
    }
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user

            # Parse property filter if provided
            property_filter = None
            if "property_ids" in request.query_params:
                property_ids_str = request.query_params.get("property_ids", "")
                if property_ids_str:
                    property_filter = [
                        pid.strip()
                        for pid in property_ids_str.split(",")
                        if pid.strip()
                    ]

            # Calculate pending balance
            pending_balance = FinancialCalculator.calculate_pending_balance(
                user=user, property_filter=property_filter
            )

            # Get count of reservations contributing to pending balance
            from datetime import timedelta

            fifteen_days_ago = date.today() - timedelta(days=15)

            query = Q(
                booking__property__staffs=user,
                booking__status=Booking.Status.COMPLETED,
                checkout_date__gte=fifteen_days_ago,
                checkout_date__lte=date.today(),
            )

            if property_filter:
                query &= Q(booking__property__id__in=property_filter)

            reservations_count = Reservation.objects.filter(query).count()

            return Response(
                {
                    "pending_balance": str(pending_balance),
                    "currency": "EUR",
                    "calculation_period": "15 days",
                    "last_updated": datetime.now().isoformat(),
                    "reservations_count": reservations_count,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logger.error(
                f"Failed to calculate pending balance for user {request.user.id}: {str(e)}",
                exc_info=True,
            )
            return Response(
                {"error": f"Failed to calculate pending balance: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class TotalEarningsAPIView(APIView):
    """
    API endpoint to calculate lifetime earnings from the platform.

    GET /booking/financial/total-earnings/

    Query Parameters:
    - property_ids: Optional comma-separated list of property IDs to filter by

    Response:
    {
        "total_earnings": "15750.25",
        "currency": "EUR",
        "calculation_type": "lifetime",
        "last_updated": "2024-01-15T10:30:00Z",
        "total_reservations": 125
    }
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user

            # Parse property filter if provided
            property_filter = None
            if "property_ids" in request.query_params:
                property_ids_str = request.query_params.get("property_ids", "")
                if property_ids_str:
                    property_filter = [
                        pid.strip()
                        for pid in property_ids_str.split(",")
                        if pid.strip()
                    ]

            # Calculate total earnings
            total_earnings = FinancialCalculator.calculate_total_earnings(
                user=user, property_filter=property_filter
            )

            # Get count of all completed reservations
            query = Q(
                booking__property__staffs=user, booking__status=Booking.Status.COMPLETED
            )

            if property_filter:
                query &= Q(booking__property__id__in=property_filter)

            total_reservations = Reservation.objects.filter(query).count()

            return Response(
                {
                    "total_earnings": str(total_earnings),
                    "currency": "EUR",
                    "calculation_type": "lifetime",
                    "last_updated": datetime.now().isoformat(),
                    "total_reservations": total_reservations,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(
                f"Failed to calculate total earnings for user {request.user.id}: {str(e)}",
                exc_info=True,
            )
            return Response(
                {"error": f"Failed to calculate total earnings: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PaymentStatusAPIView(APIView):
    """
    API endpoint to get payment status information for reservations.

    GET /booking/financial/payment-status/

    Query Parameters:
    - status: Optional filter by payment status ('payment_in_progress' or 'future_payment')
    - property_ids: Optional comma-separated list of property IDs to filter by
    - limit: Optional limit for number of reservations returned (default: 50)

    Response:
    {
        "payment_in_progress": {
            "count": 5,
            "total_amount": "2500.00",
            "reservations": [...]
        },
        "future_payment": {
            "count": 12,
            "total_amount": "6750.50",
            "reservations": [...]
        },
        "current_payment_cycle": {
            "start_date": "2024-01-01",
            "end_date": "2024-01-15",
            "payment_date": "2024-01-15"
        }
    }
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user

            # Parse query parameters
            status_filter = request.query_params.get("status")
            try:
                limit = int(request.query_params.get("limit", 50))
                if limit < 1 or limit > 1000:
                    limit = 50
            except (ValueError, TypeError):
                limit = 50

            property_filter = None
            if "property_ids" in request.query_params:
                property_ids_str = request.query_params.get("property_ids", "")
                if property_ids_str:
                    property_filter = [
                        pid.strip()
                        for pid in property_ids_str.split(",")
                        if pid.strip()
                    ]

            # Get current payment cycle info
            cycle_start, cycle_end = PaymentCycleManager.get_current_payment_cycle()

            response_data = {
                "current_payment_cycle": {
                    "start_date": cycle_start.isoformat(),
                    "end_date": cycle_end.isoformat(),
                    "payment_date": cycle_end.isoformat(),
                }
            }

            # If specific status requested, return only that
            if status_filter in ["payment_in_progress", "future_payment"]:
                reservations = FinancialCalculator.get_reservations_by_payment_status(
                    user=user, status=status_filter, property_filter=property_filter
                )[:limit]

                total_amount = sum(
                    r.net_total_for_owner or Decimal("0.00") for r in reservations
                )

                response_data[status_filter] = {
                    "count": len(reservations),
                    "total_amount": str(total_amount),
                    "reservations": ReservationSerializer(reservations, many=True).data,
                }
            else:
                # Return both statuses
                for payment_status in ["payment_in_progress", "future_payment"]:
                    reservations = (
                        FinancialCalculator.get_reservations_by_payment_status(
                            user=user,
                            status=payment_status,
                            property_filter=property_filter,
                        )[:limit]
                    )

                    total_amount = sum(
                        r.net_total_for_owner or Decimal("0.00") for r in reservations
                    )

                    response_data[payment_status] = {
                        "count": len(reservations),
                        "total_amount": str(total_amount),
                        "reservations": ReservationSerializer(
                            reservations, many=True
                        ).data,
                    }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(
                f"Failed to get payment status for user {request.user.id}: {str(e)}",
                exc_info=True,
            )
            return Response(
                {"error": f"Failed to get payment status: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class MonthlyEarningsAPIView(APIView):
    """
     GET /booking/financial/monthly-earnings/

    Query Parameters:
    - year: Year to calculate for (default: current year)
    - month: Month to calculate for (default: current month)
    - property_ids: Optional comma-separated list of property IDs to filter by

    Response:
    {
        "year": 2024,
        "month": 1,
        "first_cycle_earnings": "1250.75",
        "second_cycle_earnings": "2100.50",
        "total_monthly_earnings": "3351.25",
        "first_cycle_period": "01/01 - 15/01",
        "second_cycle_period": "16/01 - 31/01",
        "currency": "EUR"
    }
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user

            # Parse query parameters
            today = date.today()
            try:
                year = int(request.query_params.get("year", today.year))
                month = int(request.query_params.get("month", today.month))
                # Validate reasonable ranges
                if not (2000 <= year <= today.year + 1):
                    year = today.year
                if not (1 <= month <= 12):
                    month = today.month
            except (ValueError, TypeError):
                year = today.year
                month = today.month

            property_filter = None
            if "property_ids" in request.query_params:
                property_ids_str = request.query_params.get("property_ids", "")
                if property_ids_str:
                    property_filter = [
                        pid.strip()
                        for pid in property_ids_str.split(",")
                        if pid.strip()
                    ]

            # Calculate monthly earnings
            earnings_data = FinancialCalculator.calculate_monthly_earnings(
                user=user, year=year, month=month, property_filter=property_filter
            )

            # Add metadata
            earnings_data.update(
                {
                    "year": year,
                    "month": month,
                    "currency": "EUR",
                    "last_updated": datetime.now().isoformat(),
                }
            )

            # Convert Decimal values to strings for JSON serialization
            for key, value in earnings_data.items():
                if isinstance(value, Decimal):
                    earnings_data[key] = str(value)

            return Response(earnings_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(
                f"Failed to calculate monthly earnings for user {request.user.id}: {str(e)}",
                exc_info=True,
            )
            return Response(
                {"error": f"Failed to calculate monthly earnings: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def reservation_payment_info(request, reservation_id):
    """
    Get detailed payment information for a specific reservation.

    GET /booking/financial/reservation/{reservation_id}/payment-info/

    Response:
    {
        "reservation_id": "RES123456",
        "payment_status": "payment_in_progress",
        "payment_status_display": "Payment in progress",
        "payment_date": "2024-01-15",
        "payment_cycle_start": "2024-01-01",
        "payment_cycle_end": "2024-01-15",
        "days_until_payment": 3,
        "is_current_cycle": true,
        "net_amount": "450.75"
    }
    """
    try:
        # Get reservation
        reservation = Reservation.objects.get(
            id=reservation_id, booking__property__staffs=request.user
        )

        if not reservation.checkout_date:
            return Response(
                {"error": "Reservation has no checkout date"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get payment information
        payment_info = get_next_payment_info(reservation.checkout_date)
        payment_status = reservation.get_payment_status()

        return Response(
            {
                "reservation_id": reservation.id,
                "payment_status": payment_status,
                "payment_status_display": (
                    "Payment in progress"
                    if payment_status == "payment_in_progress"
                    else "Future payment"
                ),
                "payment_date": payment_info["payment_date"].isoformat(),
                "payment_cycle_start": payment_info["payment_cycle_start"].isoformat(),
                "payment_cycle_end": payment_info["payment_cycle_end"].isoformat(),
                "days_until_payment": payment_info["days_until_payment"],
                "is_current_cycle": payment_info["is_current_cycle"],
                "net_amount": str(reservation.net_total_for_owner or Decimal("0.00")),
                "checkout_date": reservation.checkout_date.isoformat(),
            },
            status=status.HTTP_200_OK,
        )

    except Reservation.DoesNotExist:
        return Response(
            {"error": "Reservation not found or access denied"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        logger.error(
            f"Failed to get payment info for reservation {reservation_id}: {str(e)}",
            exc_info=True,
        )
        return Response(
            {"error": f"Failed to get payment info: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


class ComprehensiveFinancialSummaryAPIView(APIView):
    """
    API endpoint to get comprehensive financial summary including all metrics.

    GET /booking/financial/summary/

    Query Parameters:
    - property_ids: Optional comma-separated list of property IDs to filter by

    Response:
    {
        "pending_balance": "1250.75",
        "total_earnings": "15750.25",
        "commission_breakdown": {
            "total_ota_commission": "2300.50",
            "total_heibooky_commission": "1200.00",
            "total_payment_fees": "450.25",
            "total_vat": "805.30",
            "total_taxes": "3300.00",
            "total_activation_fees": "450.00",
            "total_cleaning_costs": "800.00"
        },
        "next_payment_info": {
            "next_payment_date": "2024-01-15",
            "days_until_payment": "3",
            "current_cycle": "1st-15th January 2024"
        },
        "currency": "EUR",
        "last_updated": "2024-01-12T10:30:00Z"
    }
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user

            # Parse property filter if provided
            property_filter = None
            if "property_ids" in request.query_params:
                property_ids_str = request.query_params.get("property_ids", "")
                if property_ids_str:
                    property_filter = [
                        pid.strip()
                        for pid in property_ids_str.split(",")
                        if pid.strip()
                    ]

            # Get comprehensive financial summary
            summary = FinancialCalculator.get_financial_summary(
                user=user, property_filter=property_filter
            )

            return Response(summary, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(
                f"Failed to generate financial summary for user {request.user.id}: {str(e)}",
                exc_info=True,
            )
            return Response(
                {"error": f"Failed to generate financial summary: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ActivationFeeStatusAPIView(APIView):
    """
    API endpoint to get activation fee status for properties.

    GET /booking/financial/activation-fee-status/

    Query Parameters:
    - property_id: Required property ID to check activation fee status for

    Response:
    {
        "activation_fee_active": true,
        "original_fee_amount": "150.00",
        "remaining_fee_amount": "75.00",
        "total_recovered_amount": "75.00",
        "recovery_percentage": 50.0,
        "is_fully_recovered": false,
        "recovery_started_at": "2024-01-01T10:00:00Z",
        "fully_recovered_at": null
    }
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user
            property_id = request.query_params.get("property_id")

            if not property_id:
                return Response(
                    {"error": "property_id parameter is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get property instance
            try:
                from apps.stay.models import Property

                property_instance = Property.objects.get(id=property_id, staffs=user)
            except Property.DoesNotExist:
                return Response(
                    {"error": "Property not found or access denied"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get activation fee status
            status_info = ActivationFeeManager.get_activation_fee_status(
                property_instance, user
            )

            return Response(status_info, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(
                f"Failed to get activation fee status for user {request.user.id}: {str(e)}",
                exc_info=True,
            )
            return Response(
                {"error": f"Failed to get activation fee status: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CommissionBreakdownAPIView(APIView):
    """
    API endpoint to get detailed commission breakdown.

    GET /booking/financial/commission-breakdown/

    Query Parameters:
    - property_ids: Optional comma-separated list of property IDs to filter by
    - period: Optional period filter ('last_30_days', 'last_90_days', 'ytd', 'all_time')

    Response:
    {
        "period": "last_30_days",
        "breakdown": {
            "heibooky_properties": {
                "total_ota_commission": "1200.00",
                "total_heibooky_commission": "640.00",
                "total_payment_fees": "280.00",
                "total_vat": "404.80",
                "total_substitute_tax": "1680.00",
                "total_activation_fees": "300.00"
            },
            "domorent_properties": {
                "total_domorent_commission": "800.00",
                "total_cleaning_costs": "400.00",
                "total_payment_fees": "120.00",
                "total_vat": "202.40",
                "total_flat_tax": "1260.00"
            },
            "totals": {
                "total_commission": "2840.00",
                "total_fees": "400.00",
                "total_taxes": "3547.20",
                "grand_total": "6787.20"
            }
        },
        "currency": "EUR",
        "last_updated": "2024-01-12T10:30:00Z"
    }
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user

            # Parse property filter if provided
            property_filter = None
            if "property_ids" in request.query_params:
                property_ids_str = request.query_params.get("property_ids", "")
                if property_ids_str:
                    property_filter = [
                        pid.strip()
                        for pid in property_ids_str.split(",")
                        if pid.strip()
                    ]

            # Parse period filter
            period = request.query_params.get("period", "all_time")
            valid_periods = ["last_30_days", "last_90_days", "ytd", "all_time"]
            if period not in valid_periods:
                period = "all_time"

            # Calculate commission breakdown
            breakdown = FinancialCalculator.calculate_commission_breakdown(
                user=user, property_filter=property_filter
            )

            # Convert Decimal values to strings
            breakdown_str = {k: str(v) for k, v in breakdown.items()}

            return Response(
                {
                    "period": period,
                    "breakdown": breakdown_str,
                    "currency": "EUR",
                    "last_updated": datetime.now().isoformat(),
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(
                f"Failed to calculate commission breakdown for user {request.user.id}: {str(e)}",
                exc_info=True,
            )
            return Response(
                {"error": f"Failed to calculate commission breakdown: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def payment_cycle_info(request):
    """
    Get current payment cycle information.

    GET /booking/financial/payment-cycle-info/

    Response:
    {
        "current_cycle": {
            "start_date": "2024-01-01",
            "end_date": "2024-01-15",
            "payment_date": "2024-01-15",
            "description": "1st-15th January 2024"
        },
        "next_cycle": {
            "start_date": "2024-01-16",
            "end_date": "2024-01-31",
            "payment_date": "2024-01-31",
            "description": "16th-31st January 2024"
        },
        "days_until_next_payment": 3
    }
    """
    try:
        current_cycle_start, current_cycle_end = (
            PaymentCycleManager.get_current_payment_cycle()
        )

        # Calculate next cycle
        if date.today().day <= 15:
            # Current is 1st-15th, next is 16th-end
            import calendar

            last_day = calendar.monthrange(date.today().year, date.today().month)[1]
            next_cycle_start = date(date.today().year, date.today().month, 16)
            next_cycle_end = date(date.today().year, date.today().month, last_day)
        else:
            # Current is 16th-end, next is 1st-15th of next month
            if date.today().month == 12:
                next_month = 1
                next_year = date.today().year + 1
            else:
                next_month = date.today().month + 1
                next_year = date.today().year

            next_cycle_start = date(next_year, next_month, 1)
            next_cycle_end = date(next_year, next_month, 15)

        days_until_payment = (current_cycle_end - date.today()).days

        return Response(
            {
                "current_cycle": {
                    "start_date": current_cycle_start.strftime("%Y-%m-%d"),
                    "end_date": current_cycle_end.strftime("%Y-%m-%d"),
                    "payment_date": current_cycle_end.strftime("%Y-%m-%d"),
                    "description": f"{current_cycle_start.day}{('st' if current_cycle_start.day == 1 else 'th')}-{current_cycle_end.day}{('th' if current_cycle_end.day != 1 else 'st')} {current_cycle_start.strftime('%B %Y')}",
                },
                "next_cycle": {
                    "start_date": next_cycle_start.strftime("%Y-%m-%d"),
                    "end_date": next_cycle_end.strftime("%Y-%m-%d"),
                    "payment_date": next_cycle_end.strftime("%Y-%m-%d"),
                    "description": f"{next_cycle_start.day}{('st' if next_cycle_start.day == 1 else 'th')}-{next_cycle_end.day}{('th' if next_cycle_end.day != 1 else 'st')} {next_cycle_start.strftime('%B %Y')}",
                },
                "days_until_next_payment": max(0, days_until_payment),
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logger.error(f"Failed to get payment cycle info: {str(e)}", exc_info=True)
        return Response(
            {"error": f"Failed to get payment cycle info: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
