import json
import logging

from apps.booking.api import process_push_reservation
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

# Initialize logger for webhook
webhook_logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name="dispatch")
class SuReservationWebhookView(APIView):
    """
    Webhook endpoint to receive push notifications from SU Channel Manager.

    This endpoint handles incoming reservation data pushed by SU's Channel Manager
    following their Push API method. It processes individual reservations and
    sends acknowledgment responses as required.
    """

    permission_classes = []  # No authentication required for webhook

    def post(self, request, *args, **kwargs):
        """
        Handle incoming reservation push notifications from SU Channel Manager.

        According to SU documentation:
        - Su pushes one booking at a time to this endpoint
        - We must acknowledge the reservation notification upon successful processing
        - If acknowledgment is not received, <PERSON> will re-push at 10 min intervals
        - After 3 failed attempts, Su will send email notification
        """
        webhook_logger.info(
            "Received reservation push notification from SU Channel Manager"
        )

        try:
            # Parse JSON payload
            if not request.body:
                webhook_logger.error("Empty request body received")
                return Response(
                    {"status": "error", "message": "Empty request body"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                payload = json.loads(request.body)
            except json.JSONDecodeError as e:
                webhook_logger.error(f"Invalid JSON payload: {str(e)}")
                return Response(
                    {"status": "error", "message": "Invalid JSON payload"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            reservations = payload.get("reservations", [])

            # Process the reservation
            result = process_push_reservation(reservations)
            if result["status"] == "error":
                webhook_logger.error(
                    f"Failed to process reservation: {result['message']}"
                )
                return Response(
                    {"status": "error", "message": result["message"]},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Return response in the exact format required by SU Channel Manager
            if not reservations or not reservations[0].get("reservation_notif_id"):
                webhook_logger.error("Missing reservation_notif_id in payload")
                return Response(
                    {"status": "error", "message": "Missing reservation_notif_id"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            notif_id = reservations[0]["reservation_notif_id"]
            return Response(
                {"reservation_notif": {"reservation_notif_id": [notif_id]}},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            webhook_logger.error(
                f"Unexpected error processing reservation webhook: {str(e)}",
                exc_info=True,
            )
            return Response(
                {"status": "error", "message": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
