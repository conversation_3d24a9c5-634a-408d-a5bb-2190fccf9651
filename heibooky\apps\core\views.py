import logging

from django.http import JsonResponse
from django.views import View

logger = logging.getLogger(__name__)


class HealthCheckView(View):
    """Simple health check endpoint."""

    def get(self, request):
        """Return health status."""
        try:
            # Check database connectivity
            from django.db import connection

            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")

            # Check Redis/Celery connectivity
            from django.core.cache import cache

            cache.set("health_check", "ok", timeout=30)

            return JsonResponse(
                {"status": "healthy", "message": "Application is running"}
            )
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return JsonResponse({"status": "unhealthy", "message": str(e)}, status=500)
