import json

from apps.integrations.models import (
    <PERSON>ekinAPILog,
    ChekinConfig,
    DownloadableTemplate,
    Invoice,
    Notification,
    Payout,
    StripeCustomer,
    SUAPIActionLog,
)
from django.contrib import admin
from django.utils.html import format_html


@admin.register(SUAPIActionLog)
class SUAPIActionLogAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "property_id",
        "action",
        "description",
        "status",
        "timestamp",
    )
    list_filter = ("action", "status", "timestamp")
    search_fields = ("user__name", "property_id", "description")
    readonly_fields = (
        "user",
        "property_id",
        "action",
        "description",
        "status",
        "timestamp",
        "details",
    )
    date_hierarchy = "timestamp"
    ordering = ["-timestamp"]

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "user",
                    "property_id",
                    "action",
                    "description",
                    "status",
                    "timestamp",
                )
            },
        ),
        (
            "Additional Details",
            {
                "fields": ("details",),
            },
        ),
    )

    def has_add_permission(self, request):
        # Disable adding new logs from the admin interface
        return False


class PayoutInline(admin.TabularInline):
    """
    Inline view for Payouts linked to a Customer.
    """

    model = Payout
    extra = 0  # No extra empty rows
    fields = (
        "stripe_payment_intent_id",
        "amount",
        "currency",
        "status",
        "created_at",
        "updated_at",
    )
    readonly_fields = ("created_at", "updated_at")
    can_delete = False
    show_change_link = True


class InvoiceInline(admin.TabularInline):
    """
    Inline view for Invoices linked to a Payout.
    """

    model = Invoice
    extra = 0  # No extra empty rows
    fields = ("pdf_file", "created_at")
    readonly_fields = (
        "pdf_file",
        "created_at",
        "digithera_reference",
        "sdi_status",
        "progressive_number",
    )
    can_delete = False
    show_change_link = True


@admin.register(StripeCustomer)
class StripeCustomerAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Stripe Customer model.
    """

    list_display = ("user", "stripe_customer_id", "created_at")
    search_fields = ("user__email", "stripe_customer_id")
    list_filter = ("created_at",)
    readonly_fields = ("created_at",)
    inlines = [PayoutInline]
    ordering = ("-created_at",)  # Sort by most recently created


@admin.register(Payout)
class PaymentAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Payout model.
    """

    list_display = (
        "customer",
        "stripe_payment_intent_id",
        "amount",
        "currency",
        "status",
        "created_at",
        "updated_at",
    )
    search_fields = ("stripe_payment_intent_id", "customer__user__email")
    list_filter = ("status", "currency", "created_at")
    readonly_fields = ("created_at", "updated_at")
    inlines = [InvoiceInline]
    ordering = ("-created_at",)  # Sort by most recently created


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Invoice model.
    """

    list_display = ("payout", "pdf_file", "created_at")
    search_fields = ("payout__stripe_payment_intent_id", "progressive_number")
    list_filter = ("created_at",)
    readonly_fields = ("created_at",)
    ordering = ("-created_at",)  # Sort by most recently created


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Notification model.
    """

    list_display = ("user", "title", "is_read", "created_at")
    search_fields = ("user__name", "title", "message")
    list_filter = ("is_read", "created_at")
    readonly_fields = ("created_at",)
    ordering = ("-created_at",)


class DownloadableTemplateAdmin(admin.ModelAdmin):
    list_display = ("title", "category", "created_at")
    search_fields = ("title", "description")
    list_filter = ("category", "created_at")
    readonly_fields = ("created_at",)
    ordering = ("-created_at",)


admin.site.register(DownloadableTemplate, DownloadableTemplateAdmin)


@admin.register(ChekinConfig)
class ChekinConfigAdmin(admin.ModelAdmin):
    """
    Admin configuration for the ChekinConfig model.
    """

    list_display = (
        "property",
        "is_enabled",
        "istat_enabled",
        "alloggati_enabled",
        "chekin_enabled",
        "last_sync",
        "updated_at",
    )
    list_filter = (
        "is_enabled",
        "istat_enabled",
        "alloggati_enabled",
        "chekin_enabled",
        "chekin_environment",
        "updated_at",
    )
    search_fields = ("property__name",)
    readonly_fields = ("created_at", "updated_at", "last_sync", "chekin_last_sync")
    ordering = ("-updated_at",)

    fieldsets = (
        (
            "General Settings",
            {"fields": ("property", "is_enabled", "created_at", "updated_at")},
        ),
        (
            "Integration Settings",
            {"fields": ("istat_enabled", "alloggati_enabled", "chekin_enabled")},
        ),
        (
            "Chekin Configuration",
            {
                "fields": (
                    "chekin_api_key",
                    "chekin_environment",
                    "chekin_last_sync",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Synchronization",
            {"fields": ("last_sync", "next_sync"), "classes": ("collapse",)},
        ),
    )


@admin.register(ChekinAPILog)
class ChekinAPILogAdmin(admin.ModelAdmin):
    """
    Admin configuration for the ChekinAPILog model.
    """

    list_display = (
        "property",
        "action_type",
        "status",
        "booking_id",
        "chekin_reservation_id",
        "http_status_code",
        "response_time_ms",
        "environment",
        "created_at",
    )
    list_filter = (
        "action_type",
        "status",
        "environment",
        "http_status_code",
        "created_at",
    )
    search_fields = (
        "property__name",
        "booking_id",
        "chekin_reservation_id",
        "error_message",
    )
    readonly_fields = (
        "property",
        "booking_id",
        "chekin_reservation_id",
        "action_type",
        "status",
        "request_payload",
        "response_data",
        "error_message",
        "http_status_code",
        "response_time_ms",
        "retry_count",
        "environment",
        "created_at",
    )
    date_hierarchy = "created_at"
    ordering = ["-created_at"]
    # Avoid N+1 on property in changelist
    list_select_related = ("property",)
    readonly_fields = (
        "property",
        "booking_id",
        "chekin_reservation_id",
        "action_type",
        "status",
        "request_payload",
        "response_data",
        "request_payload_preview",
        "response_data_preview",
        "error_message",
        "http_status_code",
        "response_time_ms",
        "retry_count",
        "environment",
        "created_at",
    )

    fieldsets = (
        (
            "Basic Information",
            {
                "fields": (
                    "property",
                    "booking_id",
                    "chekin_reservation_id",
                    "action_type",
                    "status",
                    "environment",
                    "created_at",
                )
            },
        ),
        (
            "Request/Response Details",
            {
                "fields": (
                    "request_payload_preview",
                    "response_data_preview",
                    "http_status_code",
                    "response_time_ms",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Error Information",
            {"fields": ("error_message", "retry_count"), "classes": ("collapse",)},
        ),
    )

    def has_add_permission(self, request):
        # Disable adding new logs from the admin interface
        return False

    def has_change_permission(self, request, obj=None):
        # Disable editing logs
        return False

    def has_delete_permission(self, request, obj=None):
        # Disable deleting logs
        return False

    # Read-only, compact previews for large JSON fields to avoid heavy rendering
    def _json_preview(self, data, max_chars=2000):
        try:
            if data in (None, "", {}, []):
                return "-"
            # Try to pretty print JSON
            text = None
            if isinstance(data, (dict, list)):
                text = json.dumps(data, indent=2, ensure_ascii=False)
            else:
                # Attempt to parse string payloads
                try:
                    parsed = json.loads(str(data))
                    text = json.dumps(parsed, indent=2, ensure_ascii=False)
                except Exception:
                    text = str(data)
            if len(text) > max_chars:
                text = text[:max_chars] + "\n... (truncated)"
            return format_html(
                "<pre style='white-space:pre-wrap; max-height:24em; overflow:auto; margin:0'>{}</pre>",
                text,
            )
        except Exception as e:
            return f"Error rendering JSON: {e}"

    def request_payload_preview(self, obj):
        return self._json_preview(getattr(obj, "request_payload", None))

    def response_data_preview(self, obj):
        return self._json_preview(getattr(obj, "response_data", None))

    request_payload_preview.short_description = "Request payload"
    response_data_preview.short_description = "Response data"
