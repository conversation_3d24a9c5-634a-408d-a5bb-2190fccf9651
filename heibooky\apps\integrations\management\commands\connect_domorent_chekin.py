"""
Connect Domorent properties and rooms to Chekin.

This command ensures that all onboarded Domorent properties have corresponding
Chekin housings (and rooms) created and linked. If the Chekin IDs already
exist, the command can optionally perform updates instead of creation.

Usage examples:
    # Connect all onboarded Domorent properties and rooms (create only if missing)
    python manage.py connect_domorent_chekin

    # Connect a specific property by ID
    python manage.py connect_domorent_chekin --property-id=<uuid>

    # Include updates for existing housings/rooms
    python manage.py connect_domorent_chekin --update

    # Dry run (no API calls) to see what would be done
    python manage.py connect_domorent_chekin --dry-run

    # Only housings (skip rooms)
    python manage.py connect_domorent_chekin --only-housings

    # Only rooms (requires properties already connected)
    python manage.py connect_domorent_chekin --only-rooms
"""

import logging
from typing import Optional

from apps.integrations.tasks.chekin import get_or_create_chekin_config
from apps.stay.models import Property, Room
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from services.chekin.chekin_mapper import ChekinDataMapper
from services.chekin.chekin_service import ChekinAPIError, ChekinAPIService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Connect onboarded Domorent properties and rooms to Chekin"

    def add_arguments(self, parser):
        parser.add_argument(
            "--property-id", type=str, help="Target a single property UUID"
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show actions without calling the API",
        )
        parser.add_argument(
            "--update",
            action="store_true",
            help="If IDs already exist, also send updates to Chekin",
        )
        exclusive = parser.add_mutually_exclusive_group()
        exclusive.add_argument(
            "--only-housings", action="store_true", help="Connect housings only"
        )
        exclusive.add_argument(
            "--only-rooms", action="store_true", help="Connect rooms only"
        )

    def handle(self, *args, **options):
        prop_id: Optional[str] = options.get("property_id")
        dry_run: bool = options.get("dry_run", False)
        do_update: bool = options.get("update", False)
        only_housings: bool = options.get("only_housings", False)
        only_rooms: bool = options.get("only_rooms", False)

        if only_housings and only_rooms:
            raise CommandError(
                "--only-housings and --only-rooms cannot be used together"
            )

        qs = Property.objects.filter(
            is_domorent=True, is_onboarded=True, is_active=True
        )
        if prop_id:
            qs = qs.filter(id=prop_id)
            if not qs.exists():
                raise CommandError(
                    f"Property with ID {prop_id} not found or not eligible"
                )

        total = qs.count()
        connected = 0
        updated = 0
        room_connected = 0
        room_updated = 0
        skipped = 0
        errors = 0

        self.stdout.write(
            self.style.SUCCESS(
                f"Starting connect_domorent_chekin (properties={total}) "
                + ("[DRY RUN]" if dry_run else "")
            )
        )

        for prop in qs.select_related("location"):
            try:
                self.stdout.write("")
                self.stdout.write(
                    self.style.MIGRATE_HEADING(f"Property {prop.id} - {prop.name}")
                )

                # Ensure configuration exists and is valid
                config = get_or_create_chekin_config(prop)
                if not config.is_chekin_configured():
                    self.stdout.write(
                        self.style.WARNING("  Skipping: Chekin not configured")
                    )
                    skipped += 1
                    continue

                if not ChekinDataMapper.validate_property_for_chekin(prop):
                    self.stdout.write(
                        self.style.ERROR("  Error: Missing required property fields")
                    )
                    errors += 1
                    continue

                service = ChekinAPIService(
                    api_key=config.get_chekin_api_key(),
                    environment=config.chekin_environment,
                )

                # 1) Housing
                if not only_rooms:
                    if not prop.chekin_housing_id:
                        self._create_housing(prop, service, dry_run)
                        connected += 1
                    elif do_update:
                        self._update_housing(prop, service, dry_run)
                        updated += 1
                    else:
                        self.stdout.write(
                            "  Housing: already connected (use --update to update)"
                        )

                # 2) Rooms
                if not only_housings:
                    if not prop.chekin_housing_id and not dry_run:
                        self.stdout.write(
                            self.style.WARNING(
                                "  Skipping rooms: housing not connected"
                            )
                        )
                    else:
                        room_qs = prop.rooms.filter(
                            is_onboarded=True, is_active=True
                        ).select_related("property")
                        self.stdout.write(f"  Rooms to process: {room_qs.count()}")
                        for room in room_qs:
                            try:
                                if not room.chekin_room_id:
                                    self._create_room(prop, room, service, dry_run)
                                    room_connected += 1
                                elif do_update:
                                    self._update_room(prop, room, service, dry_run)
                                    room_updated += 1
                                else:
                                    self.stdout.write(
                                        f"    Room {room.id}: already connected (use --update to update)"
                                    )
                            except Exception as re:
                                errors += 1
                                logger.exception("Room processing error")
                                self.stdout.write(
                                    self.style.ERROR(
                                        f"    Room {room.id}: error - {re}"
                                    )
                                )

                # Mark sync timestamp on config
                if not dry_run:
                    config.chekin_last_sync = timezone.now()
                    config.save(update_fields=["chekin_last_sync"])

            except Exception as e:
                errors += 1
                logger.exception("Property processing error")
                self.stdout.write(self.style.ERROR(f"  Error: {e}"))

        # Summary
        self.stdout.write("\n" + "=" * 60)
        self.stdout.write(self.style.SUCCESS("Completed connect_domorent_chekin"))
        self.stdout.write(
            f"Housings created: {connected}, updated: {updated}, rooms created: {room_connected}, rooms updated: {room_updated}, skipped: {skipped}, errors: {errors}"
        )

    # ---- helpers ---------------------------------------------------------

    def _create_housing(self, prop: Property, service: ChekinAPIService, dry_run: bool):
        self.stdout.write("  Housing: create")
        if dry_run:
            return
        housing_data = ChekinDataMapper.map_property_to_chekin_housing(prop)
        try:
            resp = service.create_housing(housing_data)
        except ChekinAPIError as e:
            # If external_id already exists, link to existing housing
            if "external_id" in str(e) and "already exists" in str(e):
                existing = service.find_housing_by_external_id(
                    housing_data.get("external_id")
                )
                if existing and existing.get("id"):
                    prop.chekin_housing_id = existing["id"]
                    prop.save(update_fields=["chekin_housing_id"])
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"  Linked to existing housing: {existing['id']}"
                        )
                    )
                    return
            raise
        if resp.get("id"):
            prop.chekin_housing_id = resp["id"]
            prop.save(update_fields=["chekin_housing_id"])
            self.stdout.write(self.style.SUCCESS(f"  Created housing: {resp['id']}"))
        else:
            self.stdout.write(self.style.WARNING("  No housing ID returned"))

    def _update_housing(self, prop: Property, service: ChekinAPIService, dry_run: bool):
        self.stdout.write("  Housing: update")
        if dry_run:
            return
        payload = ChekinDataMapper.map_property_to_chekin_housing(prop)
        # Service strips external_id on PATCH
        service.update_housing(prop.chekin_housing_id, payload)
        self.stdout.write(self.style.SUCCESS("  Updated housing"))

    def _create_room(
        self, prop: Property, room: Room, service: ChekinAPIService, dry_run: bool
    ):
        self.stdout.write(f"    Room {room.id}: create")
        if dry_run:
            return
        room_data = ChekinDataMapper.map_room_to_chekin_room(
            room_instance=room, chekin_housing_id=prop.chekin_housing_id
        )
        try:
            resp = service.create_room(room_data)
        except ChekinAPIError as e:
            if "external_id" in str(e) and "already exists" in str(e):
                existing = service.find_room_by_external_id(
                    room_data.get("external_id")
                )
                if existing and existing.get("id"):
                    room.chekin_room_id = existing["id"]
                    room.save(update_fields=["chekin_room_id"])
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"    Linked to existing room: {existing['id']}"
                        )
                    )
                    return
            raise
        if resp.get("id"):
            room.chekin_room_id = resp["id"]
            room.save(update_fields=["chekin_room_id"])
            self.stdout.write(self.style.SUCCESS(f"    Created room: {resp['id']}"))
        else:
            self.stdout.write(self.style.WARNING("    No room ID returned"))

    def _update_room(
        self, prop: Property, room: Room, service: ChekinAPIService, dry_run: bool
    ):
        self.stdout.write(f"    Room {room.id}: update")
        if dry_run:
            return
        payload = ChekinDataMapper.map_room_to_chekin_room(
            room_instance=room, chekin_housing_id=prop.chekin_housing_id
        )
        service.update_room(room.chekin_room_id, payload)
        self.stdout.write(self.style.SUCCESS("    Updated room"))
