# Generated by Django 5.2.4 on 2025-08-09 12:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("integrations", "0002_initial"),
        ("stay", "0006_property_chekin_housing_id_room_chekin_room_id"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="suapiactionlog",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="su_api_logs",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.CreateModel(
            name="ChekinAPILog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "booking_id",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Internal booking ID",
                        max_length=255,
                        null=True,
                        verbose_name="Booking ID",
                    ),
                ),
                (
                    "chekin_reservation_id",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Chekin reservation ID",
                        max_length=255,
                        null=True,
                        verbose_name="Chekin Reservation ID",
                    ),
                ),
                (
                    "chekin_housing_id",
                    models.CharField(
                        blank=True,
                        help_text="Chekin housing ID",
                        max_length=255,
                        null=True,
                        verbose_name="Chekin Housing ID",
                    ),
                ),
                (
                    "chekin_room_id",
                    models.CharField(
                        blank=True,
                        help_text="Chekin room ID",
                        max_length=255,
                        null=True,
                        verbose_name="Chekin Room ID",
                    ),
                ),
                (
                    "room_id",
                    models.CharField(
                        blank=True,
                        help_text="Internal room ID",
                        max_length=255,
                        null=True,
                        verbose_name="Room ID",
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("reservation_create", "Create Reservation"),
                            ("reservation_update", "Update Reservation"),
                            ("reservation_delete", "Delete Reservation"),
                            ("test", "Test Connection"),
                            ("housing_create", "Create Housing"),
                            ("housing_update", "Update Housing"),
                            ("housing_delete", "Delete Housing"),
                            ("room_create", "Create Room"),
                            ("room_update", "Update Room"),
                            ("room_delete", "Delete Room"),
                        ],
                        max_length=20,
                        verbose_name="Action Type",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("success", "Success"),
                            ("failed", "Failed"),
                            ("retry", "Retry"),
                        ],
                        max_length=10,
                        verbose_name="Status",
                    ),
                ),
                (
                    "request_payload",
                    models.JSONField(
                        blank=True,
                        help_text="JSON payload sent to Chekin API",
                        null=True,
                        verbose_name="Request Payload",
                    ),
                ),
                (
                    "response_data",
                    models.JSONField(
                        blank=True,
                        help_text="JSON response from Chekin API",
                        null=True,
                        verbose_name="Response Data",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True,
                        help_text="Error message if the request failed",
                        null=True,
                        verbose_name="Error Message",
                    ),
                ),
                (
                    "http_status_code",
                    models.IntegerField(
                        blank=True,
                        help_text="HTTP status code from Chekin API",
                        null=True,
                        verbose_name="HTTP Status Code",
                    ),
                ),
                (
                    "response_time_ms",
                    models.IntegerField(
                        blank=True,
                        help_text="API response time in milliseconds",
                        null=True,
                        verbose_name="Response Time (ms)",
                    ),
                ),
                (
                    "retry_count",
                    models.IntegerField(
                        default=0,
                        help_text="Number of retry attempts",
                        verbose_name="Retry Count",
                    ),
                ),
                (
                    "environment",
                    models.CharField(
                        choices=[("sandbox", "Sandbox"), ("production", "Production")],
                        help_text="Chekin environment used",
                        max_length=20,
                        verbose_name="Environment",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "property",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="chekin_logs",
                        to="stay.property",
                        verbose_name="Property",
                    ),
                ),
            ],
            options={
                "verbose_name": "Chekin API Log",
                "verbose_name_plural": "Chekin API Logs",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ChekinConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_enabled",
                    models.BooleanField(
                        default=False, verbose_name="Online Check-in Enabled"
                    ),
                ),
                (
                    "istat_enabled",
                    models.BooleanField(
                        default=False, verbose_name="ISTAT Reporting Enabled"
                    ),
                ),
                (
                    "alloggati_enabled",
                    models.BooleanField(
                        default=False, verbose_name="Alloggati Web Reporting Enabled"
                    ),
                ),
                (
                    "chekin_enabled",
                    models.BooleanField(
                        default=False, verbose_name="Chekin Reporting Enabled"
                    ),
                ),
                (
                    "chekin_api_key",
                    models.CharField(
                        blank=True,
                        help_text="API key for Chekin authentication",
                        max_length=255,
                        null=True,
                        verbose_name="Chekin API Key",
                    ),
                ),
                (
                    "chekin_environment",
                    models.CharField(
                        choices=[("sandbox", "Sandbox"), ("production", "Production")],
                        default="sandbox",
                        help_text="Environment to use for Chekin API calls",
                        max_length=20,
                        verbose_name="Chekin Environment",
                    ),
                ),
                (
                    "chekin_last_sync",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name="Chekin Last Synchronization",
                    ),
                ),
                (
                    "last_sync",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Synchronization"
                    ),
                ),
                (
                    "next_sync",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        verbose_name="Next Scheduled Synchronization",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "property",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="online_checkin",
                        to="stay.property",
                        verbose_name="Property",
                    ),
                ),
            ],
            options={
                "verbose_name": "Property Online Check-in",
                "verbose_name_plural": "Property Online Check-ins",
                "ordering": ["-updated_at"],
            },
        ),
        migrations.DeleteModel(
            name="PropertyOnlineCheckIn",
        ),
        migrations.AddIndex(
            model_name="chekinapilog",
            index=models.Index(
                fields=["property", "created_at"], name="integration_propert_c8bb86_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chekinapilog",
            index=models.Index(
                fields=["booking_id"], name="integration_booking_7d774b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chekinapilog",
            index=models.Index(
                fields=["chekin_reservation_id"], name="integration_chekin__0d6f28_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chekinapilog",
            index=models.Index(
                fields=["chekin_housing_id"], name="integration_chekin__cf018e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chekinapilog",
            index=models.Index(
                fields=["chekin_room_id"], name="integration_chekin__00a603_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chekinapilog",
            index=models.Index(
                fields=["room_id"], name="integration_room_id_009a63_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chekinapilog",
            index=models.Index(
                fields=["status", "created_at"], name="integration_status_147f2c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="chekinapilog",
            index=models.Index(
                fields=["action_type", "created_at"],
                name="integration_action__ba303d_idx",
            ),
        ),
    ]
