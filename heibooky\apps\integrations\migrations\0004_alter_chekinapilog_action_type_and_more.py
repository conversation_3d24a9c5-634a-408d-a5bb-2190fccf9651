# Generated by Django 5.2.4 on 2025-08-13 14:47

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "integrations",
            "0003_alter_suapiactionlog_user_chekinapilog_chekinconfig_and_more",
        ),
        ("stay", "0006_property_chekin_housing_id_room_chekin_room_id"),
    ]

    operations = [
        migrations.AlterField(
            model_name="chekinapilog",
            name="action_type",
            field=models.CharField(
                choices=[
                    ("create", "Create Reservation"),
                    ("update", "Update Reservation"),
                    ("delete", "Delete Reservation"),
                    ("test", "Test Connection"),
                    ("housing_create", "Create Housing"),
                    ("housing_update", "Update Housing"),
                    ("housing_delete", "Delete Housing"),
                    ("room_create", "Create Room"),
                    ("room_update", "Update Room"),
                    ("room_delete", "Delete Room"),
                ],
                max_length=20,
                verbose_name="Action Type",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="chekinapilog",
            name="property",
            field=models.ForeignKey(
                default="769bffdf-4ded-42f1-ad2f-3cabb592744f",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="chekin_logs",
                to="stay.property",
                verbose_name="Property",
            ),
            preserve_default=False,
        ),
    ]
