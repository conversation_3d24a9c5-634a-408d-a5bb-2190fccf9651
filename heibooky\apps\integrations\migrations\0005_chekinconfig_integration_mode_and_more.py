# Generated by Django 5.2.4 on 2025-08-13 19:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("integrations", "0004_alter_chekinapilog_action_type_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="chekinconfig",
            name="integration_mode",
            field=models.CharField(
                choices=[
                    ("partner", "Partner (global key)"),
                    ("standalone", "Standalone (per-property key)"),
                ],
                default="standalone",
                help_text='Use "partner" for global key, "standalone" for per-property key',
                max_length=20,
                verbose_name="Integration Mode",
            ),
        ),
        migrations.AlterField(
            model_name="chekinconfig",
            name="chekin_api_key",
            field=models.CharField(
                blank=True,
                help_text="API key for Chekin authentication (install django-fernet-fields to encrypt)",
                max_length=255,
                null=True,
                verbose_name="Chekin API Key",
            ),
        ),
    ]
