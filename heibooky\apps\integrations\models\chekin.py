import logging

from apps.stay.models import Property
from django.db import models
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)


class ChekinConfig(models.Model):
    """Configuration for online check-in & Chekin integration per property."""

    class Env(models.TextChoices):
        SANDBOX = "sandbox", _("Sandbox")
        PRODUCTION = "production", _("Production")

    class Mode(models.TextChoices):
        PARTNER = "partner", _("Partner (global key)")
        STANDALONE = "standalone", _("Standalone (per-property key)")

    property = models.OneToOneField(
        Property,
        on_delete=models.CASCADE,
        related_name="online_checkin",
        verbose_name=_("Property"),
    )
    is_enabled = models.BooleanField(
        default=False, verbose_name=_("Online Check-in Enabled")
    )
    istat_enabled = models.BooleanField(
        default=False, verbose_name=_("ISTAT Reporting Enabled")
    )
    alloggati_enabled = models.BooleanField(
        default=False, verbose_name=_("Alloggati Web Reporting Enabled")
    )

    # Chekin integration fields
    chekin_enabled = models.BooleanField(
        default=False, verbose_name=_("Chekin Reporting Enabled")
    )
    integration_mode = models.CharField(
        max_length=20,
        choices=Mode.choices,
        default=Mode.STANDALONE,
        verbose_name=_("Integration Mode"),
        help_text=_('Use "partner" for global key, "standalone" for per-property key'),
    )

    # Optional encryption support if django-fernet-fields installed
    try:  # pragma: no cover - import guarded
        from fernet_fields import (
            EncryptedCharField as _EncryptedCharField,  # type: ignore
        )
    except Exception:  # fallback if dependency not present
        _EncryptedCharField = None  # type: ignore

    if _EncryptedCharField:
        chekin_api_key = _EncryptedCharField(
            max_length=255,
            blank=True,
            null=True,
            verbose_name=_("Chekin API Key"),
            help_text=_("API key for Chekin authentication (encrypted)"),
        )
    else:
        chekin_api_key = models.CharField(
            max_length=255,
            blank=True,
            null=True,
            verbose_name=_("Chekin API Key"),
            help_text=_(
                "API key for Chekin authentication (install django-fernet-fields to encrypt)"
            ),
        )

    chekin_environment = models.CharField(
        max_length=20,
        choices=Env.choices,
        default=Env.SANDBOX,
        verbose_name=_("Chekin Environment"),
        help_text=_("Environment to use for Chekin API calls"),
    )
    chekin_last_sync = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Chekin Last Synchronization")
    )

    last_sync = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Last Synchronization")
    )
    next_sync = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Next Scheduled Synchronization")
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    class Meta:
        verbose_name = _("Property Online Check-in")
        verbose_name_plural = _("Property Online Check-ins")
        ordering = ["-updated_at"]

    def __str__(self):
        return f"Online Check-in for {self.property.name}"

    def clean(self):
        """Validate Chekin configuration when enabled."""
        from django.conf import settings
        from django.core.exceptions import ValidationError

        if not self.chekin_enabled:
            return

        skip_housing_id_check = (
            hasattr(self.property, "is_domorent")
            and self.property.is_domorent
            and not self.pk
        )
        if not skip_housing_id_check:
            if not getattr(self.property, "chekin_housing_id", None):
                raise ValidationError(
                    {
                        "chekin_enabled": _(
                            "Chekin Housing ID must be set on the property when Chekin reporting is enabled."
                        )
                    }
                )

        if self.integration_mode == self.Mode.PARTNER or (
            hasattr(self.property, "is_domorent") and self.property.is_domorent
        ):
            global_api_key = getattr(settings, "CHEKIN_API_KEY", "")
            if not global_api_key:
                raise ValidationError(
                    {
                        "chekin_enabled": _(
                            "Global Chekin API Key is not configured in settings for partner / Domorent properties."
                        )
                    }
                )
        else:
            if not self.chekin_api_key:
                raise ValidationError(
                    {
                        "chekin_api_key": _(
                            "Chekin API Key is required when Chekin reporting is enabled in standalone mode."
                        )
                    }
                )

    def is_chekin_configured(self):
        """Return True if Chekin integration is usable for this property."""
        from django.conf import settings

        if not self.chekin_enabled:
            return False
        if self.integration_mode == self.Mode.PARTNER or (
            hasattr(self.property, "is_domorent") and self.property.is_domorent
        ):
            return bool(
                getattr(settings, "CHEKIN_API_KEY", "") and self.chekin_environment
            )
        return bool(self.chekin_api_key and self.chekin_environment)

    def get_chekin_api_key(self):
        """Backward-compatible method for older code paths."""
        return self.effective_api_key()

    def sdk_url(self) -> str:
        """Return CDN URL for the Chekin Host / Housings SDK."""
        return (
            "https://cdn.chekin.com/housings-sdk/latest/index.umd.js"
            if self.chekin_environment == self.Env.PRODUCTION
            else "https://cdn.chekin.com/housings-sdk/dev/index.umd.js"
        )

    def effective_api_key(self) -> str:
        """Return the API key to use based on integration mode."""
        from django.conf import settings

        if self.integration_mode == self.Mode.PARTNER or (
            hasattr(self.property, "is_domorent") and self.property.is_domorent
        ):
            return getattr(settings, "CHEKIN_API_KEY", "")
        return self.chekin_api_key or ""

    def save(self, *args, **kwargs):  # noqa: C901
        """Override save to log configuration changes and keep audit trail."""
        self.full_clean()
        is_new = self.pk is None
        original = None
        if not is_new:
            try:  # fetch original for diff logging
                original = ChekinConfig.objects.get(pk=self.pk)
            except ChekinConfig.DoesNotExist:  # pragma: no cover
                pass

        super().save(*args, **kwargs)

        changed_msgs = []
        if original:
            if original.is_enabled != self.is_enabled:
                changed_msgs.append(
                    f"online_checkin={'on' if self.is_enabled else 'off'}"
                )
            if original.istat_enabled != self.istat_enabled:
                changed_msgs.append(f"istat={'on' if self.istat_enabled else 'off'}")
            if original.alloggati_enabled != self.alloggati_enabled:
                changed_msgs.append(
                    f"alloggati={'on' if self.alloggati_enabled else 'off'}"
                )
            if original.chekin_enabled != self.chekin_enabled:
                changed_msgs.append(f"chekin={'on' if self.chekin_enabled else 'off'}")
            if original.integration_mode != self.integration_mode:
                changed_msgs.append(
                    f"mode:{original.integration_mode}->{self.integration_mode}"
                )
            if original.chekin_environment != self.chekin_environment:
                changed_msgs.append(
                    f"env:{original.chekin_environment}->{self.chekin_environment}"
                )
        else:
            changed_msgs.append("created")

        if changed_msgs:
            logger.info(
                "ChekinConfig %s property=%s %s",
                self.pk,
                getattr(self.property, "id", None),
                ", ".join(changed_msgs),
            )


class ChekinAPILog(models.Model):
    """
    Model to log all Chekin API interactions for monitoring and debugging.
    """

    class ActionType(models.TextChoices):
        RESERVATION_CREATE = "create", _("Create Reservation")
        RESERVATION_UPDATE = "update", _("Update Reservation")
        RESERVATION_DELETE = "delete", _("Delete Reservation")
        TEST_CONNECTION = "test", _("Test Connection")
        # Housing actions
        HOUSING_CREATE = "housing_create", _("Create Housing")
        HOUSING_UPDATE = "housing_update", _("Update Housing")
        HOUSING_DELETE = "housing_delete", _("Delete Housing")
        # Room actions
        ROOM_CREATE = "room_create", _("Create Room")
        ROOM_UPDATE = "room_update", _("Update Room")
        ROOM_DELETE = "room_delete", _("Delete Room")

    class Status(models.TextChoices):
        SUCCESS = "success", _("Success")
        FAILED = "failed", _("Failed")
        RETRY = "retry", _("Retry")

    property = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        related_name="chekin_logs",
        verbose_name=_("Property"),
    )
    booking_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_("Booking ID"),
        help_text=_("Internal booking ID"),
    )
    chekin_reservation_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_("Chekin Reservation ID"),
        help_text=_("Chekin reservation ID"),
    )
    chekin_housing_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_("Chekin Housing ID"),
        help_text=_("Chekin housing ID"),
    )
    chekin_room_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_("Chekin Room ID"),
        help_text=_("Chekin room ID"),
    )
    room_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name=_("Room ID"),
        help_text=_("Internal room ID"),
    )
    action_type = models.CharField(
        max_length=20, choices=ActionType.choices, verbose_name=_("Action Type")
    )
    status = models.CharField(
        max_length=10, choices=Status.choices, verbose_name=_("Status")
    )
    request_payload = models.JSONField(
        blank=True,
        null=True,
        verbose_name=_("Request Payload"),
        help_text=_("JSON payload sent to Chekin API"),
    )
    response_data = models.JSONField(
        blank=True,
        null=True,
        verbose_name=_("Response Data"),
        help_text=_("JSON response from Chekin API"),
    )
    error_message = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Error Message"),
        help_text=_("Error message if the request failed"),
    )
    http_status_code = models.IntegerField(
        blank=True,
        null=True,
        verbose_name=_("HTTP Status Code"),
        help_text=_("HTTP status code from Chekin API"),
    )
    response_time_ms = models.IntegerField(
        blank=True,
        null=True,
        verbose_name=_("Response Time (ms)"),
        help_text=_("API response time in milliseconds"),
    )
    retry_count = models.IntegerField(
        default=0,
        verbose_name=_("Retry Count"),
        help_text=_("Number of retry attempts"),
    )
    environment = models.CharField(
        max_length=20,
        choices=[("sandbox", _("Sandbox")), ("production", _("Production"))],
        verbose_name=_("Environment"),
        help_text=_("Chekin environment used"),
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))

    class Meta:
        verbose_name = _("Chekin API Log")
        verbose_name_plural = _("Chekin API Logs")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["property", "created_at"]),
            models.Index(fields=["booking_id"]),
            models.Index(fields=["chekin_reservation_id"]),
            models.Index(fields=["chekin_housing_id"]),
            models.Index(fields=["chekin_room_id"]),
            models.Index(fields=["room_id"]),
            models.Index(fields=["status", "created_at"]),
            models.Index(fields=["action_type", "created_at"]),
        ]

    def __str__(self):
        return f"Chekin {self.action_type} - {self.status} - {self.property.name}"

    @classmethod
    def log_api_call(
        cls,
        property_instance,
        action_type,
        status,
        booking_id=None,
        chekin_reservation_id=None,
        chekin_housing_id=None,
        chekin_room_id=None,
        room_id=None,
        request_payload=None,
        response_data=None,
        error_message=None,
        http_status_code=None,
        response_time_ms=None,
        retry_count=0,
        environment="sandbox",
    ):
        """
        Convenience method to create a log entry.

        Args:
            property_instance: Property instance
            action_type: Type of action (create, update, delete, test)
            status: Status of the action (success, failed, retry)
            booking_id: Internal booking ID (optional)
            chekin_reservation_id: Chekin reservation ID (optional)
            chekin_housing_id: Chekin housing ID (optional)
            chekin_room_id: Chekin room ID (optional)
            room_id: Internal room ID (optional)
            request_payload: Request payload sent to API (optional)
            response_data: Response data from API (optional)
            error_message: Error message if failed (optional)
            http_status_code: HTTP status code (optional)
            response_time_ms: Response time in milliseconds (optional)
            retry_count: Number of retries (default: 0)
            environment: Chekin environment (default: sandbox)

        Returns:
            ChekinAPILog instance
        """
        try:
            return cls.objects.create(
                property=property_instance,
                booking_id=booking_id,
                chekin_reservation_id=chekin_reservation_id,
                chekin_housing_id=chekin_housing_id,
                chekin_room_id=chekin_room_id,
                room_id=room_id,
                action_type=action_type,
                status=status,
                request_payload=request_payload,
                response_data=response_data,
                error_message=error_message,
                http_status_code=http_status_code,
                response_time_ms=response_time_ms,
                retry_count=retry_count,
                environment=environment,
            )
        except Exception as e:
            logger.error(f"Failed to create Chekin API log: {str(e)}")
            return None
