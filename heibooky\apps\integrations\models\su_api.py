from django.db import models
from django.utils import timezone


class SUAPIActionLog(models.Model):
    ACTION_CHOICES = [
        ("create", "Create"),
        ("update", "Update"),
        ("delete", "Delete"),
        ("batch", "Batch"),
        ("sync", "Sync"),
    ]
    STATUS_CHOICES = [
        ("successful", "Successful"),
        ("failed", "Failed"),
        ("partial", "Partial"),
    ]

    user = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="su_api_logs",
    )
    property_id = models.Char<PERSON><PERSON>(
        max_length=255,
        help_text="ID of the property involved in the action",
        null=True,
        blank=True,
    )
    action = models.Char<PERSON>ield(max_length=20, choices=ACTION_CHOICES)
    description = models.TextField(help_text="Description of the action taken")
    status = models.CharField(max_length=10, choices=STATUS_CHOICES)
    timestamp = models.DateTimeField(default=timezone.now)
    details = models.JSO<PERSON>ield(
        null=True,
        blank=True,
        help_text="Additional data related to the action, such as API response",
    )

    class Meta:
        ordering = ["-timestamp"]
        verbose_name = "SU API Action Log"
        verbose_name_plural = "SU API Action Logs"

    def __str__(self):
        return f"{self.user} - {self.action} - {self.status} - {self.timestamp}"
