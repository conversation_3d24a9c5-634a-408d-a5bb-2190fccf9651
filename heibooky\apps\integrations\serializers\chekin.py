from apps.integrations.models import ChekinConfig
from rest_framework import serializers


class ChekinLaunchSerializer(serializers.Serializer):
    sdkUrl = serializers.CharField()
    apiKey = serializers.CharField()
    defaultLanguage = serializers.CharField(default="en")
    housingId = serializers.CharField(required=False, allow_null=True)
    externalHousingId = serializers.CharField(required=False, allow_null=True)
    reservationId = serializers.CharField(required=False, allow_null=True)
    styles = serializers.CharField(required=False, allow_blank=True)
    hiddenSections = serializers.ListField(
        child=serializers.CharField(), required=False
    )


class ChekinConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChekinConfig
        fields = "__all__"
        read_only_fields = ("created_at", "updated_at")
