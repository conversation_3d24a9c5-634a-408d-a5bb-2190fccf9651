import logging
from typing import Any, Dict, Optional

from apps.booking.models import Booking
from apps.integrations.models import ChekinAPILog, ChekinConfig
from apps.integrations.utils import log_action
from apps.stay.models import Property
from celery import shared_task
from django.conf import settings
from django.db import transaction
from django.utils import timezone
from services.chekin.chekin_mapper import ChekinDataMapper
from services.chekin.chekin_service import (
    ChekinAPIError,
    ChekinAPIService,
    ChekinAuthenticationError,
)

logger = logging.getLogger(__name__)


def get_or_create_chekin_config(property_instance: Property) -> ChekinConfig:
    """
    Get or create ChekinConfig configuration for a Domorent property.

    Args:
        property_instance: The Property instance

    Returns:
        ChekinConfig instance
    """
    try:
        return ChekinConfig.objects.get(property=property_instance)
    except ChekinConfig.DoesNotExist:
        if property_instance.is_domorent:
            logger.info(
                f"Creating ChekinConfig configuration for Domorent property {property_instance.id}"
            )

            # Ensure environment value is valid
            default_env = getattr(settings, "CHEKIN_DEFAULT_ENVIRONMENT", "sandbox")
            environment = (
                "sandbox"
                if default_env not in ["sandbox", "production"]
                else default_env
            )

            config, _ = ChekinConfig.objects.get_or_create(
                property=property_instance,
                is_enabled=True,  # Enable online check-in for Domorent properties
                chekin_enabled=True,  # Enable Chekin integration
                chekin_environment=environment,
                # Note: API key will be retrieved from global settings via get_chekin_api_key()
            )
            logger.info(
                f"Created ChekinConfig configuration for property {property_instance.id}"
            )
            return config
        else:
            raise ChekinConfig.DoesNotExist("Property is not a Domorent property")


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def create_chekin_reservation_task(self, booking_id: str) -> Dict[str, Any]:
    """
    Create a reservation in Chekin for a Domorent property booking.

    Args:
        booking_id: The ID of the booking to create in Chekin

    Returns:
        Dict containing the result of the operation
    """
    try:
        # Get the booking
        try:
            booking = Booking.objects.select_related(
                "property", "customer", "reservation_data"
            ).get(id=booking_id)
        except Booking.DoesNotExist:
            logger.error(f"Booking {booking_id} not found")
            return {"success": False, "error": "Booking not found"}

        # Check if this is a Domorent property
        if not booking.property.is_domorent:
            logger.info(
                f"Skipping Chekin creation for non-Domorent property: {booking.property.id}"
            )
            return {
                "success": True,
                "skipped": True,
                "reason": "Not a Domorent property",
            }

        # Get or create the online check-in configuration for Domorent properties
        config = get_or_create_chekin_config(booking.property)

        # Check if Chekin is enabled and configured
        if not config.is_chekin_configured():
            logger.info(f"Chekin not configured for property {booking.property.id}")
            return {"success": True, "skipped": True, "reason": "Chekin not configured"}

        # Validate booking data
        if not ChekinDataMapper.validate_required_fields(booking):
            logger.error(f"Booking {booking_id} missing required fields for Chekin")
            return {"success": False, "error": "Missing required booking fields"}

        # Initialize Chekin service
        chekin_service = ChekinAPIService(
            api_key=config.get_chekin_api_key(), environment=config.chekin_environment
        )

        # Map booking data to Chekin format
        reservation_data = ChekinDataMapper.map_booking_to_chekin_reservation(
            booking=booking, chekin_housing_id=booking.property.chekin_housing_id
        )

        # Create the reservation in Chekin
        chekin_response = chekin_service.create_reservation(reservation_data)

        # Log the API call
        ChekinAPILog.log_api_call(
            property_instance=booking.property,
            action_type=ChekinAPILog.ActionType.RESERVATION_CREATE,
            status=ChekinAPILog.Status.SUCCESS,
            booking_id=str(booking.id),
            chekin_reservation_id=chekin_response.get("id"),
            request_payload=reservation_data,
            response_data=chekin_response,
            http_status_code=chekin_response.get("_metadata", {}).get("status_code"),
            response_time_ms=chekin_response.get("_metadata", {}).get(
                "response_time_ms"
            ),
            environment=config.chekin_environment,
        )

        # Store the Chekin reservation ID in the booking
        if chekin_response.get("id"):
            booking.chekin_reservation_id = chekin_response["id"]
            booking.save(update_fields=["chekin_reservation_id"])

        # Update the last sync time
        config.chekin_last_sync = timezone.now()
        config.save(update_fields=["chekin_last_sync"])

        # Log the successful action
        transaction.on_commit(
            lambda: log_action(
                user=booking.property.staffs.first(),
                property_id=booking.property.id,
                action="chekin_create",
                description=f"Created Chekin reservation for booking {booking_id}",
                status="successful",
                details={
                    "booking_id": str(booking_id),
                    "chekin_reservation_id": chekin_response.get("id"),
                    "external_id": reservation_data.get("external_id"),
                    "chekin_response": chekin_response,
                },
            )
        )

        logger.info(f"Successfully created Chekin reservation for booking {booking_id}")
        return {
            "success": True,
            "chekin_reservation_id": chekin_response.get("id"),
            "booking_id": str(booking_id),
        }

    except ChekinAuthenticationError as e:
        logger.error(f"Chekin authentication failed for booking {booking_id}: {str(e)}")

        # Log the API failure
        if "booking" in locals():
            ChekinAPILog.log_api_call(
                property_instance=booking.property,
                action_type=ChekinAPILog.ActionType.RESERVATION_CREATE,
                status=ChekinAPILog.Status.FAILED,
                booking_id=str(booking.id),
                request_payload=locals().get("reservation_data"),
                error_message=str(e),
                environment=(
                    config.chekin_environment if "config" in locals() else "sandbox"
                ),
            )

        # Log the failed action
        transaction.on_commit(
            lambda: log_action(
                user=booking.property.staffs.first() if "booking" in locals() else None,
                property_id=booking.property.id if "booking" in locals() else None,
                action="chekin_create",
                description=f"Failed to create Chekin reservation for booking {booking_id}",
                status="failed",
                details={"booking_id": str(booking_id), "error": str(e)},
            )
        )

        return {"success": False, "error": f"Authentication failed: {str(e)}"}

    except ChekinAPIError as e:
        logger.error(f"Chekin API error for booking {booking_id}: {str(e)}")

        # Log the failed action
        if "booking" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=booking.property.staffs.first(),
                    property_id=booking.property.id,
                    action="chekin_create",
                    description=f"Failed to create Chekin reservation for booking {booking_id}",
                    status="failed",
                    details={"booking_id": str(booking_id), "error": str(e)},
                )
            )

        # Retry for temporary API errors
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300  # 5min, 10min, 20min
            logger.info(
                f"Retrying Chekin creation for booking {booking_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": f"API error: {str(e)}"}

    except Exception as e:
        logger.error(
            f"Unexpected error creating Chekin reservation for booking {booking_id}: {str(e)}"
        )

        # Log the failed action
        if "booking" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=booking.property.staffs.first(),
                    property_id=booking.property.id,
                    action="chekin_create",
                    description=f"Failed to create Chekin reservation for booking {booking_id}",
                    status="failed",
                    details={"booking_id": str(booking_id), "error": str(e)},
                )
            )

        # Retry for unexpected errors
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            logger.info(
                f"Retrying Chekin creation for booking {booking_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": f"Unexpected error: {str(e)}"}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def update_chekin_reservation_task(
    self, booking_id: str, chekin_reservation_id: str
) -> Dict[str, Any]:
    """
    Update a reservation in Chekin for a Domorent property booking.

    Args:
        booking_id: The ID of the booking to update in Chekin
        chekin_reservation_id: The Chekin reservation ID to update

    Returns:
        Dict containing the result of the operation
    """
    try:
        # Get the booking
        try:
            booking = Booking.objects.select_related(
                "property", "customer", "reservation_data"
            ).get(id=booking_id)
        except Booking.DoesNotExist:
            logger.error(f"Booking {booking_id} not found")
            return {"success": False, "error": "Booking not found"}

        # Check if this is a Domorent property
        if not booking.property.is_domorent:
            logger.info(
                f"Skipping Chekin update for non-Domorent property: {booking.property.id}"
            )
            return {
                "success": True,
                "skipped": True,
                "reason": "Not a Domorent property",
            }

        # Get or create the online check-in configuration for Domorent properties
        # Get or create Chekin configuration for the property
        config = get_or_create_chekin_config(booking.property)

        # Check if Chekin is enabled and configured
        if not config.is_chekin_configured():
            logger.info(f"Chekin not configured for property {booking.property.id}")
            return {"success": True, "skipped": True, "reason": "Chekin not configured"}

        # Validate booking data
        if not ChekinDataMapper.validate_required_fields(booking):
            logger.error(f"Booking {booking_id} missing required fields for Chekin")
            return {"success": False, "error": "Missing required booking fields"}

        # Initialize Chekin service
        chekin_service = ChekinAPIService(
            api_key=config.get_chekin_api_key(), environment=config.chekin_environment
        )

        # Map booking data to Chekin update format
        update_data = ChekinDataMapper.map_booking_update_to_chekin(
            booking=booking, chekin_housing_id=booking.property.chekin_housing_id
        )

        # Update the reservation in Chekin
        chekin_response = chekin_service.update_reservation(
            chekin_reservation_id, update_data
        )

        # Log the API call (mirror create task behavior)
        ChekinAPILog.log_api_call(
            property_instance=booking.property,
            action_type=ChekinAPILog.ActionType.RESERVATION_UPDATE,
            status=ChekinAPILog.Status.SUCCESS,
            booking_id=str(booking.id),
            chekin_reservation_id=chekin_reservation_id,
            request_payload=update_data,
            response_data=chekin_response,
            http_status_code=chekin_response.get("_metadata", {}).get("status_code"),
            response_time_ms=chekin_response.get("_metadata", {}).get(
                "response_time_ms"
            ),
            environment=config.chekin_environment,
        )

        # Update the last sync time
        config.chekin_last_sync = timezone.now()
        config.save(update_fields=["chekin_last_sync"])

        # Log the successful action
        transaction.on_commit(
            lambda: log_action(
                user=booking.property.staffs.first(),
                property_id=booking.property.id,
                action="chekin_update",
                description=f"Updated Chekin reservation for booking {booking_id}",
                status="successful",
                details={
                    "booking_id": str(booking_id),
                    "chekin_reservation_id": chekin_reservation_id,
                    "chekin_response": chekin_response,
                },
            )
        )

        logger.info(
            f"Successfully updated Chekin reservation {chekin_reservation_id} for booking {booking_id}"
        )
        return {
            "success": True,
            "chekin_reservation_id": chekin_reservation_id,
            "booking_id": str(booking_id),
        }

    except ChekinAuthenticationError as e:
        logger.error(
            f"Chekin authentication failed updating reservation for booking {booking_id}: {str(e)}"
        )

        # Log API failure
        if "booking" in locals():
            ChekinAPILog.log_api_call(
                property_instance=booking.property,
                action_type=ChekinAPILog.ActionType.RESERVATION_UPDATE,
                status=ChekinAPILog.Status.FAILED,
                booking_id=str(booking.id),
                chekin_reservation_id=chekin_reservation_id,
                request_payload=locals().get("update_data"),
                error_message=str(e),
                environment=(
                    config.chekin_environment if "config" in locals() else "sandbox"
                ),
            )

        # Log the failed action
        if "booking" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=booking.property.staffs.first(),
                    property_id=booking.property.id,
                    action="chekin_update",
                    description=f"Failed to update Chekin reservation for booking {booking_id}",
                    status="failed",
                    details={
                        "booking_id": str(booking_id),
                        "chekin_reservation_id": chekin_reservation_id,
                        "error": str(e),
                    },
                )
            )

        # Retry with back-off (matches create task's strategy)
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            logger.info(
                f"Retrying Chekin update after auth failure for booking {booking_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": f"Authentication failed: {str(e)}"}

    except ChekinAPIError as e:
        logger.error(
            f"Chekin API error updating reservation for booking {booking_id}: {str(e)}"
        )

        # Log API failure
        if "booking" in locals():
            ChekinAPILog.log_api_call(
                property_instance=booking.property,
                action_type=ChekinAPILog.ActionType.RESERVATION_UPDATE,
                status=ChekinAPILog.Status.FAILED,
                booking_id=str(booking.id),
                chekin_reservation_id=chekin_reservation_id,
                request_payload=locals().get("update_data"),
                error_message=str(e),
                environment=(
                    config.chekin_environment if "config" in locals() else "sandbox"
                ),
            )

        # Log the failed action
        if "booking" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=booking.property.staffs.first(),
                    property_id=booking.property.id,
                    action="chekin_update",
                    description=f"Failed to update Chekin reservation for booking {booking_id}",
                    status="failed",
                    details={
                        "booking_id": str(booking_id),
                        "chekin_reservation_id": chekin_reservation_id,
                        "error": str(e),
                    },
                )
            )

        # Retry for temporary API errors
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300  # 5min, 10min, 20min
            logger.info(
                f"Retrying Chekin update for booking {booking_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": f"API error: {str(e)}"}

    except Exception as e:
        logger.error(
            f"Error updating Chekin reservation for booking {booking_id}: {str(e)}"
        )

        # Log the failed action
        if "booking" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=booking.property.staffs.first(),
                    property_id=booking.property.id,
                    action="chekin_update",
                    description=f"Failed to update Chekin reservation for booking {booking_id}",
                    status="failed",
                    details={
                        "booking_id": str(booking_id),
                        "chekin_reservation_id": chekin_reservation_id,
                        "error": str(e),
                    },
                )
            )

        # Retry logic similar to create task
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            logger.info(
                f"Retrying Chekin update for booking {booking_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": str(e)}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def delete_chekin_reservation_task(
    self, booking_id: str, chekin_reservation_id: str
) -> Dict[str, Any]:
    """
    Delete a reservation from Chekin for a cancelled or no-show booking.

    Args:
        booking_id: The ID of the booking to delete from Chekin
        chekin_reservation_id: The Chekin reservation ID to delete

    Returns:
        Dict containing the result of the operation
    """
    try:
        # Get the booking (may not exist if it was deleted)
        booking = None
        property_instance = None
        config = None

        try:
            booking = Booking.objects.select_related("property").get(id=booking_id)
            property_instance = booking.property
        except Booking.DoesNotExist:
            logger.warning(f"Booking {booking_id} not found for Chekin deletion")
            # We'll still try to delete from Chekin if we have the reservation ID

        # If we have a booking, get the configuration
        if property_instance:
            try:
                config = ChekinConfig.objects.get(property=property_instance)
            except ChekinConfig.DoesNotExist:
                logger.warning(
                    f"No online check-in config found for property {property_instance.id}"
                )
                return {"success": False, "error": "Online check-in not configured"}

            # Check if Chekin is configured
            if not config.is_chekin_configured():
                logger.info(
                    f"Chekin not configured for property {property_instance.id}"
                )
                return {
                    "success": True,
                    "skipped": True,
                    "reason": "Chekin not configured",
                }

            # Initialize Chekin service
            chekin_service = ChekinAPIService(
                api_key=config.get_chekin_api_key(),
                environment=config.chekin_environment,
            )
        else:
            # If no booking found, we need to use default configuration
            # This is a fallback scenario - in practice, we should store Chekin config separately
            logger.warning(
                f"No booking found for {booking_id}, cannot determine Chekin configuration"
            )
            return {"success": False, "error": "Cannot determine Chekin configuration"}

        # Delete the reservation from Chekin
        success = chekin_service.delete_reservation(chekin_reservation_id)

        if success and config:
            # Update the last sync time
            config.chekin_last_sync = timezone.now()
            config.save(update_fields=["chekin_last_sync"])

        # Log the successful action
        if property_instance:
            transaction.on_commit(
                lambda: log_action(
                    user=property_instance.staffs.first(),
                    property_id=property_instance.id,
                    action="chekin_delete",
                    description=f"Deleted Chekin reservation for booking {booking_id}",
                    status="successful",
                    details={
                        "booking_id": str(booking_id),
                        "chekin_reservation_id": chekin_reservation_id,
                    },
                )
            )

        if success:
            logger.info(
                f"Successfully deleted Chekin reservation {chekin_reservation_id} for booking {booking_id}"
            )
            return {
                "success": True,
                "chekin_reservation_id": chekin_reservation_id,
                "booking_id": str(booking_id),
            }
        else:
            logger.warning(
                f"Chekin deletion returned False for reservation {chekin_reservation_id} and booking {booking_id}"
            )
            return {
                "success": False,
                "error": "Deletion unsuccessful",
            }

    except ChekinAPIError as e:
        logger.error(f"Chekin API error for booking {booking_id}: {str(e)}")

        # Log the failed action
        if "booking" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=booking.property.staffs.first(),
                    property_id=booking.property.id,
                    action="chekin_delete",
                    description=f"Failed to delete Chekin reservation for booking {booking_id}",
                    status="failed",
                    details={
                        "booking_id": str(booking_id),
                        "chekin_reservation_id": chekin_reservation_id,
                        "error": str(e),
                    },
                )
            )
        else:
            logger.warning(
                f"Chekin deletion returned False for reservation {chekin_reservation_id} and booking {booking_id}"
            )
            return {
                "success": False,
                "error": "Deletion unsuccessful",
            }

    except Exception as e:
        logger.error(
            f"Error deleting Chekin reservation for booking {booking_id}: {str(e)}"
        )

        # Log the failed action
        if property_instance:
            transaction.on_commit(
                lambda: log_action(
                    user=property_instance.staffs.first(),
                    property_id=property_instance.id,
                    action="chekin_delete",
                    description=f"Failed to delete Chekin reservation for booking {booking_id}",
                    status="failed",
                    details={
                        "booking_id": str(booking_id),
                        "chekin_reservation_id": chekin_reservation_id,
                        "error": str(e),
                    },
                )
            )

        # Retry logic
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            logger.info(
                f"Retrying Chekin deletion for booking {booking_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": str(e)}
