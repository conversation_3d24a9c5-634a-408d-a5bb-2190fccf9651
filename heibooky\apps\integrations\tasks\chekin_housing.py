import logging
from typing import Any, Dict, Optional

from apps.integrations.models import ChekinAP<PERSON>og, ChekinConfig
from apps.integrations.utils import log_action
from apps.stay.models import Property
from celery import shared_task
from django.db import transaction
from django.utils import timezone
from services.chekin.chekin_mapper import ChekinDataMapper
from services.chekin.chekin_service import (
    ChekinAPIError,
    ChekinAPIService,
    ChekinAuthenticationError,
)

from .chekin import get_or_create_chekin_config

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def create_chekin_housing_task(self, property_id: str) -> Dict[str, Any]:
    """
    Create a housing in Chekin for a Domorent property.

    Args:
        property_id: The ID of the property to create in Chekin

    Returns:
        Dict containing the result of the operation
    """
    try:
        # Get the property
        try:
            property_instance = Property.objects.select_related("location").get(
                id=property_id
            )
        except (Property.DoesNotExist, ValueError):
            logger.error(f"Property {property_id} not found")
            return {"success": False, "error": "Property not found"}
        # Check if this is a Domorent property
        if not property_instance.is_domorent:
            logger.info(
                f"Skipping Chekin housing creation for non-Domorent property: {property_id}"
            )
            return {
                "success": True,
                "skipped": True,
                "reason": "Not a Domorent property",
            }

        # Check if property is onboarded
        if not property_instance.is_onboarded:
            logger.info(
                f"Skipping Chekin housing creation for non-onboarded property: {property_id}"
            )
            return {
                "success": True,
                "skipped": True,
                "reason": "Property not onboarded",
            }

        # Get or create Chekin configuration for the property
        config = get_or_create_chekin_config(property_instance)

        # Check if Chekin is enabled and configured
        if not config.is_chekin_configured():
            logger.info(f"Chekin not configured for property {property_id}")
            return {"success": True, "skipped": True, "reason": "Chekin not configured"}

        # Validate property data
        if not ChekinDataMapper.validate_property_for_chekin(property_instance):
            logger.error(f"Property {property_id} missing required fields for Chekin")
            return {"success": False, "error": "Missing required property fields"}

        # Initialize Chekin service
        chekin_service = ChekinAPIService(
            api_key=config.get_chekin_api_key(), environment=config.chekin_environment
        )

        # Map property data to Chekin format
        housing_data = ChekinDataMapper.map_property_to_chekin_housing(
            property_instance
        )

        # Create the housing in Chekin (idempotent)
        try:
            chekin_response = chekin_service.create_housing(housing_data)
        except ChekinAPIError as e:
            # Handle duplicate external_id gracefully by linking to existing housing
            msg = str(e)
            if "external_id" in msg and "already exists" in msg:
                existing = chekin_service.find_housing_by_external_id(
                    housing_data.get("external_id")
                )
                if existing and existing.get("id"):
                    chekin_response = existing
                    logger.info(
                        f"Linked existing Chekin housing {existing.get('id')} for property {property_id} (idempotent create)"
                    )
                else:
                    raise
            else:
                raise

        # Store the Chekin housing ID in the property
        if chekin_response.get("id"):
            property_instance.chekin_housing_id = chekin_response["id"]
            property_instance.save(update_fields=["chekin_housing_id"])

        # Update the last sync time
        config.chekin_last_sync = timezone.now()
        config.save(update_fields=["chekin_last_sync"])

        # Log the API call
        ChekinAPILog.log_api_call(
            property_instance=property_instance,
            action_type=ChekinAPILog.ActionType.HOUSING_CREATE,
            status=ChekinAPILog.Status.SUCCESS,
            chekin_housing_id=chekin_response.get("id"),
            request_payload=housing_data,
            response_data=chekin_response,
            http_status_code=chekin_response.get("_metadata", {}).get("status_code"),
            response_time_ms=chekin_response.get("_metadata", {}).get(
                "response_time_ms"
            ),
            environment=config.chekin_environment,
        )

        # Log the successful action
        transaction.on_commit(
            lambda: log_action(
                user=property_instance.staffs.first(),
                property_id=property_instance.id,
                action="create",
                description=f"Created Chekin housing for property {property_id}",
                status="successful",
                details={
                    "property_id": str(property_id),
                    "chekin_housing_id": chekin_response.get("id"),
                    "external_id": housing_data.get("external_id"),
                    "chekin_response": chekin_response,
                },
            )
        )

        logger.info(f"Successfully created Chekin housing for property {property_id}")
        return {
            "success": True,
            "chekin_housing_id": chekin_response.get("id"),
            "property_id": str(property_id),
        }

    except ChekinAuthenticationError as e:
        logger.error(
            f"Chekin authentication failed for property {property_id}: {str(e)}"
        )

        # Log the API failure
        if "property_instance" in locals():
            ChekinAPILog.log_api_call(
                property_instance=property_instance,
                action_type=ChekinAPILog.ActionType.HOUSING_CREATE,
                status=ChekinAPILog.Status.FAILED,
                request_payload=locals().get("housing_data"),
                error_message=str(e),
                environment=(
                    config.chekin_environment if "config" in locals() else "sandbox"
                ),
            )

        # Log the failed action
        transaction.on_commit(
            lambda: log_action(
                user=(
                    property_instance.staffs.first()
                    if "property_instance" in locals()
                    else None
                ),
                property_id=(
                    property_instance.id if "property_instance" in locals() else None
                ),
                action="create",
                description=f"Failed to create Chekin housing for property {property_id}",
                status="failed",
                details={"property_id": str(property_id), "error": str(e)},
            )
        )

        return {"success": False, "error": f"Authentication failed: {str(e)}"}

    except ChekinAPIError as e:
        logger.error(f"Chekin API error for property {property_id}: {str(e)}")

        # Log the API failure
        if "property_instance" in locals():
            ChekinAPILog.log_api_call(
                property_instance=property_instance,
                action_type=ChekinAPILog.ActionType.HOUSING_CREATE,
                status=ChekinAPILog.Status.FAILED,
                request_payload=locals().get("housing_data"),
                error_message=str(e),
                environment=(
                    config.chekin_environment if "config" in locals() else "sandbox"
                ),
            )

        # Log the failed action
        if "property_instance" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=property_instance.staffs.first(),
                    property_id=property_instance.id,
                    action="create",
                    description=f"Failed to create Chekin housing for property {property_id}",
                    status="failed",
                    details={"property_id": str(property_id), "error": str(e)},
                )
            )

        # Retry for temporary API errors
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300  # 5min, 10min, 20min
            logger.info(
                f"Retrying Chekin housing creation for property {property_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": f"API error: {str(e)}"}

    except Exception as e:
        logger.error(
            f"Unexpected error creating Chekin housing for property {property_id}: {str(e)}"
        )

        # Log the failed action
        if "property_instance" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=property_instance.staffs.first(),
                    property_id=property_instance.id,
                    action="create",
                    description=f"Failed to create Chekin housing for property {property_id}",
                    status="failed",
                    details={"property_id": str(property_id), "error": str(e)},
                )
            )

        # Retry for unexpected errors
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            logger.info(
                f"Retrying Chekin housing creation for property {property_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": f"Unexpected error: {str(e)}"}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def update_chekin_housing_task(
    self, property_id: str, chekin_housing_id: str
) -> Dict[str, Any]:
    """
    Update a housing in Chekin for a Domorent property.

    Args:
        property_id: The ID of the property to update in Chekin
        chekin_housing_id: The Chekin housing ID to update

    Returns:
        Dict containing the result of the operation
    """
    try:
        # Get the property
        try:
            property_instance = Property.objects.select_related("location").get(
                id=property_id
            )
        except Property.DoesNotExist:
            logger.error(f"Property {property_id} not found")
            return {"success": False, "error": "Property not found"}

        # Check if this is a Domorent property
        if not property_instance.is_domorent:
            logger.info(
                f"Skipping Chekin housing update for non-Domorent property: {property_id}"
            )
            return {
                "success": True,
                "skipped": True,
                "reason": "Not a Domorent property",
            }

        # Get or create Chekin configuration for the property
        config = get_or_create_chekin_config(property_instance)

        # Check if Chekin is enabled and configured
        if not config.is_chekin_configured():
            logger.info(f"Chekin not configured for property {property_id}")
            return {"success": True, "skipped": True, "reason": "Chekin not configured"}

        # Validate property data
        if not ChekinDataMapper.validate_property_for_chekin(property_instance):
            logger.error(f"Property {property_id} missing required fields for Chekin")
            return {"success": False, "error": "Missing required property fields"}

        # Initialize Chekin service
        chekin_service = ChekinAPIService(
            api_key=config.get_chekin_api_key(), environment=config.chekin_environment
        )

        # Map property data to Chekin update format
        housing_data = ChekinDataMapper.map_property_to_chekin_housing(
            property_instance
        )

        # Update the housing in Chekin
        chekin_response = chekin_service.update_housing(chekin_housing_id, housing_data)

        # Update the last sync time
        config.chekin_last_sync = timezone.now()
        config.save(update_fields=["chekin_last_sync"])

        # Log the successful action and API call on commit
        def _on_commit_success(
            pi=property_instance,
            pid=property_id,
            ch_id=chekin_housing_id,
            resp=chekin_response,
            hdata=housing_data,
            conf=config,
        ):
            log_action(
                user=pi.staffs.first(),
                property_id=pi.id,
                action="update",
                description=f"Updated Chekin housing for property {pid}",
                status="successful",
                details={
                    "property_id": str(pid),
                    "chekin_housing_id": ch_id,
                    "chekin_response": resp,
                },
            )
            ChekinAPILog.log_api_call(
                property_instance=pi,
                action_type=ChekinAPILog.ActionType.HOUSING_UPDATE,
                status=ChekinAPILog.Status.SUCCESS,
                chekin_housing_id=ch_id,
                request_payload=hdata,
                response_data=resp,
                http_status_code=(
                    resp.get("_metadata", {}).get("status_code")
                    if isinstance(resp, dict)
                    else None
                ),
                response_time_ms=(
                    resp.get("_metadata", {}).get("response_time_ms")
                    if isinstance(resp, dict)
                    else None
                ),
                environment=conf.chekin_environment if conf else "sandbox",
            )

        transaction.on_commit(_on_commit_success)

        logger.info(
            f"Successfully updated Chekin housing {chekin_housing_id} for property {property_id}"
        )
        return {
            "success": True,
            "chekin_housing_id": chekin_housing_id,
            "property_id": str(property_id),
        }

    except Exception as e:
        logger.error(
            f"Error updating Chekin housing for property {property_id}: {str(e)}"
        )

        # Log the failed action
        if "property_instance" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=property_instance.staffs.first(),
                    property_id=property_instance.id,
                    action="update",
                    description=f"Failed to update Chekin housing for property {property_id}",
                    status="failed",
                    details={
                        "property_id": str(property_id),
                        "chekin_housing_id": chekin_housing_id,
                        "error": str(e),
                    },
                )
            )

            # Log API failure for update (parity with create flow)
            ChekinAPILog.log_api_call(
                property_instance=property_instance,
                action_type=ChekinAPILog.ActionType.HOUSING_UPDATE,
                status=ChekinAPILog.Status.FAILED,
                chekin_housing_id=chekin_housing_id,
                request_payload=locals().get("housing_data"),
                error_message=str(e),
                environment=(
                    config.chekin_environment if "config" in locals() else "sandbox"
                ),
            )

        # Retry logic similar to create task
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            logger.info(
                f"Retrying Chekin housing update for property {property_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": str(e)}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def delete_chekin_housing_task(
    self, property_id: str, chekin_housing_id: str
) -> Dict[str, Any]:
    """
    Delete a housing from Chekin for a deleted Domorent property.

    Args:
        property_id: The ID of the property to delete from Chekin
        chekin_housing_id: The Chekin housing ID to delete

    Returns:
        Dict containing the result of the operation
    """
    try:
        # Get the property (may not exist if it was deleted)
        property_instance = None
        config = None

        try:
            property_instance = Property.objects.select_related("location").get(
                id=property_id
            )
        except Property.DoesNotExist:
            logger.warning(
                f"Property {property_id} not found for Chekin housing deletion"
            )
            # We'll still try to delete from Chekin if we have the housing ID

        # If we have a property, get the configuration
        if property_instance:
            try:
                config = ChekinConfig.objects.get(property=property_instance)
            except ChekinConfig.DoesNotExist:
                logger.warning(
                    f"No online check-in config found for property {property_id}"
                )
                return {"success": False, "error": "Online check-in not configured"}

            # Check if Chekin is configured
            if not config.is_chekin_configured():
                logger.info(f"Chekin not configured for property {property_id}")
                return {
                    "success": True,
                    "skipped": True,
                    "reason": "Chekin not configured",
                }

            # Initialize Chekin service
            chekin_service = ChekinAPIService(
                api_key=config.get_chekin_api_key(),
                environment=config.chekin_environment,
            )
        else:
            # If no property found, we need to use default configuration
            # This is a fallback scenario - in practice, we should store Chekin config separately
            logger.warning(
                f"No property found for {property_id}, cannot determine Chekin configuration"
            )
            return {"success": False, "error": "Cannot determine Chekin configuration"}

        # Delete the housing from Chekin
        success = chekin_service.delete_housing(chekin_housing_id)

        if success and config:
            # Update the last sync time
            config.chekin_last_sync = timezone.now()
            config.save(update_fields=["chekin_last_sync"])

        # If deletion failed (e.g., 404/410), surface as failure
        if success is False:
            logger.warning(
                f"Chekin housing deletion returned False for housing {chekin_housing_id} and property {property_id}"
            )
            return {"success": False, "error": "Deletion unsuccessful"}

        # Log the successful action
        if property_instance and success:
            transaction.on_commit(
                lambda: log_action(
                    user=property_instance.staffs.first(),
                    property_id=property_instance.id,
                    action="delete",
                    description=f"Deleted Chekin housing for property {property_id}",
                    status="successful",
                    details={
                        "property_id": str(property_id),
                        "chekin_housing_id": chekin_housing_id,
                    },
                )
            )

        logger.info(
            f"Successfully deleted Chekin housing {chekin_housing_id} for property {property_id}"
        )
        return {
            "success": True,
            "chekin_housing_id": chekin_housing_id,
            "property_id": str(property_id),
        }

    except Exception as e:
        logger.error(
            f"Error deleting Chekin housing for property {property_id}: {str(e)}"
        )

        # Log the failed action
        if property_instance:
            transaction.on_commit(
                lambda: log_action(
                    user=property_instance.staffs.first(),
                    property_id=property_instance.id,
                    action="delete",
                    description=f"Failed to delete Chekin housing for property {property_id}",
                    status="failed",
                    details={
                        "property_id": str(property_id),
                        "chekin_housing_id": chekin_housing_id,
                        "error": str(e),
                    },
                )
            )

        # Retry logic
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            logger.info(
                f"Retrying Chekin housing deletion for property {property_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": str(e)}
