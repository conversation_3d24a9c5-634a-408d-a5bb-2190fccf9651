import logging
from typing import Any, Dict, Optional

from apps.integrations.models import ChekinAP<PERSON>og, ChekinConfig
from apps.integrations.utils import log_action
from apps.stay.models import Property, Room
from celery import shared_task
from django.db import transaction
from django.utils import timezone
from services.chekin.chekin_mapper import ChekinDataMapper
from services.chekin.chekin_service import (
    ChekinAPIError,
    ChekinAPIService,
    ChekinAuthenticationError,
)

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def create_chekin_room_task(self, room_id: str) -> Dict[str, Any]:
    """
    Create a room in Chekin for a Domorent property room.

    Args:
        room_id: The ID of the room to create in Chekin

    Returns:
        Dict containing the result of the operation
    """
    try:
        # Get the room
        try:
            room_instance = Room.objects.select_related(
                "property", "property__location"
            ).get(id=room_id)
        except Room.DoesNotExist:
            logger.error(f"Room {room_id} not found")
            return {"success": False, "error": "Room not found"}

        property_instance = room_instance.property

        # Check if this is a Domorent property
        if not property_instance.is_domorent:
            logger.info(
                f"Skipping Chekin room creation for non-Domorent property: {property_instance.id}"
            )
            return {
                "success": True,
                "skipped": True,
                "reason": "Not a Domorent property",
            }

        # Check if room is onboarded
        if not room_instance.is_onboarded:
            logger.info(
                f"Skipping Chekin room creation for non-onboarded room: {room_id}"
            )
            return {"success": True, "skipped": True, "reason": "Room not onboarded"}

        # Check if property has Chekin housing ID
        if not property_instance.chekin_housing_id:
            logger.warning(
                f"Property {property_instance.id} missing Chekin housing ID for room {room_id}"
            )
            return {"success": False, "error": "Property missing Chekin housing ID"}

        # Get or create the online check-in configuration for Domorent properties
        try:
            config = ChekinConfig.objects.get(property=property_instance)
        except ChekinConfig.DoesNotExist:
            logger.info(
                f"Creating ChekinConfig configuration for Domorent property {property_instance.id}"
            )
            # Create default configuration for Domorent properties
            from django.conf import settings

            config = ChekinConfig.objects.create(
                property=property_instance,
                is_enabled=True,  # Enable online check-in for Domorent properties
                chekin_enabled=True,  # Enable Chekin integration
                chekin_environment=getattr(
                    settings, "CHEKIN_DEFAULT_ENVIRONMENT", "sandbox"
                ),
                # Note: API key will be retrieved from global settings via get_chekin_api_key()
            )
            logger.info(
                f"Created ChekinConfig configuration for property {property_instance.id}"
            )

        # Check if Chekin is enabled and configured
        if not config.is_chekin_configured():
            logger.info(f"Chekin not configured for property {property_instance.id}")
            return {"success": True, "skipped": True, "reason": "Chekin not configured"}

        # Validate room data
        if not ChekinDataMapper.validate_room_for_chekin(room_instance):
            logger.error(f"Room {room_id} missing required fields for Chekin")
            return {"success": False, "error": "Missing required room fields"}

        # Initialize Chekin service
        chekin_service = ChekinAPIService(
            api_key=config.get_chekin_api_key(), environment=config.chekin_environment
        )

        # Map room data to Chekin format
        room_data = ChekinDataMapper.map_room_to_chekin_room(
            room_instance=room_instance,
            chekin_housing_id=property_instance.chekin_housing_id,
        )

        # Create the room in Chekin (idempotent)
        try:
            chekin_response = chekin_service.create_room(room_data)
        except ChekinAPIError as e:
            # Handle duplicate external_id gracefully by linking to existing room
            msg = str(e)
            if "external_id" in msg and "already exists" in msg:
                existing = chekin_service.find_room_by_external_id(
                    room_data.get("external_id")
                )
                if existing and existing.get("id"):
                    chekin_response = existing
                    logger.info(
                        f"Linked existing Chekin room {existing.get('id')} for room {room_id} (idempotent create)"
                    )
                else:
                    raise
            else:
                raise

        # Store the Chekin room ID in the room
        if chekin_response.get("id"):
            room_instance.chekin_room_id = chekin_response["id"]
            room_instance.save(update_fields=["chekin_room_id"])

        # Update the last sync time
        config.chekin_last_sync = timezone.now()
        config.save(update_fields=["chekin_last_sync"])

        # Log the API call
        ChekinAPILog.log_api_call(
            property_instance=property_instance,
            action_type=ChekinAPILog.ActionType.ROOM_CREATE,
            status=ChekinAPILog.Status.SUCCESS,
            room_id=str(room_instance.id),
            chekin_room_id=chekin_response.get("id"),
            request_payload=room_data,
            response_data=chekin_response,
            http_status_code=chekin_response.get("_metadata", {}).get("status_code"),
            response_time_ms=chekin_response.get("_metadata", {}).get(
                "response_time_ms"
            ),
            environment=config.chekin_environment,
        )

        # Log the successful action
        transaction.on_commit(
            lambda: log_action(
                user=property_instance.staffs.first(),
                property_id=property_instance.id,
                action="create",
                description=f"Created Chekin room for room {room_id}",
                status="successful",
                details={
                    "room_id": str(room_id),
                    "property_id": str(property_instance.id),
                    "chekin_room_id": chekin_response.get("id"),
                    "external_id": room_data.get("external_id"),
                    "chekin_response": chekin_response,
                },
            )
        )

        logger.info(f"Successfully created Chekin room for room {room_id}")
        return {
            "success": True,
            "chekin_room_id": chekin_response.get("id"),
            "room_id": str(room_id),
        }

    except ChekinAuthenticationError as e:
        logger.error(f"Chekin authentication failed for room {room_id}: {str(e)}")

        # Log the API failure
        if "room_instance" in locals():
            ChekinAPILog.log_api_call(
                property_instance=room_instance.property,
                action_type=ChekinAPILog.ActionType.ROOM_CREATE,
                status=ChekinAPILog.Status.FAILED,
                room_id=str(room_instance.id),
                request_payload=locals().get("room_data"),
                error_message=str(e),
                environment=(
                    config.chekin_environment if "config" in locals() else "sandbox"
                ),
            )

        # Log the failed action
        transaction.on_commit(
            lambda: log_action(
                user=(
                    room_instance.property.staffs.first()
                    if "room_instance" in locals()
                    else None
                ),
                property_id=(
                    room_instance.property.id if "room_instance" in locals() else None
                ),
                action="create",
                description=f"Failed to create Chekin room for room {room_id}",
                status="failed",
                details={"room_id": str(room_id), "error": str(e)},
            )
        )

        return {"success": False, "error": f"Authentication failed: {str(e)}"}

    except ChekinAPIError as e:
        logger.error(f"Chekin API error for room {room_id}: {str(e)}")

        # Log the API failure
        if "room_instance" in locals():
            ChekinAPILog.log_api_call(
                property_instance=room_instance.property,
                action_type=ChekinAPILog.ActionType.ROOM_CREATE,
                room_id=str(room_instance.id),
                status=ChekinAPILog.Status.FAILED,
                request_payload=locals().get("room_data"),
                error_message=str(e),
                environment=(
                    config.chekin_environment if "config" in locals() else "sandbox"
                ),
            )

        # Log the failed action
        if "room_instance" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=room_instance.property.staffs.first(),
                    property_id=room_instance.property.id,
                    action="create",
                    description=f"Failed to create Chekin room for room {room_id}",
                    status="failed",
                    details={"room_id": str(room_id), "error": str(e)},
                )
            )

        # Retry for temporary API errors
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300  # 5min, 10min, 20min
            logger.info(
                f"Retrying Chekin room creation for room {room_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": f"API error: {str(e)}"}

    except Exception as e:
        logger.error(
            f"Unexpected error creating Chekin room for room {room_id}: {str(e)}"
        )

        # Log the failed action
        if "room_instance" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=room_instance.property.staffs.first(),
                    property_id=room_instance.property.id,
                    action="create",
                    description=f"Failed to create Chekin room for room {room_id}",
                    status="failed",
                    details={"room_id": str(room_id), "error": str(e)},
                )
            )

        # Retry for unexpected errors
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            logger.info(
                f"Retrying Chekin room creation for room {room_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": f"Unexpected error: {str(e)}"}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def update_chekin_room_task(self, room_id: str, chekin_room_id: str) -> Dict[str, Any]:
    """
    Update a room in Chekin for a Domorent property room.

    Args:
        room_id: The ID of the room to update in Chekin
        chekin_room_id: The Chekin room ID to update

    Returns:
        Dict containing the result of the operation
    """
    try:
        # Get the room
        try:
            room_instance = Room.objects.select_related(
                "property", "property__location"
            ).get(id=room_id)
        except Room.DoesNotExist:
            logger.error(f"Room {room_id} not found")
            return {"success": False, "error": "Room not found"}

        property_instance = room_instance.property

        # Check if this is a Domorent property
        if not property_instance.is_domorent:
            logger.info(
                f"Skipping Chekin room update for non-Domorent property: {property_instance.id}"
            )
            return {
                "success": True,
                "skipped": True,
                "reason": "Not a Domorent property",
            }

        # Check if property has Chekin housing ID
        if not property_instance.chekin_housing_id:
            logger.warning(
                f"Property {property_instance.id} missing Chekin housing ID for room {room_id}"
            )
            return {"success": False, "error": "Property missing Chekin housing ID"}

        # Get or create the online check-in configuration for Domorent properties
        try:
            config = ChekinConfig.objects.get(property=property_instance)
        except ChekinConfig.DoesNotExist:
            logger.info(
                f"Creating ChekinConfig configuration for Domorent property {property_instance.id}"
            )
            # Create default configuration for Domorent properties
            from django.conf import settings

            config = ChekinConfig.objects.create(
                property=property_instance,
                is_enabled=True,  # Enable online check-in for Domorent properties
                chekin_enabled=True,  # Enable Chekin integration
                chekin_environment=getattr(
                    settings, "CHEKIN_DEFAULT_ENVIRONMENT", "sandbox"
                ),
                # Note: API key will be retrieved from global settings via get_chekin_api_key()
            )
            logger.info(
                f"Created ChekinConfig configuration for property {property_instance.id}"
            )

        # Check if Chekin is enabled and configured
        if not config.is_chekin_configured():
            logger.info(f"Chekin not configured for property {property_instance.id}")
            return {"success": True, "skipped": True, "reason": "Chekin not configured"}

        # Validate room data
        if not ChekinDataMapper.validate_room_for_chekin(room_instance):
            logger.error(f"Room {room_id} missing required fields for Chekin")
            return {"success": False, "error": "Missing required room fields"}

        # Initialize Chekin service
        chekin_service = ChekinAPIService(
            api_key=config.get_chekin_api_key(), environment=config.chekin_environment
        )

        # Map room data to Chekin update format
        room_data = ChekinDataMapper.map_room_to_chekin_room(
            room_instance=room_instance,
            chekin_housing_id=property_instance.chekin_housing_id,
        )

        # Update the room in Chekin
        chekin_response = chekin_service.update_room(chekin_room_id, room_data)

        # Update the last sync time
        config.chekin_last_sync = timezone.now()
        config.save(update_fields=["chekin_last_sync"])

        # Log the successful action
        transaction.on_commit(
            lambda: log_action(
                user=property_instance.staffs.first(),
                property_id=property_instance.id,
                action="update",
                description=f"Updated Chekin room for room {room_id}",
                status="successful",
                details={
                    "room_id": str(room_id),
                    "property_id": str(property_instance.id),
                    "chekin_room_id": chekin_room_id,
                    "chekin_response": chekin_response,
                },
            )
        )

        logger.info(
            f"Successfully updated Chekin room {chekin_room_id} for room {room_id}"
        )
        return {
            "success": True,
            "chekin_room_id": chekin_room_id,
            "room_id": str(room_id),
        }

    except ChekinAuthenticationError as e:
        logger.error(f"Chekin authentication failed updating room {room_id}: {str(e)}")

        # Log API failure
        if "room_instance" in locals():
            ChekinAPILog.log_api_call(
                property_instance=room_instance.property,
                action_type=ChekinAPILog.ActionType.ROOM_UPDATE,
                status=ChekinAPILog.Status.FAILED,
                room_id=str(room_instance.id),
                chekin_room_id=chekin_room_id,
                request_payload=locals().get("room_data"),
                error_message=str(e),
                environment=(
                    config.chekin_environment if "config" in locals() else "sandbox"
                ),
            )

        # Log the failed action
        transaction.on_commit(
            lambda: log_action(
                user=(
                    room_instance.property.staffs.first()
                    if "room_instance" in locals()
                    else None
                ),
                property_id=(
                    room_instance.property.id if "room_instance" in locals() else None
                ),
                action="update",
                description=f"Failed to update Chekin room for room {room_id}",
                status="failed",
                details={"room_id": str(room_id), "error": str(e)},
            )
        )

        return {"success": False, "error": f"Authentication failed: {str(e)}"}

    except ChekinAPIError as e:
        logger.error(f"Chekin API error updating room {room_id}: {str(e)}")

        # Log API failure
        if "room_instance" in locals():
            ChekinAPILog.log_api_call(
                property_instance=room_instance.property,
                action_type=ChekinAPILog.ActionType.ROOM_UPDATE,
                status=ChekinAPILog.Status.FAILED,
                room_id=str(room_instance.id),
                chekin_room_id=chekin_room_id,
                request_payload=locals().get("room_data"),
                error_message=str(e),
                environment=(
                    config.chekin_environment if "config" in locals() else "sandbox"
                ),
            )

        # Log the failed action
        if "room_instance" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=room_instance.property.staffs.first(),
                    property_id=room_instance.property.id,
                    action="update",
                    description=f"Failed to update Chekin room for room {room_id}",
                    status="failed",
                    details={"room_id": str(room_id), "error": str(e)},
                )
            )

        # Retry for temporary API errors
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300  # 5min, 10min, 20min
            logger.info(
                f"Retrying Chekin room update for room {room_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": f"API error: {str(e)}"}

    except Exception as e:
        logger.error(f"Error updating Chekin room for room {room_id}: {str(e)}")

        # Log the failed action
        if "room_instance" in locals():
            transaction.on_commit(
                lambda: log_action(
                    user=room_instance.property.staffs.first(),
                    property_id=room_instance.property.id,
                    action="update",
                    description=f"Failed to update Chekin room for room {room_id}",
                    status="failed",
                    details={
                        "room_id": str(room_id),
                        "chekin_room_id": chekin_room_id,
                        "error": str(e),
                    },
                )
            )

        # Retry logic similar to create task
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            logger.info(
                f"Retrying Chekin room update for room {room_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": str(e)}


@shared_task(bind=True, max_retries=3, default_retry_delay=300)
def delete_chekin_room_task(self, room_id: str, chekin_room_id: str) -> Dict[str, Any]:
    """
    Delete a room from Chekin for a deleted Domorent property room.

    Args:
        room_id: The ID of the room to delete from Chekin
        chekin_room_id: The Chekin room ID to delete

    Returns:
        Dict containing the result of the operation
    """
    try:
        # Get the room (may not exist if it was deleted)
        room_instance = None
        property_instance = None
        config = None

        try:
            room_instance = Room.objects.select_related(
                "property", "property__location"
            ).get(id=room_id)
            property_instance = room_instance.property
        except Room.DoesNotExist:
            logger.warning(f"Room {room_id} not found for Chekin room deletion")
            # We'll still try to delete from Chekin if we have the room ID

        # If we have a room, get the configuration
        if property_instance:
            try:
                config = ChekinConfig.objects.get(property=property_instance)
            except ChekinConfig.DoesNotExist:
                logger.warning(
                    f"No online check-in config found for property {property_instance.id}"
                )
                return {"success": False, "error": "Online check-in not configured"}

            # Check if Chekin is configured
            if not config.is_chekin_configured():
                logger.info(
                    f"Chekin not configured for property {property_instance.id}"
                )
                return {
                    "success": True,
                    "skipped": True,
                    "reason": "Chekin not configured",
                }

            # Initialize Chekin service
            chekin_service = ChekinAPIService(
                api_key=config.get_chekin_api_key(),
                environment=config.chekin_environment,
            )
        else:
            # If no room found, we need to use default configuration
            # This is a fallback scenario - in practice, we should store Chekin config separately
            logger.warning(
                f"No room found for {room_id}, cannot determine Chekin configuration"
            )
            return {"success": False, "error": "Cannot determine Chekin configuration"}

        # Delete the room from Chekin
        success = chekin_service.delete_room(chekin_room_id)

        if success and config:
            # Update the last sync time
            config.chekin_last_sync = timezone.now()
            config.save(update_fields=["chekin_last_sync"])

        # Log the successful action
        if property_instance:
            transaction.on_commit(
                lambda: log_action(
                    user=property_instance.staffs.first(),
                    property_id=property_instance.id,
                    action="delete",
                    description=f"Deleted Chekin room for room {room_id}",
                    status="successful",
                    details={"room_id": str(room_id), "chekin_room_id": chekin_room_id},
                )
            )

        logger.info(
            f"Successfully deleted Chekin room {chekin_room_id} for room {room_id}"
        )
        return {
            "success": True,
            "chekin_room_id": chekin_room_id,
            "room_id": str(room_id),
        }

    except Exception as e:
        logger.error(f"Error deleting Chekin room for room {room_id}: {str(e)}")

        # Log the failed action
        if property_instance:
            transaction.on_commit(
                lambda: log_action(
                    user=property_instance.staffs.first(),
                    property_id=property_instance.id,
                    action="delete",
                    description=f"Failed to delete Chekin room for room {room_id}",
                    status="failed",
                    details={
                        "room_id": str(room_id),
                        "chekin_room_id": chekin_room_id,
                        "error": str(e),
                    },
                )
            )

        # Retry logic
        if self.request.retries < self.max_retries:
            retry_delay = (2**self.request.retries) * 300
            logger.info(
                f"Retrying Chekin room deletion for room {room_id} in {retry_delay} seconds"
            )
            raise self.retry(exc=e, countdown=retry_delay)

        return {"success": False, "error": str(e)}
