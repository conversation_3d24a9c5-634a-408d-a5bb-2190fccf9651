import uuid

from apps.integrations.models import ChekinConfig
from apps.integrations.serializers.chekin import (
    ChekinConfigSerializer,
    ChekinLaunchSerializer,
)
from apps.stay.utils import validate_ownership
from django.shortcuts import get_object_or_404
from rest_framework import permissions, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response


class IsHostOfProperty(permissions.BasePermission):
    """Ensures the authenticated user owns (or is validated owner of) the target property.

    Rules:
    - For object-level checks (detail routes) the user must own the config's property.
    - For the custom launch action, a property_id must be supplied and user must own it.
    - For list/create: if creating, property_id required & ownership enforced; list restricted to authenticated users (optionally could be filtered elsewhere).
    """

    message = {
        "error": "You don't have access to this property's Chekin configuration."
    }

    def _validate_uuid(self, value: str, field: str) -> uuid.UUID:
        try:
            return uuid.UUID(str(value), version=4)
        except (ValueError, AttributeError, TypeError):
            raise ValidationError({field: "Invalid UUID provided."})

    def _owns_property(self, user, property_id: uuid.UUID) -> bool:
        return validate_ownership(user, property_id)

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # For unsafe methods or actions needing a specific property context, enforce ownership early.
        action = getattr(view, "action", None)

        if action in {"create", "launch"}:
            property_id = request.data.get("property_id") or request.data.get(
                "property"
            )
            if not property_id:
                self.message = {"error": "property_id is required."}
                return False
            try:
                pid = self._validate_uuid(property_id, "property_id")
            except ValidationError as e:
                self.message = e.detail
                return False
            if not self._owns_property(request.user, pid):
                self.message = {"error": "You are not the owner of this property."}
                return False
        return True

    def has_object_permission(self, request, view, obj: ChekinConfig):
        try:
            return self._owns_property(request.user, obj.property.id)
        except Exception:
            return False


class ChekinConfigViewSet(viewsets.ModelViewSet):
    queryset = ChekinConfig.objects.select_related("property")
    serializer_class = ChekinConfigSerializer
    permission_classes = [permissions.IsAuthenticated, IsHostOfProperty]

    @action(detail=False, methods=["POST"], url_path="launch")
    def launch(self, request):
        prop_id = request.data.get("property_id")
        reservation_id = request.data.get("reservation_id")
        cfg = get_object_or_404(
            ChekinConfig,
            property_id=prop_id,
            is_enabled=True,
            chekin_enabled=True,
        )

        housing_id = getattr(cfg.property, "chekin_housing_id", None)
        payload = {
            "sdkUrl": cfg.sdk_url(),
            "apiKey": cfg.effective_api_key(),
            "defaultLanguage": "en",
            "housingId": housing_id,
            "externalHousingId": str(cfg.property.id),
            "reservationId": reservation_id,
            "hiddenSections": [],
            "styles": (
                ".chekin-button { border-radius: 8px; }\n"
                ".chekin-primary { font-family: ui-sans-serif, system-ui; }\n"
            ),
        }
        return Response(ChekinLaunchSerializer(payload).data)

    @action(detail=True, methods=["POST"], url_path="status")
    def update_status_from_sdk(self, request, pk=None):
        cfg = self.get_object()
        t = request.data.get("type")
        changed_fields = []
        if t == "police_connected":
            cfg.alloggati_enabled = True
            changed_fields.append("alloggati_enabled")
        if t == "police_disconnected":
            cfg.alloggati_enabled = False
            changed_fields.append("alloggati_enabled")
        if t == "istat_connected":
            cfg.istat_enabled = True
            changed_fields.append("istat_enabled")
        if t == "istat_disconnected":
            cfg.istat_enabled = False
            changed_fields.append("istat_enabled")
        if changed_fields:
            cfg.save(update_fields=changed_fields + ["updated_at"])
        return Response({"ok": True})
