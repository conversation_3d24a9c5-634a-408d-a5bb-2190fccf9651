import logging
import time

import psutil
from celery import current_app as celery_app
from django.conf import settings
from django.core.cache import cache
from django.db import connections
from django.db.utils import OperationalError
from prometheus_client import Counter, Gauge, Histogram, Info

logger = logging.getLogger(__name__)

# Prometheus metrics
REQUEST_COUNT = Counter(
    "django_http_requests_total",
    "Total HTTP requests",
    ["method", "status", "endpoint"],
)

REQUEST_LATENCY = Histogram(
    "django_http_request_duration_seconds",
    "HTTP request latency",
    ["method", "endpoint"],
)

ACTIVE_REQUESTS = Gauge("django_http_requests_active", "Active HTTP requests")

DATABASE_CONNECTIONS = Gauge(
    "django_database_connections_total", "Total database connections", ["database"]
)

CACHE_OPERATIONS = Counter(
    "django_cache_operations_total", "Total cache operations", ["operation", "result"]
)

CELERY_TASKS = Counter(
    "celery_tasks_total", "Total Celery tasks", ["task_name", "state"]
)

CELERY_QUEUE_LENGTH = Gauge("celery_queue_length", "Celery queue length", ["queue"])

CELERY_WORKERS = Gauge("celery_workers_total", "Total Celery workers")

PROCESS_MEMORY = Gauge(
    "django_process_memory_usage_bytes", "Process memory usage in bytes"
)

PROCESS_CPU = Gauge("django_process_cpu_percent", "Process CPU usage percentage")

APP_INFO = Info("django_app_info", "Application information")


class MetricsCollector:
    """Collects various application metrics."""

    def __init__(self):
        self._process = None

    @property
    def process(self):
        if self._process is None:
            self._process = psutil.Process()
        return self._process

    def collect_system_metrics(self):
        """Collect lightweight system-level metrics."""
        try:
            # Memory usage - quick operation
            memory_info = self.process.memory_info()
            PROCESS_MEMORY.set(memory_info.rss)

            # CPU usage - use non-blocking call (interval=None for instant reading)
            # This is much faster but less accurate - acceptable for monitoring
            cpu_percent = self.process.cpu_percent(interval=None)
            if cpu_percent > 0:  # Only set if we get a valid reading
                PROCESS_CPU.set(cpu_percent)
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")

    def collect_database_metrics(self):
        """Collect lightweight database metrics - simplified for performance."""
        try:
            # Only check default database to reduce overhead
            default_db = settings.DATABASES.get("default", {})
            if default_db:
                try:
                    conn = connections["default"]
                    # Simple connection test instead of complex query
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        DATABASE_CONNECTIONS.labels(database="default").set(1)
                except OperationalError:
                    DATABASE_CONNECTIONS.labels(database="default").set(0)
        except Exception as e:
            logger.error(f"Error collecting database metrics: {e}")

    def collect_cache_metrics(self):
        """Lightweight cache connectivity test."""
        try:
            # Quick cache test without timing
            test_key = "metrics_health_check"
            cache.set(test_key, "ok", timeout=1)
            result = cache.get(test_key)
            cache.delete(test_key)

            # Simple success/failure tracking
            if result == "ok":
                CACHE_OPERATIONS.labels(
                    operation="health_check", result="success"
                ).inc()
            else:
                CACHE_OPERATIONS.labels(
                    operation="health_check", result="failure"
                ).inc()

        except Exception as e:
            logger.error(f"Error collecting cache metrics: {e}")
            CACHE_OPERATIONS.labels(operation="health_check", result="error").inc()

    def collect_celery_metrics(self):
        """Lightweight Celery metrics - reduced timeout and simplified checks."""
        try:
            # Very short timeout to prevent blocking
            inspect = celery_app.control.inspect(timeout=2.0)

            # Quick worker count check only
            active_workers = inspect.active()
            worker_count = len(active_workers) if active_workers else 0
            CELERY_WORKERS.set(worker_count)

            # Skip queue length checks as they can be expensive
            # Only set a basic queue metric if we have workers
            if worker_count > 0:
                CELERY_QUEUE_LENGTH.labels(queue="default").set(0)  # Placeholder

        except Exception as e:
            logger.error(f"Error collecting Celery metrics: {e}")
            # Set safe defaults on error
            CELERY_WORKERS.set(0)
            CELERY_QUEUE_LENGTH.labels(queue="default").set(0)

    def collect_all_metrics(self):
        """Collect all metrics."""
        self.collect_system_metrics()
        self.collect_database_metrics()
        self.collect_cache_metrics()
        self.collect_celery_metrics()


# Global metrics collector instance
metrics_collector = MetricsCollector()


def get_health_status():
    """Get application health status."""
    health = {"status": "healthy", "timestamp": time.time(), "checks": {}}

    # Database check
    try:
        from django.db import connection

        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        health["checks"]["database"] = "healthy"
    except Exception as e:
        health["checks"]["database"] = f"unhealthy: {str(e)}"
        health["status"] = "unhealthy"

    # Cache check
    try:
        cache.set("health_check", "ok", timeout=1)
        result = cache.get("health_check")
        if result == "ok":
            health["checks"]["cache"] = "healthy"
        else:
            health["checks"]["cache"] = "unhealthy: cache test failed"
            health["status"] = "unhealthy"
        cache.delete("health_check")
    except Exception as e:
        health["checks"]["cache"] = f"unhealthy: {str(e)}"
        health["status"] = "unhealthy"

    # Celery check
    try:
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        if active_workers:
            health["checks"]["celery"] = "healthy"
        else:
            health["checks"]["celery"] = "unhealthy: no active workers"
            health["status"] = "degraded"
    except Exception as e:
        health["checks"]["celery"] = f"unhealthy: {str(e)}"
        health["status"] = "degraded"

    return health
