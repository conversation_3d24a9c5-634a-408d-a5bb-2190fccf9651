import logging
import time

from django.utils.deprecation import MiddlewareMixin

from .metrics import ACTIVE_REQUESTS, REQUEST_COUNT, REQUEST_LATENCY

logger = logging.getLogger(__name__)


class PrometheusMiddleware(MiddlewareMixin):
    """Optimized Prometheus metrics collection with minimal overhead."""

    def __init__(self, get_response):
        super().__init__(get_response)
        self.slow_request_threshold = 3.0  # Only log very slow requests

    def process_request(self, request):
        """Start timing and increment active requests."""
        request._prometheus_start_time = time.time()
        request._prometheus_decremented = False
        ACTIVE_REQUESTS.inc()

    def process_response(self, request, response):
        """Collect essential metrics only."""
        try:
            if hasattr(request, "_prometheus_start_time"):
                duration = time.time() - request._prometheus_start_time

                # Simplified endpoint detection - use path if resolver_match unavailable
                endpoint = "unknown"
                if hasattr(request, "resolver_match") and request.resolver_match:
                    endpoint = request.resolver_match.view_name or request.path
                else:
                    endpoint = request.path

                # Record core metrics
                REQUEST_COUNT.labels(
                    method=request.method,
                    status=response.status_code,
                    endpoint=endpoint,
                ).inc()

                REQUEST_LATENCY.labels(
                    method=request.method, endpoint=endpoint
                ).observe(duration)

                # Decrement active requests
                if not getattr(request, "_prometheus_decremented", False):
                    ACTIVE_REQUESTS.dec()
                    request._prometheus_decremented = True

                # Only log extremely slow requests to reduce log noise
                if duration > self.slow_request_threshold:
                    logger.warning(
                        f"Very slow request: {request.method} {request.path} - {duration:.2f}s"
                    )

        except Exception as e:
            logger.error(f"PrometheusMiddleware error: {e}")
            # Ensure active requests counter is decremented
            if not getattr(request, "_prometheus_decremented", False):
                ACTIVE_REQUESTS.dec()
                request._prometheus_decremented = True

        return response

    def process_exception(self, request, exception):
        """Handle exceptions with minimal processing."""
        try:
            if hasattr(request, "_prometheus_start_time"):
                duration = time.time() - request._prometheus_start_time
                endpoint = getattr(request, "path", "unknown")

                REQUEST_COUNT.labels(
                    method=request.method, status=500, endpoint=endpoint
                ).inc()

                REQUEST_LATENCY.labels(
                    method=request.method, endpoint=endpoint
                ).observe(duration)

                # Decrement active requests
                if not getattr(request, "_prometheus_decremented", False):
                    ACTIVE_REQUESTS.dec()
                    request._prometheus_decremented = True

        except Exception as e:
            logger.error(f"PrometheusMiddleware exception handler error: {e}")
            # Ensure counter is decremented
            if not getattr(request, "_prometheus_decremented", False):
                ACTIVE_REQUESTS.dec()
                request._prometheus_decremented = True


class MetricsCollectionMiddleware(MiddlewareMixin):
    """
    Lightweight metrics collection with significantly reduced overhead.
    Collects only essential system metrics at longer intervals.
    """

    def __init__(self, get_response):
        super().__init__(get_response)
        self._last_collection = 0
        self._collection_interval = (
            300  # Collect metrics every 5 minutes instead of 30 seconds
        )
        self._error_count = 0
        self._max_errors = 5  # Stop trying after 5 consecutive errors

    def process_request(self, request):
        """Collect lightweight metrics at reduced frequency."""
        current_time = time.time()

        # Skip if we've had too many errors or not enough time has passed
        if (
            self._error_count >= self._max_errors
            or current_time - self._last_collection < self._collection_interval
        ):
            return None

        try:
            # Only collect essential system metrics, skip expensive operations
            from .metrics import metrics_collector

            metrics_collector.collect_system_metrics()  # Only system metrics, not DB/Celery
            self._last_collection = current_time
            self._error_count = 0  # Reset error count on success

        except Exception as e:
            self._error_count += 1
            # Simplified error logging
            logger.error(
                f"Metrics collection failed (attempt {self._error_count}): {e}"
            )

            # If we've hit max errors, log a warning and stop trying
            if self._error_count >= self._max_errors:
                logger.warning(
                    f"Metrics collection disabled after {self._max_errors} consecutive failures"
                )

        return None
