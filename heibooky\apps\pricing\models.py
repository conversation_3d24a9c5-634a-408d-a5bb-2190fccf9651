from datetime import timed<PERSON><PERSON>

from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models


class RatePlan(models.Model):
    """Model representing rate plans (pricing plans) for rooms."""

    MEAL_CHOICES = [
        (1, "All inclusive"),
        (2, "Breakfast"),
        (3, "Lunch"),
        (4, "Dinner"),
        (5, "American"),
        (6, "Bed & breakfast"),
        (7, "Buffet breakfast"),
        (8, "Caribbean breakfast"),
        (9, "Continental breakfast"),
        (10, "English breakfast"),
        (11, "European plan"),
        (12, "Family plan"),
        (13, "Full board"),
        (14, "Half board/modified American plan"),
        (15, "Room only (Default)"),
        (16, "Self catering"),
        (17, "Bermuda"),
        (18, "Dinner bed and breakfast plan"),
        (19, "Family American"),
        (20, "Modified"),
        (21, "Breakfast & lunch"),
        (22, "Full breakfast"),
    ]

    property = models.ForeignKey(
        "stay.Property", on_delete=models.CASCADE, related_name="rate_plans"
    )
    name = models.CharField(max_length=255, help_text="Name of the rate plan.")
    close_out_days = models.IntegerField(
        blank=True,
        null=True,
        help_text="Number of days before check-in when bookings are closed (0-30).",
    )
    checkin_time = models.TimeField(
        blank=True, null=True, help_text="Time of day when guests can check in."
    )
    close_out_time = models.TimeField(
        blank=True,
        null=True,
        help_text="Time of day when bookings are closed (required if close_out_days is 0).",
    )
    description = models.CharField(
        max_length=300, help_text="Description of the rate plan."
    )
    meal_plan = models.IntegerField(
        choices=MEAL_CHOICES, default=15, help_text="Select the meal plan."
    )
    is_active = models.BooleanField(default=True)
    is_onboarded = models.BooleanField(default=False)

    def clean(self):
        if self.close_out_days is not None and not (0 <= self.close_out_days <= 30):
            raise ValidationError("Close-out days must be between 0 and 30.")
        if self.close_out_days == 0 and self.close_out_time is None:
            raise ValidationError(
                "Close-out time is required if close-out days is set to 0."
            )

        if self.close_out_time:
            if self.close_out_time.minute not in [0, 30]:
                raise ValidationError(
                    "Close-out time must be in 30-minute intervals (e.g., 00:00, 00:30)."
                )

    def __str__(self):
        return f"{self.name} - {self.property.name}"


class RoomRate(models.Model):
    rate_plan = models.ForeignKey(
        RatePlan, on_delete=models.CASCADE, related_name="room_rates"
    )
    room = models.ForeignKey(
        "stay.Room", on_delete=models.CASCADE, related_name="room_rates"
    )
    rate = models.DecimalField(max_digits=10, decimal_places=2)
    room_amount = models.IntegerField()
    start_date = models.DateField()
    end_date = models.DateField()
    minimum_stay = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(730)]
    )
    maximum_stay = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(730)]
    )
    is_season = models.BooleanField()
    ticket_id = models.CharField(null=True, blank=True)
    is_onboarded = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def handle_overlapping_rates(self):
        """Handle any existing rates that overlap with this rate's date range."""
        overlapping_rates = RoomRate.objects.filter(
            room=self.room, start_date__lte=self.end_date, end_date__gte=self.start_date
        ).exclude(pk=self.pk)

        for existing_rate in overlapping_rates:
            # Case 1: Existing rate starts before and ends during new rate
            if (
                existing_rate.start_date < self.start_date
                and existing_rate.end_date <= self.end_date
            ):
                # Handle same-day rates: if existing rate is a same-day rate and ends on new rate's start date
                if (
                    existing_rate.start_date == existing_rate.end_date
                    and existing_rate.end_date == self.start_date
                ):
                    # Keep existing same-day rate, no adjustment needed
                    continue
                else:
                    existing_rate.end_date = self.start_date - timedelta(days=1)

            # Case 2: Existing rate starts during and ends after new rate
            elif (
                existing_rate.start_date >= self.start_date
                and existing_rate.end_date > self.end_date
            ):
                # Handle same-day rates: if new rate is a same-day rate and existing rate starts on new rate's end date
                if (
                    self.start_date == self.end_date
                    and existing_rate.start_date == self.end_date
                ):
                    # Keep existing rate, no adjustment needed
                    continue
                else:
                    existing_rate.start_date = self.end_date + timedelta(days=1)

            # Case 3: Existing rate is completely within new rate
            elif (
                existing_rate.start_date >= self.start_date
                and existing_rate.end_date <= self.end_date
            ):
                existing_rate.delete()
                continue

            # Case 4: Existing rate completely encompasses new rate
            elif (
                existing_rate.start_date < self.start_date
                and existing_rate.end_date > self.end_date
            ):
                # For same-day new rates, we need to handle splitting differently
                if self.start_date == self.end_date:
                    # Split only if the existing rate spans more than one day
                    if existing_rate.start_date != existing_rate.end_date:
                        # Create rate for after the single day
                        if self.end_date < existing_rate.end_date:
                            RoomRate.objects.create(
                                rate_plan=existing_rate.rate_plan,
                                room=existing_rate.room,
                                rate=existing_rate.rate,
                                room_amount=existing_rate.room_amount,
                                start_date=self.end_date + timedelta(days=1),
                                end_date=existing_rate.end_date,
                                minimum_stay=existing_rate.minimum_stay,
                                maximum_stay=existing_rate.maximum_stay,
                                is_season=existing_rate.is_season,
                                is_active=existing_rate.is_active,
                            )
                        # Adjust existing rate to end before the single day
                        if existing_rate.start_date < self.start_date:
                            existing_rate.end_date = self.start_date - timedelta(days=1)
                        else:
                            existing_rate.delete()
                            continue
                else:
                    # Split into two rates for multi-day new rates
                    RoomRate.objects.create(
                        rate_plan=existing_rate.rate_plan,
                        room=existing_rate.room,
                        rate=existing_rate.rate,
                        room_amount=existing_rate.room_amount,
                        start_date=self.end_date + timedelta(days=1),
                        end_date=existing_rate.end_date,
                        minimum_stay=existing_rate.minimum_stay,
                        maximum_stay=existing_rate.maximum_stay,
                        is_season=existing_rate.is_season,
                        is_active=existing_rate.is_active,
                    )
                    existing_rate.end_date = self.start_date - timedelta(days=1)

            if existing_rate.end_date >= existing_rate.start_date:
                existing_rate.save()
            else:
                existing_rate.delete()

    def save(self, *args, **kwargs):
        if not self.pk:  # Only for new rates
            super().save(*args, **kwargs)  # Save first to get a pk
            self.handle_overlapping_rates()
        else:
            super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.room} - {self.rate_plan}"
