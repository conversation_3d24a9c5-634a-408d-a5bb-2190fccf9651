from apps.reviews.views import ReviewsViewset, ReviewWebhookAPIView
from django.urls import include, path
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register(r"", ReviewsViewset)

# Place webhook BEFORE router URLs so 'webhook/' isn't mistaken for a pk by the router
urlpatterns = [
    path("webhook/", ReviewWebhookAPIView.as_view(), name="review_webhook"),
    path("", include(router.urls)),
]
