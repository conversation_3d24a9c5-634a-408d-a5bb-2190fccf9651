# Generated by Django 5.2.4 on 2025-07-22 20:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("stay", "0003_property_is_domorent"),
    ]

    operations = [
        migrations.AddField(
            model_name="property",
            name="cleaning_cost",
            field=models.DecimalField(
                decimal_places=2,
                default=80.0,
                help_text="Cleaning cost for Domorent properties (default: €80)",
                max_digits=10,
            ),
        ),
        migrations.AddField(
            model_name="property",
            name="domorent_percentage",
            field=models.DecimalField(
                decimal_places=2,
                default=25.0,
                help_text="Domorent commission percentage (default: 25%)",
                max_digits=5,
            ),
        ),
    ]
