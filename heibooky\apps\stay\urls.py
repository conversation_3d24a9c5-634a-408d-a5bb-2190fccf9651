from apps.stay.views import *
from django.urls import include, path
from rest_framework.routers import DefaultRouter

# Create a router and register our viewsets with it.
router = DefaultRouter()
router.register(r"locations", LocationViewSet, basename="location")
router.register(r"property", PropertyViewSet, basename="property")
router.register(r"team", TeamViewSet, basename="team")
router.register(r"photos", PhotoViewSet, basename="photos")
router.register(r"guest-info", GuestArrivalInfoViewSet, basename="guest-info")
router.register(r"rooms", RoomViewSet, basename="rooms")
router.register(r"room-amenities", RoomAmenityViewSet, basename="room-amenities")

# Property metadata uses property_id as lookup field
property_metadata_list = PropertyMetadataViewSet.as_view({"get": "list"})
property_metadata_detail = PropertyMetadataViewSet.as_view(
    {"get": "retrieve", "put": "update", "patch": "partial_update"}
)
property_metadata_property_info = PropertyMetadataViewSet.as_view(
    {"patch": "update_property_info"}
)
property_metadata_cancelation_policy = PropertyMetadataViewSet.as_view(
    {"patch": "update_cancelation_policy"}
)

# The API URLs are now determined automatically by the router.
urlpatterns = [
    path("", include(router.urls)),
    path("amenity/", PropertyAmenityAPIView.as_view(), name="amenities"),
    path(
        "settings-status/",
        PropertySettingsStatusView.as_view(),
        name="property-settings-status",
    ),
    path(
        "team/verify_invite/", VerifyTeamInviteView.as_view(), name="verify-team-invite"
    ),
    # Property metadata endpoints using property_id
    path("property-metadata/", property_metadata_list, name="property-metadata-list"),
    path(
        "property-metadata/<uuid:property_id>/",
        property_metadata_detail,
        name="property-metadata-detail",
    ),
    path(
        "property-metadata/<uuid:property_id>/property-info/",
        property_metadata_property_info,
        name="property-metadata-property-info",
    ),
    path(
        "property-metadata/<uuid:property_id>/cancelation-policy/",
        property_metadata_cancelation_policy,
        name="property-metadata-cancelation-policy",
    ),
]
