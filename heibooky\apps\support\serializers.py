import logging

from apps.users.models import User
from django.core.exceptions import ValidationError as DjangoValidationError
from django.core.validators import FileExtensionValidator
from rest_framework import serializers
from rest_framework.exceptions import ValidationError as DRFValidationError

from .models import Chat, MessageAttachment, SupportMessage

logger = logging.getLogger(__name__)


class MessageAttachmentSerializer(serializers.ModelSerializer):
    """Serializer for message attachments"""

    file_url = serializers.SerializerMethodField()
    file = serializers.FileField(write_only=True, required=False)

    class Meta:
        model = MessageAttachment
        fields = ["id", "file", "file_url", "file_name", "created_at"]
        extra_kwargs = {
            "file_name": {"required": False}  # Make file_name not required on input
        }

    def get_file_url(self, obj):
        return obj.file.url if obj.file else None

    def validate_file(self, value):
        """
        Validate file size and type with enhanced error handling
        """
        import logging

        logger = logging.getLogger(__name__)

        if not value:
            raise DRFValidationError("No file provided")

        try:
            # Check if the file has the required attributes
            if not hasattr(value, "size") or not hasattr(value, "content_type"):
                logger.error(
                    "Invalid file object: missing size or content_type attributes"
                )
                raise DRFValidationError("Invalid file format")

            # Log file information for debugging
            logger.info(
                f"Validating file: {value.name}, size: {value.size}, type: {value.content_type}"
            )

            # Create a temporary instance for validation
            instance = MessageAttachment(
                file=value, file_size=value.size, content_type=value.content_type
            )

            # Run the model's validation
            instance.clean()
            return value

        except DjangoValidationError as e:
            logger.warning(
                f"File validation failed for {getattr(value, 'name', 'unknown')}: {e.messages}"
            )
            raise DRFValidationError(e.messages)
        except Exception as e:
            logger.error(
                f"Unexpected error validating file {getattr(value, 'name', 'unknown')}: {str(e)}"
            )
            raise DRFValidationError(f"File validation error: {str(e)}")

    def create(self, validated_data):
        file_obj = validated_data.get("file")
        # Automatically set file metadata from the uploaded file
        if file_obj:
            validated_data.update(
                {
                    "file_name": file_obj.name,
                    "file_size": file_obj.size,
                    "content_type": file_obj.content_type,
                }
            )
        return super().create(validated_data)


class ChatSerializer(serializers.ModelSerializer):
    """Serializer for chat objects"""

    user_name = serializers.SerializerMethodField()
    user_email = serializers.SerializerMethodField()
    last_message = serializers.SerializerMethodField()

    class Meta:
        model = Chat
        fields = [
            "id",
            "user",
            "user_name",
            "user_email",
            "status",
            "priority",
            "created_at",
            "updated_at",
            "last_message_at",
            "last_message",
        ]

    def get_user_name(self, obj):
        return obj.user.name if obj.user else None

    def get_user_email(self, obj):
        return obj.user.email if obj.user else None

    def get_last_message(self, obj):
        last_msg = obj.messages.order_by("-created_at").first()
        if last_msg:
            return {
                "message": last_msg.message,
                "sender": last_msg.sender,
                "created_at": last_msg.created_at,
            }
        return None


class ChatListSerializer(serializers.ModelSerializer):
    """Serializer for listing chats in a condensed format"""

    user_id = serializers.UUIDField(source="user.id")
    user_name = serializers.CharField(source="user.name")
    user_email = serializers.CharField(source="user.email")
    last_message = serializers.SerializerMethodField()
    last_updated = serializers.DateTimeField(source="last_message_at")

    class Meta:
        model = Chat
        fields = [
            "id",
            "user_id",
            "user_name",
            "user_email",
            "last_message",
            "priority",
            "status",
            "last_updated",
        ]

    def get_last_message(self, obj):
        last_msg = obj.messages.order_by("-created_at").first()
        if last_msg:
            return last_msg.message[:100]  # Truncate to first 100 chars
        return ""


class SupportMessageSerializer(serializers.ModelSerializer):
    """Serializer for support messages with attachments"""

    attachments = MessageAttachmentSerializer(many=True, read_only=True)
    sender = serializers.ReadOnlyField()
    chat_id = serializers.UUIDField(write_only=True, required=False)

    class Meta:
        model = SupportMessage
        fields = [
            "id",
            "chat_id",
            "message",
            "is_from_support",
            "is_read",
            "created_at",
            "updated_at",
            "sender",
            "attachments",
        ]

    def validate(self, attrs):
        """Validate the support message data"""
        # Log validation attempt
        logger.info(f"Validating support message data: {attrs}")

        # Ensure message is provided
        if not attrs.get("message"):
            logger.warning("Support message validation failed: No message provided")
            raise serializers.ValidationError({"message": "Message is required"})

        logger.info("Support message validation successful")
        return attrs

    def create(self, validated_data):
        try:
            request = self.context.get("request")
            if not request:
                raise DRFValidationError("Request context is required")

            # Get attachments from request.FILES
            attachments_data = request.FILES.getlist("attachments", [])
            logger.info(
                f"Processing support message with {len(attachments_data)} attachments"
            )

            # Validate total attachments size
            if attachments_data:
                try:
                    total_size = sum(
                        getattr(attachment, "size", 0)
                        for attachment in attachments_data
                    )
                    max_total_size = 25 * 1024 * 1024  # 25MB total

                    logger.info(
                        f"Total attachment size: {total_size/1024/1024:.2f}MB of {max_total_size/1024/1024}MB limit"
                    )

                    if total_size > max_total_size:
                        raise DRFValidationError(
                            "Total attachments size cannot exceed 25MB"
                        )
                except Exception as e:
                    logger.error(f"Error validating attachment sizes: {str(e)}")
                    raise DRFValidationError(f"Error processing attachments: {str(e)}")

            # Get chat or create a new one if not provided
            chat_id = validated_data.pop("chat_id", None)
            if chat_id:
                try:
                    chat = Chat.objects.exclude(status="resolved").get(id=chat_id)
                except Chat.DoesNotExist:
                    raise DRFValidationError(f"Chat with ID {chat_id} does not exist")
            else:
                # Create chat for the current user with pending status
                chat = Chat.objects.create(user=request.user, status="pending")

            is_from_support = request.user.is_staff

            # Create the message
            message = SupportMessage.objects.create(
                chat=chat,
                message=validated_data.get("message", ""),
                is_from_support=is_from_support,
            )
            logger.info(f"Created support message ID: {message.id} in chat: {chat.id}")

            # Handle attachments with validation
            successful_attachments = 0
            for i, attachment in enumerate(attachments_data):
                try:
                    logger.info(
                        f"Processing attachment {i+1}/{len(attachments_data)}: {attachment.name}"
                    )

                    # Validate the attachment
                    attachment_serializer = MessageAttachmentSerializer(
                        data={"file": attachment}
                    )
                    attachment_serializer.is_valid(raise_exception=True)

                    # Save the validated attachment
                    attachment_obj = attachment_serializer.save(message=message)
                    successful_attachments += 1

                    logger.info(
                        f"Successfully saved attachment ID: {attachment_obj.id}, file: {attachment_obj.file_name}"
                    )
                except Exception as e:
                    logger.error(f"Error processing attachment {i+1}: {str(e)}")
                    # Continue processing other attachments instead of failing completely

            logger.info(
                f"Processed {successful_attachments}/{len(attachments_data)} attachments successfully"
            )
            return message

        except DjangoValidationError as e:
            logger.error(f"Django validation error: {e.messages}")
            raise DRFValidationError(e.messages)
        except DRFValidationError:
            # Re-raise DRF validation errors
            raise
        except Exception as e:
            logger.error(
                f"Unexpected error creating support message: {str(e)}", exc_info=True
            )
            raise DRFValidationError(f"Error creating support message: {str(e)}")


class SupportMessageListSerializer(serializers.ModelSerializer):
    """Serializer for listing messages in a chat thread"""

    sender = serializers.ReadOnlyField()
    user_name = serializers.SerializerMethodField()
    attachments = MessageAttachmentSerializer(many=True, read_only=True)
    chat_id = serializers.UUIDField(source="chat.id")

    class Meta:
        model = SupportMessage
        fields = [
            "id",
            "chat_id",
            "message",
            "is_read",
            "created_at",
            "sender",
            "user_name",
            "attachments",
        ]

    def get_user_name(self, obj):
        return obj.chat.user.name if obj.chat and obj.chat.user else None


class SupportMessageCreateSerializer(serializers.Serializer):
    """Serializer for admin creating support messages to users"""

    user_id = serializers.CharField(required=True)
    message = serializers.CharField(required=True)
    attachments = serializers.ListField(
        child=serializers.FileField(
            validators=[
                FileExtensionValidator(
                    allowed_extensions=MessageAttachment.ALLOWED_EXTENSIONS
                )
            ],
            max_length=MessageAttachment.MAX_SIZE,
        ),
        required=False,
    )

    def validate_user_id(self, value):
        try:
            user = User.objects.get(id=value)
            return user
        except User.DoesNotExist:
            raise serializers.ValidationError("User with this ID does not exist.")

    def create(self, validated_data):
        user = validated_data.get(
            "user_id"
        )  # Already a User instance from validate_user_id
        message_text = validated_data.get("message")
        attachments_data = validated_data.get("attachments", [])

        # Get or create a chat for this user
        chat = Chat.get_or_create_chat(user)

        # Create the support message
        message = SupportMessage.objects.create(
            chat=chat, message=message_text, is_from_support=True
        )

        # Update chat status to sent for admin-initiated messages
        chat.status = "sent"
        chat.save(update_fields=["status"])

        # Create attachments if any
        for attachment in attachments_data:
            MessageAttachment.objects.create(
                message=message, file=attachment, file_name=attachment.name
            )

        return message
