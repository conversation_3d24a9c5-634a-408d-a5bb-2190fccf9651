"""
Tests for support analytics endpoints
"""

import datetime
from unittest.mock import patch

from apps.support.models import Chat, SupportMessage
from apps.users.models import User
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient


class SupportAnalyticsTest(TestCase):
    def setUp(self):
        self.client = APIClient()

        # Create test users
        self.staff_user = User.objects.create_user(
            email="<EMAIL>", name="Staff User", is_staff=True
        )
        self.regular_user = User.objects.create_user(
            email="<EMAIL>", name="Regular User"
        )

        # Create test chats
        self.chat1 = Chat.objects.create(
            user=self.regular_user, status="resolved", priority="high"
        )
        self.chat2 = Chat.objects.create(
            user=self.regular_user, status="pending", priority="medium"
        )

        # Create test messages
        self.user_msg = SupportMessage.objects.create(
            chat=self.chat1, message="Need help", is_from_support=False
        )
        self.support_msg = SupportMessage.objects.create(
            chat=self.chat1, message="How can I help?", is_from_support=True
        )

    def test_analytics_summary_requires_staff(self):
        """Test that analytics summary requires staff permission"""
        self.client.force_authenticate(user=self.regular_user)
        url = reverse("support-analytics-summary")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_analytics_summary_success(self):
        """Test analytics summary endpoint returns correct data"""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse("support-analytics-summary")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        # Check required fields are present
        required_fields = [
            "resolved_chats",
            "average_response_time_minutes",
            "active_agents_this_week",
            "total_messages_sent",
            "high_priority_chats",
            "average_chats_per_day",
        ]
        for field in required_fields:
            self.assertIn(field, data)

    def test_analytics_enhanced_success(self):
        """Test enhanced analytics endpoint returns correct data"""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse("support-analytics-enhanced")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        # Check required fields are present
        required_fields = [
            "total",
            "pending",
            "inProgress",
            "resolved",
            "highPriority",
            "pendingTrend",
            "resolvedTrend",
            "totalTrend",
            "averageResponseTime",
            "responseTimeTrend",
            "resolutionRate",
            "resolutionRateTrend",
            "activeAgents",
            "averageChatsPerDay",
            "totalMessages",
            "customerSatisfaction",
            "satisfactionTrend",
        ]
        for field in required_fields:
            self.assertIn(field, data)

    def test_analytics_by_priority_success(self):
        """Test analytics by priority endpoint returns correct data"""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse("support-analytics-by-priority")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn("priority_stats", data)
        priority_stats = data["priority_stats"]

        # Should have stats for all priority levels
        self.assertEqual(len(priority_stats), 4)

        # Check each priority stat has required fields
        for stat in priority_stats:
            required_fields = [
                "priority",
                "count",
                "average_response_time",
                "resolution_rate",
                "average_resolution_time",
            ]
            for field in required_fields:
                self.assertIn(field, stat)

    def test_analytics_timeseries_success(self):
        """Test timeseries analytics endpoint returns correct data"""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse("support-analytics-timeseries")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        # Check required fields are present
        required_fields = [
            "dates",
            "total",
            "pending",
            "inProgress",
            "resolved",
            "pendingTrend",
            "resolvedTrend",
            "highPriority",
            "messageVolume",
            "userMessages",
            "supportMessages",
            "responseTime",
            "responseTimeTrend",
        ]
        for field in required_fields:
            self.assertIn(field, data)

    def test_analytics_timeseries_with_parameters(self):
        """Test timeseries analytics with custom parameters"""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse("support-analytics-timeseries")

        # Test with custom days
        response = self.client.get(url, {"days": 7})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test with custom interval
        response = self.client.get(url, {"interval": "weekly"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_analytics_timeseries_invalid_parameters(self):
        """Test timeseries analytics with invalid parameters"""
        self.client.force_authenticate(user=self.staff_user)
        url = reverse("support-analytics-timeseries")

        # Test invalid days
        response = self.client.get(url, {"days": "invalid"})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test invalid interval
        response = self.client.get(url, {"interval": "invalid"})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test days out of range
        response = self.client.get(url, {"days": 500})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
