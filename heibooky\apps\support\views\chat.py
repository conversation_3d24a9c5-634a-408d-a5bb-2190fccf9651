import logging

from apps.support.models import Chat, SupportMessage
from apps.support.serializers import (
    ChatListSerializer,
    ChatSerializer,
    SupportMessageCreateSerializer,
    SupportMessageListSerializer,
    SupportMessageSerializer,
)
from apps.users.permissions import IsStaffPermission
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, JSONParser, MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

logger = logging.getLogger(__name__)


class ChatPagination(PageNumberPagination):
    """Pagination for chat listings"""

    page_size = 20
    page_size_query_param = "page_size"
    max_page_size = 100


class MessagePagination(PageNumberPagination):
    """Pagination for message listings"""

    page_size = 50
    page_size_query_param = "page_size"
    max_page_size = 200


class ChatViewSet(viewsets.ModelViewSet):
    """
    API endpoint for support chats

    list:
        Returns a list of all support chats for staff members or
        the current user's chat for regular users (excluding resolved)
    retrieve:
        Returns details of a specific chat
    create:
        Creates a new chat (staff only)
    update:
        Updates chat status/priority (staff only)
    """

    serializer_class = ChatSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = ChatPagination
    filter_backends = [filters.OrderingFilter, filters.SearchFilter]
    search_fields = ["user__email", "user__name"]
    ordering_fields = ["last_message_at", "created_at", "priority", "status"]
    ordering = ["-last_message_at"]

    def get_queryset(self):
        """Return all chats for staff, own chat for regular users (excluding resolved)"""
        user = self.request.user

        if user.is_staff:
            # Staff can see all chats, excluding resolved ones
            return Chat.objects.all().exclude(status="resolved")
        else:
            # Regular users can only see their own chats, excluding resolved ones
            return Chat.objects.filter(user=user).exclude(status="resolved")

    def get_serializer_class(self):
        """Return appropriate serializer class"""
        if self.action == "list":
            return ChatListSerializer
        return ChatSerializer

    @action(detail=False, methods=["get"], permission_classes=[IsStaffPermission])
    def all(self, request):
        """List all chats for staff without pagination (excluding resolved)"""
        queryset = self.filter_queryset(self.get_queryset())
        serializer = ChatListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def resolve(self, request, pk=None):
        """Mark a chat as resolved"""
        chat = self.get_object()

        # Only staff can mark chats as resolved, or users can mark their own chats
        if not request.user.is_staff and request.user != chat.user:
            return Response(
                {"error": "You don't have permission to resolve this chat"},
                status=status.HTTP_403_FORBIDDEN,
            )

        chat.status = "resolved"
        chat.save(update_fields=["status"])

        return Response({"status": "resolved"})

    @action(detail=True, methods=["post"])
    def prioritize(self, request, pk=None):
        """Update the priority of a chat (staff only)"""
        if not request.user.is_staff:
            return Response(
                {"error": "Only staff can set chat priority"},
                status=status.HTTP_403_FORBIDDEN,
            )

        chat = self.get_object()
        priority = request.data.get("priority")

        if priority not in dict(Chat.PRIORITY_CHOICES):
            return Response(
                {"error": "Invalid priority value"}, status=status.HTTP_400_BAD_REQUEST
            )

        chat.priority = priority
        chat.save(update_fields=["priority"])

        return Response({"priority": priority})


class MessageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for support messages within a chat

    list:
        Returns a list of messages for a specific chat
    create:
        Creates a new message in a chat
    """

    serializer_class = SupportMessageSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = MessagePagination
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ["created_at"]
    ordering = ["created_at"]

    def get_queryset(self):
        """Return messages filtered by chat_id"""
        chat_id = self.request.query_params.get("chat_id")
        user = self.request.user

        # Base queryset
        queryset = SupportMessage.objects.select_related("chat")

        # If chat_id is provided, filter by it
        if chat_id:
            queryset = queryset.filter(chat_id=chat_id)

        # Apply permissions
        if user.is_staff:
            # Staff can see all messages, excluding resolved ones
            return queryset.exclude(chat__status="resolved")
        else:
            # Regular users can only see their own messages from non-resolved chats
            return queryset.filter(chat__user=user).exclude(chat__status="resolved")

    def get_serializer_class(self):
        """Return appropriate serializer class"""
        if self.action == "list":
            return SupportMessageListSerializer
        return SupportMessageSerializer

    def perform_create(self, serializer):
        """Create a new message and broadcast via WebSocket"""
        message = serializer.save()
        chat = message.chat

        # Broadcast message via WebSocket
        try:
            channel_layer = get_channel_layer()

            # Prepare attachments data for WebSocket
            attachments_data = []
            for attachment in message.attachments.all():
                attachments_data.append(
                    {
                        "id": str(attachment.id),
                        "file_name": attachment.file_name,
                        "file_size": attachment.file_size,
                        "content_type": attachment.content_type,
                        "file_url": attachment.file.url if attachment.file else None,
                    }
                )

            # Prepare message data for WebSocket
            message_data = {
                "id": str(message.id),
                "message": message.message,
                "sender": message.sender,
                "timestamp": message.created_at.isoformat(),
                "chat_id": str(chat.id),
                "status": chat.status,
                "priority": chat.priority,
                "attachments": attachments_data,  # Add attachments
            }

            # Send to the chat group (for both user and support staff)
            async_to_sync(channel_layer.group_send)(
                f"support_chat_{chat.user.id}",
                {
                    "type": "chat.message",
                    "message": message_data["message"],
                    "sender": message_data["sender"],
                    "timestamp": message_data["timestamp"],
                    "id": message_data["id"],
                    "status": message_data["status"],
                    "priority": message_data["priority"],
                    "attachments": message_data["attachments"],
                },
            )

            logger.info(f"WebSocket message sent to group support_chat_{chat.user.id}")

        except Exception as e:
            logger.error(f"Error sending WebSocket message: {str(e)}")

        return message

    @action(detail=False, methods=["post"], permission_classes=[IsStaffPermission])
    def send_to_user(self, request):
        """Staff-only endpoint to send a message to a user"""
        serializer = SupportMessageCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        message = serializer.save()

        # Return the created message
        return Response(
            SupportMessageSerializer(message).data, status=status.HTTP_201_CREATED
        )

    @action(detail=False, methods=["post"])
    def upload_progress(self, request):
        """Track file upload progress"""
        message_id = request.data.get("message_id")
        progress = request.data.get("progress", 0)

        if not message_id:
            return Response(
                {"error": "message_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Broadcast upload progress via WebSocket
            channel_layer = get_channel_layer()

            # Get the chat from message
            message = SupportMessage.objects.select_related("chat").get(id=message_id)

            async_to_sync(channel_layer.group_send)(
                f"support_chat_{message.chat.user.id}",
                {
                    "type": "upload_progress",
                    "message_id": str(message_id),
                    "progress": progress,
                    "sender": "support" if request.user.is_staff else "user",
                },
            )

            return Response({"status": "progress_sent"})

        except SupportMessage.DoesNotExist:
            return Response(
                {"error": "Message not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error tracking upload progress: {str(e)}")
            return Response(
                {"error": "Failed to track progress"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
