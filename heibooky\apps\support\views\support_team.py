import datetime
import logging

import pandas as pd
from apps.support.models import Chat, SupportMessage
from apps.users.permissions import IsStaffPermission
from django.core.cache import cache
from django.db.models import Avg, Count, Q
from django.db.models.functions import TruncDay
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response

logger = logging.getLogger(__name__)


@api_view(["GET"])
@permission_classes([IsStaffPermission])
def support_analytics_summary(request):
    """
    Returns summary analytics for support team
    """
    cache_key = "support_analytics_summary"
    cached_data = cache.get(cache_key)

    if cached_data:
        return Response(cached_data)

    try:
        # Get resolved chats
        resolved_chats = Chat.objects.filter(status="resolved").count()

        # Calculate average response time
        # First, get all user messages
        user_messages = SupportMessage.objects.filter(is_from_support=False).order_by(
            "chat__user", "created_at"
        )

        # For each user message, find the next staff reply
        response_times = []

        for msg in user_messages:
            # Find the next support message after this user message
            next_response = (
                SupportMessage.objects.filter(
                    chat=msg.chat, is_from_support=True, created_at__gt=msg.created_at
                )
                .order_by("created_at")
                .first()
            )

            if next_response:
                # Calculate time difference in minutes
                time_diff = (
                    next_response.created_at - msg.created_at
                ).total_seconds() / 60
                response_times.append(time_diff)

        # Calculate average response time
        avg_response_time = 0
        if response_times:
            avg_response_time = round(sum(response_times) / len(response_times))

        # Count active agents this week
        today = timezone.now()
        week_start = today - datetime.timedelta(days=today.weekday())
        active_agents = (
            SupportMessage.objects.filter(
                is_from_support=True, created_at__gte=week_start
            )
            .values("chat__user")
            .distinct()
            .count()
        )

        # Count total messages sent
        total_messages = SupportMessage.objects.count()

        # Count high priority chats
        high_priority_chats = Chat.objects.filter(
            priority__in=["high", "urgent"]
        ).count()

        # Calculate average chats per day over the last 30 days
        thirty_days_ago = today - datetime.timedelta(days=30)
        total_chats_30_days = Chat.objects.filter(
            created_at__gte=thirty_days_ago
        ).count()
        chats_per_day = round(total_chats_30_days / 30, 2)

        data = {
            "resolved_chats": resolved_chats,
            "average_response_time_minutes": avg_response_time,
            "active_agents_this_week": active_agents,
            "total_messages_sent": total_messages,
            "high_priority_chats": high_priority_chats,
            "average_chats_per_day": chats_per_day,
        }

        # Cache the results
        cache.set(cache_key, data, 60 * 5)  # Cache for 5 minutes

        return Response(data)

    except Exception as e:
        logger.error(
            f"Error getting support analytics summary: {str(e)}", exc_info=True
        )
        return Response(
            {"error": "Failed to retrieve support analytics"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsStaffPermission])
def support_analytics_timeseries(request):
    """
    Returns time-series analytics for support team dashboard.

    Request Parameters:
    - days (optional): Number of days to retrieve (default: 30)
    - interval (optional): Data interval - 'daily', 'weekly', 'monthly' (default: 'daily')

    Includes the following data:
    - total: Total number of chats over time
    - pending: Number of pending chats over time
    - inProgress: Number of in-progress chats over time
    - resolved: Number of resolved chats over time
    - pendingTrend: Trend data for pending chats
    - resolvedTrend: Trend data for resolved chats
    - highPriority: Number of high/urgent priority chats over time
    - messageVolume: Number of messages sent over time
    - responseTime: Average response time over time
    """
    # Validate parameters
    try:
        days = int(request.query_params.get("days", 30))
        if days < 1 or days > 365:
            return Response(
                {"error": "Days parameter must be between 1 and 365"},
                status=status.HTTP_400_BAD_REQUEST,
            )
    except ValueError:
        return Response(
            {"error": "Days parameter must be a valid integer"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    interval = request.query_params.get("interval", "daily")
    if interval not in ["daily", "weekly", "monthly"]:
        return Response(
            {"error": "Invalid interval. Must be 'daily', 'weekly', or 'monthly'"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    cache_key = f"support_analytics_timeseries_{days}_{interval}"
    cached_data = cache.get(cache_key)

    if cached_data:
        return Response(cached_data)

    try:
        # Default: last 30 days
        end_date = timezone.now().date()
        start_date = end_date - datetime.timedelta(days=days)

        # Create frequency mapping for pandas
        freq_map = {"daily": "D", "weekly": "W", "monthly": "M"}
        freq = freq_map[interval]

        # Create a date range for the requested period
        date_range = pd.date_range(start=start_date, end=end_date, freq=freq)
        date_str_list = [date.strftime("%Y-%m-%d") for date in date_range]

        # Query chats with date aggregation
        chats_by_date = (
            Chat.objects.filter(
                created_at__date__gte=start_date, created_at__date__lte=end_date
            )
            .annotate(date=TruncDay("created_at"))
            .values("date")
            .annotate(
                total_count=Count("id"),
                pending_count=Count("id", filter=Q(status="pending")),
                in_progress_count=Count("id", filter=Q(status="in_progress")),
                resolved_count=Count("id", filter=Q(status="resolved")),
                high_priority_count=Count(
                    "id", filter=Q(priority__in=["high", "urgent"])
                ),
            )
            .order_by("date")
        )

        # Query messages volume by date
        messages_by_date = (
            SupportMessage.objects.filter(
                created_at__date__gte=start_date, created_at__date__lte=end_date
            )
            .annotate(date=TruncDay("created_at"))
            .values("date")
            .annotate(
                message_count=Count("id"),
                user_message_count=Count("id", filter=Q(is_from_support=False)),
                support_message_count=Count("id", filter=Q(is_from_support=True)),
            )
            .order_by("date")
        )

        # Initialize data structures
        total_data = {date: 0 for date in date_str_list}
        pending_data = {date: 0 for date in date_str_list}
        in_progress_data = {date: 0 for date in date_str_list}
        resolved_data = {date: 0 for date in date_str_list}
        high_priority_data = {date: 0 for date in date_str_list}
        message_volume_data = {date: 0 for date in date_str_list}
        user_message_data = {date: 0 for date in date_str_list}
        support_message_data = {date: 0 for date in date_str_list}
        response_time_data = {date: None for date in date_str_list}

        # Fill in chat status data
        for entry in chats_by_date:
            date_str = entry["date"].strftime("%Y-%m-%d")
            total_data[date_str] = entry["total_count"]
            pending_data[date_str] = entry["pending_count"]
            in_progress_data[date_str] = entry["in_progress_count"]
            resolved_data[date_str] = entry["resolved_count"]
            high_priority_data[date_str] = entry["high_priority_count"]

        # Fill in message volume data
        for entry in messages_by_date:
            date_str = entry["date"].strftime("%Y-%m-%d")
            message_volume_data[date_str] = entry["message_count"]
            user_message_data[date_str] = entry["user_message_count"]
            support_message_data[date_str] = entry["support_message_count"]

        # Calculate response time by date
        for date_obj in date_range:
            date_start = datetime.datetime.combine(
                date_obj.date(),
                datetime.time.min,
                tzinfo=timezone.get_current_timezone(),
            )
            date_end = datetime.datetime.combine(
                date_obj.date(),
                datetime.time.max,
                tzinfo=timezone.get_current_timezone(),
            )
            date_str = date_obj.strftime("%Y-%m-%d")

            # Get user messages for this day
            day_user_messages = SupportMessage.objects.filter(
                is_from_support=False, created_at__range=(date_start, date_end)
            )

            response_times_for_day = []

            for msg in day_user_messages:
                # Find the next support response
                next_response = (
                    SupportMessage.objects.filter(
                        chat=msg.chat,
                        is_from_support=True,
                        created_at__gt=msg.created_at,
                    )
                    .order_by("created_at")
                    .first()
                )

                if next_response:
                    # Calculate time difference in minutes
                    time_diff = (
                        next_response.created_at - msg.created_at
                    ).total_seconds() / 60
                    response_times_for_day.append(time_diff)

            # Calculate average response time for this day
            if response_times_for_day:
                response_time_data[date_str] = round(
                    sum(response_times_for_day) / len(response_times_for_day)
                )
            else:
                response_time_data[date_str] = 0

        # Calculate trends using pandas
        pending_df = pd.Series(list(pending_data.values()))
        resolved_df = pd.Series(list(resolved_data.values()))
        response_time_df = pd.Series(
            [rt if rt is not None else 0 for rt in response_time_data.values()]
        )

        # Use rolling average for trend calculation (7-day window)
        window_size = min(7, len(pending_df))
        pending_trend = pending_df.rolling(window=window_size).mean().fillna(0).tolist()
        resolved_trend = (
            resolved_df.rolling(window=window_size).mean().fillna(0).tolist()
        )
        response_time_trend = (
            response_time_df.rolling(window=window_size).mean().fillna(0).tolist()
        )

        # Format response data
        data = {
            "dates": date_str_list,
            "total": list(total_data.values()),
            "pending": list(pending_data.values()),
            "inProgress": list(in_progress_data.values()),
            "resolved": list(resolved_data.values()),
            "pendingTrend": pending_trend,
            "resolvedTrend": resolved_trend,
            "highPriority": list(high_priority_data.values()),
            "messageVolume": list(message_volume_data.values()),
            "userMessages": list(user_message_data.values()),
            "supportMessages": list(support_message_data.values()),
            "responseTime": list(response_time_data.values()),
            "responseTimeTrend": response_time_trend,
        }

        # Cache the results with the days parameter in the key
        cache.set(cache_key, data, 60 * 5)  # Cache for 5 minutes

        return Response(data)

    except Exception as e:
        logger.error(
            f"Error getting support analytics timeseries: {str(e)}", exc_info=True
        )
        return Response(
            {"error": "Failed to retrieve support analytics timeseries"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsStaffPermission])
def support_analytics_by_priority(request):
    """
    Returns analytics data grouped by priority levels.

    This endpoint provides:
    - Distribution of chats by priority level
    - Average response time by priority level
    - Resolution rate by priority level
    """
    cache_key = "support_analytics_by_priority"
    cached_data = cache.get(cache_key)

    if cached_data:
        return Response(cached_data)

    try:
        # Get distribution of chats by priority level
        # Calculate stats by priority level
        priority_stats = []

        for priority_level in ["low", "medium", "high", "urgent"]:
            # Get chats of this priority
            priority_chats = Chat.objects.filter(priority=priority_level)
            total_chats = priority_chats.count()

            if total_chats == 0:
                priority_stats.append(
                    {
                        "priority": priority_level,
                        "count": 0,
                        "average_response_time": 0,
                        "resolution_rate": 0,
                        "average_resolution_time": 0,
                    }
                )
                continue

            # Calculate resolved percentage
            resolved_chats = priority_chats.filter(status="resolved").count()
            resolution_rate = (resolved_chats / total_chats) if total_chats > 0 else 0

            # Calculate average response time for this priority
            response_times = []
            for chat in priority_chats:
                user_messages = SupportMessage.objects.filter(
                    chat=chat, is_from_support=False
                ).order_by("created_at")

                for msg in user_messages:
                    next_response = (
                        SupportMessage.objects.filter(
                            chat=chat,
                            is_from_support=True,
                            created_at__gt=msg.created_at,
                        )
                        .order_by("created_at")
                        .first()
                    )

                    if next_response:
                        time_diff = (
                            next_response.created_at - msg.created_at
                        ).total_seconds() / 60
                        response_times.append(time_diff)

            avg_response_time = (
                round(sum(response_times) / len(response_times))
                if response_times
                else 0
            )

            # Calculate average resolution time (from first message to resolved status)
            resolution_times = []
            for chat in priority_chats.filter(status="resolved"):
                first_message = (
                    SupportMessage.objects.filter(chat=chat)
                    .order_by("created_at")
                    .first()
                )
                if first_message:
                    resolution_time = (
                        chat.updated_at - first_message.created_at
                    ).total_seconds() / 3600  # in hours
                    resolution_times.append(resolution_time)

            avg_resolution_time = (
                round(sum(resolution_times) / len(resolution_times), 1)
                if resolution_times
                else 0
            )

            priority_stats.append(
                {
                    "priority": priority_level,
                    "count": total_chats,
                    "average_response_time": avg_response_time,
                    "resolution_rate": round(resolution_rate, 3),
                    "average_resolution_time": avg_resolution_time,  # in hours
                }
            )

        data = {"priority_stats": priority_stats}

        # Cache the results
        cache.set(cache_key, data, 60 * 5)  # Cache for 5 minutes

        return Response(data)

    except Exception as e:
        logger.error(
            f"Error getting support analytics by priority: {str(e)}", exc_info=True
        )
        return Response(
            {"error": "Failed to retrieve support analytics by priority"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsStaffPermission])
def support_analytics_enhanced(request):
    """
    Returns comprehensive analytics data combining all metrics needed for the stats cards and overview section.

    This endpoint provides:
    - Total counts by status
    - Trend calculations
    - Response time metrics
    - Resolution rate
    - Active agents
    - Customer satisfaction metrics
    """
    cache_key = "support_analytics_enhanced"
    cached_data = cache.get(cache_key)

    if cached_data:
        return Response(cached_data)

    try:
        today = timezone.now()

        # Calculate date ranges for trend comparison
        current_period_start = today - datetime.timedelta(days=30)
        previous_period_start = today - datetime.timedelta(days=60)
        previous_period_end = today - datetime.timedelta(days=30)

        # Get current period data
        current_chats = Chat.objects.filter(created_at__gte=current_period_start)
        total_current = current_chats.count()
        pending_current = current_chats.filter(status="pending").count()
        in_progress_current = current_chats.filter(status="in_progress").count()
        resolved_current = current_chats.filter(status="resolved").count()
        high_priority_current = current_chats.filter(
            priority__in=["high", "urgent"]
        ).count()

        # Get previous period data for trend calculation
        previous_chats = Chat.objects.filter(
            created_at__gte=previous_period_start, created_at__lt=previous_period_end
        )
        total_previous = previous_chats.count()
        pending_previous = previous_chats.filter(status="pending").count()
        resolved_previous = previous_chats.filter(status="resolved").count()

        # Calculate trends (percentage change)
        def calculate_trend(current, previous):
            if previous == 0:
                return 100.0 if current > 0 else 0.0
            return round(((current - previous) / previous) * 100, 1)

        total_trend = calculate_trend(total_current, total_previous)
        pending_trend = calculate_trend(pending_current, pending_previous)
        resolved_trend = calculate_trend(resolved_current, resolved_previous)

        # Calculate response time metrics
        response_time_stats = SupportMessage.get_response_time_stats(
            start_date=current_period_start, end_date=today
        )
        average_response_time = response_time_stats["average"]

        # Calculate previous period response time for trend
        prev_response_time_stats = SupportMessage.get_response_time_stats(
            start_date=previous_period_start, end_date=previous_period_end
        )
        prev_response_time = prev_response_time_stats["average"]
        response_time_trend = calculate_trend(average_response_time, prev_response_time)
        if average_response_time > prev_response_time:
            response_time_trend = -abs(
                response_time_trend
            )  # Negative trend for increased response time

        # Calculate resolution rate
        total_chats_all_time = Chat.objects.count()
        resolved_chats_all_time = Chat.objects.filter(status="resolved").count()
        resolution_rate = (
            round((resolved_chats_all_time / total_chats_all_time) * 100)
            if total_chats_all_time > 0
            else 0
        )

        # Calculate previous resolution rate for trend
        prev_total_chats = Chat.objects.filter(
            created_at__lt=previous_period_end
        ).count()
        prev_resolved_chats = Chat.objects.filter(
            status="resolved", created_at__lt=previous_period_end
        ).count()
        prev_resolution_rate = (
            (prev_resolved_chats / prev_total_chats) * 100
            if prev_total_chats > 0
            else 0
        )
        resolution_rate_trend = calculate_trend(resolution_rate, prev_resolution_rate)

        # Count active agents this week
        week_start = today - datetime.timedelta(days=today.weekday())
        active_agents = (
            SupportMessage.objects.filter(
                is_from_support=True, created_at__gte=week_start
            )
            .values("chat__user")
            .distinct()
            .count()
        )

        # Calculate average chats per day over the last 30 days
        thirty_days_ago = today - datetime.timedelta(days=30)
        daily_chats = Chat.objects.filter(created_at__gte=thirty_days_ago).count()
        average_chats_per_day = round(daily_chats / 30, 1)

        # Count total messages
        total_messages = SupportMessage.objects.count()

        # Mock customer satisfaction (would be calculated from actual satisfaction data)
        # This would typically come from surveys or ratings
        customer_satisfaction = 92  # Mock value
        satisfaction_trend = 2.1  # Mock trend

        data = {
            "total": total_current,
            "pending": pending_current,
            "inProgress": in_progress_current,
            "resolved": resolved_current,
            "highPriority": high_priority_current,
            "pendingTrend": pending_trend,
            "resolvedTrend": resolved_trend,
            "totalTrend": total_trend,
            "averageResponseTime": average_response_time,
            "responseTimeTrend": response_time_trend,
            "resolutionRate": resolution_rate,
            "resolutionRateTrend": resolution_rate_trend,
            "activeAgents": active_agents,
            "averageChatsPerDay": average_chats_per_day,
            "totalMessages": total_messages,
            "customerSatisfaction": customer_satisfaction,
            "satisfactionTrend": satisfaction_trend,
        }

        # Cache the results
        cache.set(cache_key, data, 60 * 5)  # Cache for 5 minutes

        return Response(data)

    except Exception as e:
        logger.error(
            f"Error getting enhanced support analytics: {str(e)}", exc_info=True
        )
        return Response(
            {"error": "Failed to retrieve enhanced support analytics"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
