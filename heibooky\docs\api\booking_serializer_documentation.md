# PropertyBookingListAPIView Serializer Documentation

## Overview

This document provides comprehensive documentation for the `PropertyBookingListAPIView` serializer output, specifically for Heibooky properties (where `is_manual == false` and `property_instance.is_domorent == false`).

## API Endpoint

```
GET /booking/property/
```

## Query Parameters

- `status`: Filter by booking status (`new`, `modified`, `request`, `cancelled`, `completed`)
- `type`: Filter by booking type (`manual`, `ota`)
- `property`: Filter by specific property ID

## Response Structure

### Reservation Object Fields

#### Basic Information
```json
{
  "id": "RES123456",
  "guest_name": "<PERSON>",
  "checkin_date": "2024-01-15",
  "checkout_date": "2024-01-18",
  "booked_at": "2024-01-10T14:30:00Z",
  "modified_at": "2024-01-10T14:30:00Z",
  "number_of_guests": 2,
  "number_of_adults": 2,
  "number_of_children": 0,
  "number_of_infants": 0
}
```

#### Financial Information - Heibooky Properties
```json
{
  "calculation_type": "Heibooky Property",
  "net_price": "1000.00",
  "total_price": "1000.00",
  "base_price": "1000.00",
  "client_price": "1000.00",
  
  "ota_commission": "150.00",
  "heibooky_commission": "80.00",
  "payment_fee": "35.00",
  "payment_charge": "35.00",
  
  "activation_fee": "150.00",
  "activation_fee_applied": "150.00",
  "activation_fee_remaining_before": "150.00",
  "activation_fee_remaining_after": "0.00",
  
  "vat_22": "50.60",
  "iva_amount": "50.60",
  "substitute_tax_21": "210.00",
  "owner_tax": "210.00",
  
  "net_total_for_owner": "283.70",
  "total_owner_payout": "283.70"
}
```

#### Payment Status Information

#### Enhanced Payment Details Object
```json
{
  "payment_details": {
    "payment_type": "Channel Collect",
    "calculation_type": "Heibooky Property",
    "client_price": 1000.00,
    "base_price": 1000.00,
    "ota_commission": 150.00,
    "heibooky_commission": 80.00,
    "payment_fee": 35.00,
    "payment_charge": 35.00,
    "activation_fee": 150.00,
    "vat_22": 50.60,
    "iva_amount": 50.60,
    "substitute_tax_21": 210.00,
    "owner_tax": 210.00,
    "net_total_for_owner": 283.70,
    "total_owner_payout": 283.70,
    "activation_fee_applied": 150.00,
    "activation_fee_remaining_before": 150.00,
    "activation_fee_remaining_after": 0.00
  }
}
```

## Field Mappings for Frontend Display

### Heibooky Property Pricing Structure

Based on the Heibooky Financial Summary, here's how each component should be displayed:

| Display Label | API Field | Calculation | Example Value |
|---------------|-----------|-------------|---------------|
| Client Price | `client_price` | Amount customer pays | €1,000.00 |
| OTA Commission (15%) | `ota_commission` | 15% of client price | €150.00 |
| Heibooky Commission (8%) | `heibooky_commission` | 8% of client price | €80.00 |
| Payment Fee (3.5%) | `payment_fee` | 3.5% of client price | €35.00 |
| Activation Fee | `activation_fee` | Configurable (default €150) | €150.00 |
| VAT (22%) | `vat_22` | 22% of (OTA + Heibooky commissions) | €50.60 |
| Substitute Tax (21%) | `substitute_tax_21` | 21% of client price | €210.00 |
| **Total Owner Payout** | `total_owner_payout` | Client price - all deductions | €283.70 |

### Payment Status Display

| Display Label                       | API Field                 | Calculation                                     | Example Value |
|-------------------------------------|---------------------------|-------------------------------------------------|---------------|
| Client Price                        | `client_price`            | Amount customer pays                            | €1,000.00     |
| OTA Commission (15%)                | `ota_commission`          | 15% of client price                             | €150.00       |
| Heibooky Commission (8%)            | `heibooky_commission`     | 8% of client price                              | €80.00        |
| Payment Fee (3.5%)                  | `payment_fee`             | 3.5% of client price                            | €35.00        |
| Activation Fee (applied)            | `activation_fee_applied`  | Portion deducted on this booking                | €150.00       |
| VAT (22%)                           | `vat_22`                  | 22% of (OTA + Heibooky commissions)             | €50.60        |
| Substitute Tax (21%)                | `substitute_tax_21`       | 21% of client price                             | €210.00       |
| **Total Owner Payout**              | `total_owner_payout`      | Client price - all deductions                   | €283.70       |
- `days_until_payment`: Days remaining until payment
- `is_current_cycle`: Whether it's in the current payment cycle

## Example Complete Response

```json
{
  "results": [
    {
      "id": "RES123456",
      "guest_name": "John Doe",
      "checkin_date": "2024-01-15",
      "checkout_date": "2024-01-18",
      "calculation_type": "Heibooky Property",
      "client_price": "1000.00",
      "ota_commission": "150.00",
      "heibooky_commission": "80.00",
      "payment_fee": "35.00",
      "activation_fee": "150.00",
      "vat_22": "50.60",
      "substitute_tax_21": "210.00",
      "total_owner_payout": "283.70",
      "payment_status": "payment_in_progress",
      "payment_status_display": "Payment in progress",
      "next_payment_info": {
        "payment_date": "2024-01-15",
        "payment_cycle_start": "2024-01-01",
        "payment_cycle_end": "2024-01-15",
        "days_until_payment": 3,
        "is_current_cycle": true
      },
      "payment_details": {
        "calculation_type": "Heibooky Property",
        "client_price": 1000.00,
        "ota_commission": 150.00,
        "heibooky_commission": 80.00,
        "payment_fee": 35.00,
        "activation_fee": 150.00,
        "vat_22": 50.60,
        "substitute_tax_21": 210.00,
        "total_owner_payout": 283.70,
        "activation_fee_applied": 150.00,
        "activation_fee_remaining_before": 150.00,
        "activation_fee_remaining_after": 0.00
      }
    }
  ],
  "count": 1,
  "next": null,
  "previous": null
}
```

## Activation Fee System

### How Activation Fees Work

1. **One-time fee**: €150.00 (configurable) applied to new users' first property
2. **Gradual recovery**: Deducted from booking revenue without causing negative payouts
3. **Tracking fields**:
   - `activation_fee_applied`: Amount deducted from this specific reservation
   - `activation_fee_remaining_before`: Remaining fee before this reservation
   - `activation_fee_remaining_after`: Remaining fee after this reservation

### Frontend Implementation Notes

1. **Display Logic**: Show activation fee only when `activation_fee_applied > 0`
2. **Progress Tracking**: Use remaining amounts to show recovery progress
3. **Status Indicators**: Different styling for payment_in_progress vs future_payment
4. **Responsive Design**: Consider mobile-friendly layouts for detailed breakdowns

## Error Handling

All financial calculations include fallback values:
- Missing or null values default to `0.00`
- Invalid dates default to `future_payment` status
- Calculation errors return empty objects for complex fields

## Security Considerations

- Financial information is hidden for cleaning staff users
- All monetary values are returned as strings to prevent precision loss
- Sensitive fields are filtered based on user permissions
