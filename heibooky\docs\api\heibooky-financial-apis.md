# Heibooky Booking Financial APIs Documentation

## Overview

This documentation provides comprehensive API specifications for the Heibooky booking financial system, including pricing calculations, payment status tracking, and financial reporting endpoints.

## Authentication

All endpoints require authentication. Include the authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Base URL
```
/booking/financial/
```

---

## Core Financial APIs

### 1. Pending Balance API

**Endpoint:** `GET /booking/financial/pending-balance/`

**Description:** Calculate pending balance from reservations completed in the last 15 days.

**Query Parameters:**
- `property_ids` (optional): Comma-separated list of property IDs to filter by

**Response:**
```json
{
    "pending_balance": "1250.75",
    "currency": "EUR",
    "calculation_period": "15 days",
    "last_updated": "2024-01-15T10:30:00Z"
}
```

### 2. Total Earnings API

**Endpoint:** `GET /booking/financial/total-earnings/`

**Description:** Calculate lifetime earnings from the platform.

**Query Parameters:**
- `property_ids` (optional): Comma-separated list of property IDs to filter by

**Response:**
```json
{
    "total_earnings": "15750.25",
    "currency": "EUR",
    "calculation_type": "lifetime",
    "last_updated": "2024-01-15T10:30:00Z",
    "total_reservations": 125
}
```

### 3. Comprehensive Financial Summary API

**Endpoint:** `GET /booking/financial/summary/`

**Description:** Get comprehensive financial summary including all metrics.

**Query Parameters:**
- `property_ids` (optional): Comma-separated list of property IDs to filter by

**Response:**
```json
{
    "pending_balance": "1250.75",
    "total_earnings": "15750.25",
    "commission_breakdown": {
        "total_ota_commission": "2300.50",
        "total_heibooky_commission": "1200.00",
        "total_payment_fees": "450.25",
        "total_vat": "805.30",
        "total_taxes": "3300.00",
        "total_activation_fees": "450.00",
        "total_cleaning_costs": "800.00"
    },
    "next_payment_info": {
        "next_payment_date": "2024-01-15",
        "days_until_payment": "3",
        "current_cycle": "1st-15th January 2024"
    },
    "currency": "EUR",
    "last_updated": "2024-01-12T10:30:00Z"
}
```

---

## Payment Status and Cycle APIs

### 4. Payment Status API

**Endpoint:** `GET /booking/financial/payment-status/`

**Description:** Get payment status information for reservations.

**Query Parameters:**
- `status` (optional): Filter by payment status ('payment_in_progress' or 'future_payment')
- `property_ids` (optional): Comma-separated list of property IDs to filter by
- `limit` (optional): Limit for number of reservations returned (default: 50)

**Response:**
```json
{
    "payment_in_progress": {
        "count": 5,
        "total_amount": "2500.00",
        "reservations": [
            {
                "id": "RES123456",
                "guest_name": "John Doe",
                "checkout_date": "2024-01-12",
                "net_amount": "450.75",
                "payment_status": "payment_in_progress"
            }
        ]
    },
    "future_payment": {
        "count": 12,
        "total_amount": "5200.00",
        "reservations": [...]
    }
}
```

### 5. Payment Cycle Information API

**Endpoint:** `GET /booking/financial/payment-cycle-info/`

**Description:** Get current payment cycle information.

**Response:**
```json
{
    "current_cycle": {
        "start_date": "2024-01-01",
        "end_date": "2024-01-15",
        "payment_date": "2024-01-15",
        "description": "1st-15th January 2024"
    },
    "next_cycle": {
        "start_date": "2024-01-16",
        "end_date": "2024-01-31",
        "payment_date": "2024-01-31",
        "description": "16th-31st January 2024"
    },
    "days_until_next_payment": 3
}
```

### 6. Individual Reservation Payment Info API

**Endpoint:** `GET /booking/financial/reservation/{reservation_id}/payment-info/`

**Description:** Get detailed payment information for a specific reservation.

**Response:**
```json
{
    "reservation_id": "RES123456",
    "payment_status": "payment_in_progress",
    "payment_status_display": "Payment in progress",
    "payment_date": "2024-01-15",
    "payment_cycle_start": "2024-01-01",
    "payment_cycle_end": "2024-01-15",
    "days_until_payment": 3,
    "is_current_cycle": true,
    "net_amount": "450.75"
}
```

---

## Commission and Fee APIs

### 7. Commission Breakdown API

**Endpoint:** `GET /booking/financial/commission-breakdown/`

**Description:** Get detailed commission breakdown for financial reporting.

**Query Parameters:**
- `property_ids` (optional): Comma-separated list of property IDs to filter by
- `period` (optional): Period filter ('last_30_days', 'last_90_days', 'ytd', 'all_time')

**Response:**
```json
{
    "period": "last_30_days",
    "breakdown": {
        "total_ota_commission": "1200.00",
        "total_heibooky_commission": "640.00",
        "total_domorent_commission": "800.00",
        "total_payment_fees": "400.00",
        "total_vat": "607.20",
        "total_taxes": "2940.00",
        "total_activation_fees": "300.00",
        "total_cleaning_costs": "400.00"
    },
    "currency": "EUR",
    "last_updated": "2024-01-12T10:30:00Z"
}
```

### 8. Activation Fee Status API

**Endpoint:** `GET /booking/financial/activation-fee-status/`

**Description:** Get activation fee status for properties.

**Query Parameters:**
- `property_id` (required): Property ID to check activation fee status for

**Response:**
```json
{
    "activation_fee_active": true,
    "original_fee_amount": "150.00",
    "remaining_fee_amount": "75.00",
    "total_recovered_amount": "75.00",
    "recovery_percentage": 50.0,
    "is_fully_recovered": false,
    "recovery_started_at": "2024-01-01T10:00:00Z",
    "fully_recovered_at": null,
    "message": "Activation fee recovery in progress"
}
```

---

## Reservation Serializer API Response

### PropertyBookingListAPIView Response

For Heibooky properties (`is_manual == false` and `property_instance.is_domorent == false`), the reservation serializer returns:

```json
{
    "id": "RES123456",
    "checkin_date": "2024-01-10",
    "checkout_date": "2024-01-12",
    "guest_name": "John Doe",
    "total_price": "1000.00",
    "net_price": "1000.00",
    "payment_type": "Channel Collect",
    "payment_details": {
        "payment_type": "Channel Collect",
        "calculation_type": "Heibooky Property",
        
        // Main pricing structure (exactly as shown in Heibooky Financial Summary)
        "client_price": 1000.00,
        "base_price": 1000.00,
        
        // Commission breakdown (15% + 8% + 3.5%)
        "ota_commission": 150.00,          // 15% of Client Price
        "heibooky_commission": 80.00,      // 8% of Client Price  
        "payment_fee_3_5": 35.00,          // 3.5% of Client Price
        "payment_charge": 35.00,           // For compatibility
        
        // Activation fee (gradual recovery)
        "activation_fee": 15.00,
        
        // Tax calculations
        "vat_22": 50.60,                   // 22% of (OTA + Heibooky Commission)
        "iva_amount": 50.60,               // For compatibility
        "substitute_tax_21": 210.00,       // 21% of Client Price
        "owner_tax": 210.00,               // For compatibility
        
        // Final owner amount (TOTAL OWNER as shown in image)
        "total_owner": 474.40,             // Final amount owner receives
        "net_total_for_owner": 474.40,     // For compatibility
        "total_owner_payout": 474.40,      // For compatibility
        
        // Activation fee recovery tracking
        "activation_fee_tracking": {
            "activation_fee_applied": 15.00,
            "activation_fee_remaining_before": 150.00,
            "activation_fee_remaining_after": 135.00,
            "is_activation_fee_recovery": true
        },
        
        // Detailed breakdown for auditing
        "breakdown": {
            "client_price": 1000.00,
            "ota_commission_15_percent": 150.00,
            "heibooky_commission_8_percent": 80.00,
            "payment_fee_3_5_percent": 35.00,
            "vat_22_percent_on_commissions": 50.60,
            "substitute_tax_21_percent": 210.00,
            "total_deductions": 525.60,
            "activation_fee_deducted": 15.00,
            "final_owner_payout": 474.40
        },
        
        // Summary totals for frontend calculations
        "summary": {
            "total_commissions": 265.00,      // OTA + Heibooky + Payment Fee
            "total_taxes": 260.60,            // VAT + Substitute Tax
            "total_fees": 15.00,              // Activation Fee
            "grand_total_deductions": 540.60  // All deductions
        }
    },
    "payment_status": "payment_in_progress",
    "payment_status_display": "Payment in progress",
    "next_payment_info": {
        "next_payment_date": "2024-01-15",
        "days_until_payment": "3",
        "current_cycle": "1st-15th January 2024"
    }
}
```

---

## Payment Cycles Explanation

### Payment Schedule
- **1st-15th of month**: Payment processed on 15th
- **16th-30th/31st of month**: Payment processed on last day of month

### Payment Status Logic
- **"payment_in_progress"**: Checkout date falls within current 15-day payment cycle
- **"future_payment"**: Checkout date falls outside current payment cycle

---

## Pricing Calculation Structure

### Heibooky Properties (is_domorent = false)

Based on the exact Heibooky Financial Summary structure:

```
Client Price: €1,000.00 (amount customer pays)
├── OTA Commission: €150.00 (15% of Client Price)
├── Heibooky Commission: €80.00 (8% of Client Price)  
├── Payment Fee (3.5%): €35.00 (3.5% of Client Price)
├── Activation Fee: €15.00 (configurable, gradual recovery)
├── VAT (22%): €50.60 (22% of OTA + Heibooky Commission)
└── Substitute Tax (21%): €210.00 (21% of Client Price)

TOTAL OWNER: €474.40
```

### Key Calculation Points

1. **Commission Base**: All percentages calculated from client price
2. **VAT Calculation**: Applied only to OTA + Heibooky commissions
3. **Substitute Tax**: Applied to full client price
4. **Activation Fee**: Gradually recovered, never makes owner payout negative
5. **Owner Protection**: Final payout never goes below €0

---

## Error Handling

All APIs return consistent error responses:

```json
{
    "error": "Description of the error",
    "code": "ERROR_CODE",
    "details": {
        "field": "Additional error details"
    }
}
```

### Common HTTP Status Codes
- `200 OK`: Successful request
- `400 Bad Request`: Invalid parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

---

## Rate Limiting

Financial APIs are rate-limited to prevent abuse:
- **60 requests per minute** per user
- **1000 requests per hour** per user

---

## Data Precision

All monetary values:
- Are returned as strings to preserve precision
- Use 2 decimal places
- Are in EUR currency
- Follow exact calculation formulas from Heibooky Financial Summary

---

## Frontend Integration Notes

### Key Points for Frontend Developers

1. **Always use the `payment_details` object** for displaying pricing breakdowns
2. **Check `calculation_type`** to determine display structure (Heibooky vs Domorent)
3. **Use `total_owner` field** for final owner payout (matches image exactly)
4. **Display activation fee recovery status** using `activation_fee_tracking`
5. **Show payment status** using `payment_status_display`
6. **All monetary values are strings** - convert to numbers for calculations
7. **Breakdown object provides audit trail** for detailed calculations

### Recommended Display Structure

```javascript
// Frontend display example
const reservation = response.data;
const details = reservation.payment_details;

if (details.calculation_type === "Heibooky Property") {
    // Display Heibooky pricing structure
    console.log("Client Price:", details.client_price);
    console.log("OTA Commission (15%):", details.ota_commission);
    console.log("Heibooky Commission (8%):", details.heibooky_commission);
    console.log("Payment Fee (3.5%):", details.payment_fee_3_5);
    console.log("Activation Fee:", details.activation_fee);
    console.log("VAT (22%):", details.vat_22);
    console.log("Substitute Tax (21%):", details.substitute_tax_21);
    console.log("TOTAL OWNER:", details.total_owner);
}
```

### Real-time Updates

Financial data should be refreshed:
- **Every 15 minutes** for pending balance
- **Daily** for total earnings
- **On page load** for payment status
- **After booking updates** for individual reservation data

---

This documentation ensures the frontend team can correctly implement and display the Heibooky financial calculations with full accuracy and proper user experience.
