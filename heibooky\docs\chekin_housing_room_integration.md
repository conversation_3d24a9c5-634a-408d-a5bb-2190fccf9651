# Chekin Housing and Room Integration

This document describes the automatic housing and room management integration with Chekin for Domorent properties.

## Overview

The Chekin housing and room integration automatically manages housing and room entities in Chekin when Domorent properties and rooms are onboarded, updated, or deleted in the BackTrack system. This ensures that the Chekin system stays synchronized with property and room data for automatic check-in functionality.

## Features

### Automatic Housing Management
- **Housing Creation**: When a Domorent property is onboarded, a corresponding housing is automatically created in Chekin
- **Housing Updates**: When property details are updated, the corresponding Chekin housing is updated
- **Housing Deletion**: When a property is deleted, the corresponding Chekin housing is removed

### Automatic Room Management
- **Room Creation**: When a room in a Domorent property is onboarded, a corresponding room is created in Chekin
- **Room Updates**: When room details are updated, the corresponding Chekin room is updated
- **Room Deletion**: When a room is deleted, the corresponding Chekin room is removed

### Data Mapping
- **Property Types**: Single-unit properties map to "HOU" (House), multi-unit properties map to "HOT" (Hotel)
- **Country Codes**: Country names are automatically mapped to ISO alpha-2 codes
- **External IDs**: BackTrack property and room IDs are used as external IDs in Chekin

## Architecture

### Components

1. **Models**
   - `Property.chekin_housing_id`: Stores the Chekin housing ID
   - `Room.chekin_room_id`: Stores the Chekin room ID
   - `ChekinAPILog`: Enhanced to log housing and room operations

2. **Services**
   - `ChekinAPIService`: Extended with housing and room management methods
   - `ChekinDataMapper`: Enhanced with property and room mapping methods

3. **Tasks**
   - `create_chekin_housing_task`: Creates housing in Chekin
   - `update_chekin_housing_task`: Updates housing in Chekin
   - `delete_chekin_housing_task`: Deletes housing from Chekin
   - `create_chekin_room_task`: Creates room in Chekin
   - `update_chekin_room_task`: Updates room in Chekin
   - `delete_chekin_room_task`: Deletes room from Chekin

4. **Signals**
   - Property signals: Trigger housing operations on property changes
   - Room signals: Trigger room operations on room changes

### Data Flow

```
Property/Room Change → Signal Handler → Validation → Celery Task → Chekin API → Log Result
```

## Configuration

### Prerequisites

1. **Property Configuration**
   - Property must be marked as Domorent (`is_domorent=True`)
   - Property must be onboarded (`is_onboarded=True`)
   - Property must be active (`is_active=True`)
   - Property must have valid location data

2. **Online Check-in Configuration**
   - `ChekinConfig` record must exist
   - Chekin must be enabled (`chekin_enabled=True`)
   - Valid Chekin API key must be configured
   - Environment must be set (sandbox/production)

### Room Requirements

1. **Room Configuration**
   - Room must belong to a Domorent property
   - Room must be onboarded (`is_onboarded=True`)
   - Room must be active (`is_active=True`)
   - Property must have a Chekin housing ID

## API Integration

### Housing Operations

#### Create Housing
```python
# Triggered when property is onboarded
housing_data = {
    "name": "Property Name",
    "external_id": "property-uuid",
    "commercial_name": "Property Name",
    "type": "HOU",  # or "HOT" for multi-unit
    "is_deactivated": False,
    "location": {
        "country": "IT",
        "city": "Rome"
    }
}
```

#### Update Housing
```python
# Triggered when property details change
# Same structure as create, sent to existing housing
```

#### Delete Housing
```python
# Triggered when property is deleted
# DELETE /housings/{housing_id}
```

### Room Operations

#### Create Room
```python
# Triggered when room is onboarded
room_data = {
    "housing_id": "chekin-housing-id",
    "name": "Single - Property Name"
    # Optionally include "external_id": "room-uuid" if your integration requires it
}
#### Update Room
```python
# Triggered when room details change
# Same structure as create, sent to existing room
```

#### Delete Room
```python
# Triggered when room is deleted
# DELETE /rooms/{room_id}
```

## Error Handling

### Retry Logic
- All tasks implement exponential backoff retry (3 attempts)
- Retry delays: 5 minutes, 10 minutes, 20 minutes
- Authentication errors are not retried

### Error Logging
- All API calls are logged in `ChekinAPILog`
- Failed operations include error messages
- Integration actions are logged for audit trail

### Validation
- Properties and rooms are validated before API calls
- Missing required fields result in task skipping
- Invalid configurations are logged as warnings

## Monitoring

### API Logs
Query `ChekinAPILog` for housing and room operations:

```python
# Housing operations
housing_logs = ChekinAPILog.objects.filter(
    action_type__in=[
        ChekinAPILog.ActionType.HOUSING_CREATE,
        ChekinAPILog.ActionType.HOUSING_UPDATE,
        ChekinAPILog.ActionType.HOUSING_DELETE,
    ]
)

# Room operations
room_logs = ChekinAPILog.objects.filter(
    action_type__in=[
        ChekinAPILog.ActionType.ROOM_CREATE,
        ChekinAPILog.ActionType.ROOM_UPDATE,
        ChekinAPILog.ActionType.ROOM_DELETE,
    ]
)
```

### Health Checks
- Monitor task success rates
- Check for properties without Chekin housing IDs
- Verify room synchronization status

## Management Commands

### Sync Existing Properties

```bash
# Dry run to see what would be synced
python manage.py sync_chekin_housing --dry-run

# Sync all eligible properties
python manage.py sync_chekin_housing

# Sync specific property
python manage.py sync_chekin_housing --property-id=<uuid>

# Force sync even if housing ID exists
python manage.py sync_chekin_housing --force

# Include room synchronization
python manage.py sync_chekin_housing --include-rooms
```

## Testing

### Unit Tests
Run the housing and room integration tests:

```bash
python manage.py test tests.test_chekin_housing_room_integration
```

### Manual Testing

1. **Create Domorent Property**
   - Create property with `is_domorent=True`
   - Configure online check-in with Chekin
   - Set `is_onboarded=True`
   - Verify housing is created in Chekin

2. **Create Room**
   - Add room to Domorent property
   - Set `is_onboarded=True`
   - Verify room is created in Chekin

3. **Update Property/Room**
   - Modify property or room details
   - Verify updates are sent to Chekin

4. **Delete Property/Room**
   - Delete property or room
   - Verify deletion is sent to Chekin

## Troubleshooting

### Common Issues

1. **Housing Not Created**
   - Check if property is Domorent and onboarded
   - Verify online check-in configuration
   - Check Chekin API credentials
   - Review task logs for errors

2. **Room Not Created**
   - Ensure property has Chekin housing ID
   - Check if room is onboarded and active
   - Verify property is Domorent

3. **API Errors**
   - Check Chekin API status
   - Verify API credentials
   - Review error messages in logs
   - Check network connectivity

### Debug Commands

```bash
# Check property configuration
python manage.py shell
>>> from apps.stay.models import Property
>>> prop = Property.objects.get(id='<uuid>')
>>> print(f"Domorent: {prop.is_domorent}")
>>> print(f"Onboarded: {prop.is_onboarded}")
>>> print(f"Housing ID: {prop.chekin_housing_id}")

# Check online check-in config
>>> from apps.integrations.models import ChekinConfig
>>> config = ChekinConfig.objects.get(property=prop)
>>> print(f"Chekin enabled: {config.chekin_enabled}")
>>> print(f"API key configured: {bool(config.chekin_api_key)}")
```

## Security Considerations

1. **API Keys**: Chekin API keys are stored securely in the database
2. **Environment Separation**: Sandbox and production environments are clearly separated
3. **Data Validation**: All data is validated before sending to Chekin
4. **Error Handling**: Sensitive information is not exposed in error messages
5. **Audit Trail**: All operations are logged for compliance and debugging
