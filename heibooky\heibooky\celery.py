import logging
import os

from celery import Celery
from celery.signals import celeryd_after_setup
from django.conf import settings
from kombu.utils.url import maybe_sanitize_url

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "heibooky.settings")

app = Celery("heibooky")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.conf.beat_scheduler = "django_celery_beat.schedulers:DatabaseScheduler"

# SSL and Connection Settings
app.conf.update(
    broker_url=settings.CELERY_BROKER_URL,
    result_backend=settings.CELERY_RESULT_BACKEND,
    broker_connection_retry=True,
    broker_connection_max_retries=10,
    broker_connection_timeout=30,
    broker_transport_options={
        "visibility_timeout": 3600,
        "max_retries": 3,
        "interval_start": 0,
        "interval_step": 0.2,
        "interval_max": 0.5,
    },
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=50,
    worker_max_memory_per_child=50000,  # 50MB
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
)


# Log Redis connection details
@celeryd_after_setup.connect
def log_redis_connection(sender, instance, **kwargs):
    logger = logging.getLogger(__name__)
    logger.info(f"Connecting to Redis at: {maybe_sanitize_url(settings.REDIS_URL)}")


# Include tasks from all registered Django apps
app.autodiscover_tasks()

# Important: Explicitly register task modules
app.conf.imports = [
    "apps.booking.tasks",
    "apps.integrations.tasks.payments",
    "apps.integrations.tasks.chekin",
    "apps.integrations.tasks.chekin_housing",
    "apps.integrations.tasks.chekin_room",
    "apps.pricing.tasks",
    "apps.integrations.tasks.chekin",
    "apps.stay.tasks",
    "apps.support.tasks",
    "apps.users.tasks",
]

# Configure periodic tasks
app.conf.beat_schedule = {
    "sync-guest-data-daily": {
        "task": "apps.integrations.tasks.chekin.sync_guest_data",
        "schedule": 86400.0,  # Run once a day (in seconds)
        "options": {"expires": 3600},  # Task expires after 1 hour
    },
}


@app.task(bind=True)
def debug_task(self):
    print(f"Request: {self.request!r}")
