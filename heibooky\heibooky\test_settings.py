"""
Test-specific Django settings for Heibooky.

This module provides a complete test configuration that doesn't depend on
external environment variables, making it suitable for CI/CD environments.
"""

import os

# Set ALL required environment variables with dummy values BEFORE importing main settings
# This ensures that main settings.py won't fail when trying to read missing env vars
test_env_vars = {
    "SECRET_KEY": "test-secret-key-for-testing",
    "DEBUG": "False",
    "ALLOWED_HOSTS": "localhost,127.0.0.1,testserver",
    # Database (will be overridden later)
    "DB_NAME": "test_heibooky",
    "DB_USER": "postgres",
    "DB_PASSWORD": "postgres",
    "DB_HOST": "localhost",
    "DB_PORT": "5432",
    # Redis
    "REDIS_HOST": "localhost",
    "REDIS_PORT": "6379",
    "REDIS_DB": "0",
    # Email
    "MAILGUN_API_KEY": "test-mailgun-key",
    "MAILGUN_DOMAIN": "test.mailgun.org",
    "DEFAULT_FROM_EMAIL": "<EMAIL>",
    # AWS/Storage
    "AWS_ACCESS_KEY_ID": "test-access-key",
    "AWS_SECRET_ACCESS_KEY": "test-secret-key",
    "AWS_STORAGE_BUCKET_NAME": "test-bucket",
    "AWS_S3_ENDPOINT_URL": "https://test.s3.amazonaws.com",
    "AWS_CDN_URL": "https://test.cloudfront.net",
    # Social auth
    "GOOGLE_CLIENT_ID": "test-google-client-id",
    "GOOGLE_CLIENT_SECRET": "test-google-client-secret",
    # APIs
    "DIGITHERA_BASE_URL": "https://test.digithera.com",
    "DIGITHERA_IDENTIFIER": "test-identifier",
    "DIGITHERA_API_TOKEN": "test-token",
    "STRIPE_SECRET_KEY": "sk_test_dummy_key",
    "IPINFO_TOKEN": "test-ipinfo-token",
    "SU_API_BASE_URL": "https://test.su-api.com",
    "SU_API_APP_ID": "test-app-id",
    "SU_API_KEY": "test-api-key",
    # Company
    "COMPANY_VAT_CODE": "*************",
    "COMPANY_NAME": "Test Company",
    # URLs
    "FRONTEND_URL": "http://localhost:3000",
    "ADMIN_URL": "http://localhost:8000/admin",
    "SUPPORT_URL": "http://localhost:8000/support",
    "LOGO_URL": "http://localhost:8000/static/logo.png",
    # Optional
    "ADMIN_EMAIL": "<EMAIL>",
    "ENABLE_MEDIA_FILE_DELETION": "False",
}

# Set environment variables BEFORE importing main settings
for key, value in test_env_vars.items():
    os.environ.setdefault(key, value)

# Override DEBUG to ensure logs directory is created in the right place
os.environ["DEBUG"] = (
    "True"  # This will make LOGS_DIR use BASE_DIR / "logs" instead of /var/log/heibooky
)

# Now safely import from main settings
from .settings import *

# Override settings for testing
DEBUG = False  # Set back to False for testing, but LOGS_DIR is already set correctly
TESTING = True

# Override logs directory to use temp directory for tests
LOGS_DIR = "/tmp/test_logs"
os.makedirs(LOGS_DIR, exist_ok=True)

# Database configuration for tests
if os.environ.get("DATABASE_URL"):
    DATABASES = {"default": env.db()}
else:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": "test_heibooky",
            "USER": "postgres",
            "PASSWORD": "postgres",
            "HOST": "localhost",
            "PORT": "5432",
        }
    }

# Use in-memory cache for tests
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
    }
}

# Use in-memory channel layer for tests
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer",
    }
}


# Disable migrations for faster tests
class DisableMigrations:
    def __contains__(self, item):
        return True

    def __getitem__(self, item):
        return None


MIGRATION_MODULES = DisableMigrations()

# Use console email backend for tests
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# Disable Celery for tests
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# Simple password hashers for faster tests
PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.MD5PasswordHasher",
]

# Disable logging during tests
LOGGING_CONFIG = None
import logging

logging.disable(logging.CRITICAL)

# Override logging configuration for tests (no file handlers)
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "null": {
            "class": "logging.NullHandler",
        },
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
        },
    },
    "root": {
        "handlers": ["null"],
        "level": "WARNING",
    },
    "loggers": {
        "django": {
            "handlers": ["null"],
            "level": "WARNING",
            "propagate": False,
        },
    },
}

# Use simple file storage for tests
STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.InMemoryStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
    "document_storage": {
        "BACKEND": "django.core.files.storage.InMemoryStorage",
    },
    "photo_storage": {
        "BACKEND": "django.core.files.storage.InMemoryStorage",
    },
    "invoice_storage": {
        "BACKEND": "django.core.files.storage.InMemoryStorage",
    },
}

# Override media settings
MEDIA_ROOT = "/tmp/test_media"
MEDIA_URL = "/media/"

# Disable throttling for tests and simplify REST framework config
REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": ["rest_framework.permissions.AllowAny"],
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ),
    "DEFAULT_THROTTLE_CLASSES": [],
    "DEFAULT_THROTTLE_RATES": {},
    "TEST_REQUEST_DEFAULT_FORMAT": "json",
}

# Set allowed hosts
ALLOWED_HOSTS = ["localhost", "127.0.0.1", "testserver"]

# Remove logging middleware for tests
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "allauth.account.middleware.AccountMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django.middleware.locale.LocaleMiddleware",
]
