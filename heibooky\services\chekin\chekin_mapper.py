import logging
from datetime import date, datetime
from typing import Any, Dict, Optional

from apps.booking.models import Booking, Customer, Reservation
from apps.stay.models import Property, Room

logger = logging.getLogger(__name__)


class ChekinDataMapper:
    """
    Utility class for mapping internal booking data to Chekin API schema.
    """

    @staticmethod
    def map_booking_to_chekin_reservation(
        booking: Booking, chekin_housing_id: str, source_name: str = "Heibooky"
    ) -> Dict[str, Any]:
        """
        Map a Booking instance to Chekin reservation format.

        Args:
            booking: The Booking instance to map
            chekin_housing_id: The Chekin housing ID for the property
            source_name: The source name to use (default: "Heibooky")

        Returns:
            Dict containing the mapped reservation data for Chekin API
        """
        try:
            reservation = booking.reservation_data
            customer = booking.customer

            # Calculate total number of guests
            def _safe_guest_count(res):
                return (
                    (getattr(res, "number_of_adults", 0) or 0)
                    + (getattr(res, "number_of_children", 0) or 0)
                    + (getattr(res, "number_of_infants", 0) or 0)
                )

            total_guests = _safe_guest_count(reservation)

            # Ensure we have at least 1 guest
            if total_guests == 0:
                total_guests = reservation.number_of_guests or 1

            # Build the reservation data
            chekin_data = {
                "housing": chekin_housing_id,
                "check_in_date": booking.checkin_date.strftime("%Y-%m-%d"),
                "check_out_date": booking.checkout_date.strftime("%Y-%m-%d"),
                "guest_group": {"known_number_of_guests": total_guests},
                "external_id": str(booking.id),
                "source_name": source_name,
            }

            # Add optional fields if available
            if reservation.guest_name:
                chekin_data["default_leader_full_name"] = reservation.guest_name
            elif customer:
                chekin_data["default_leader_full_name"] = customer.get_full_name()

            if customer and customer.email:
                chekin_data["default_invite_email"] = customer.email

            if customer and customer.telephone:
                chekin_data["default_phone_number"] = customer.telephone

            # Add booking reference if available
            if hasattr(reservation, "id") and reservation.id:
                chekin_data["booking_reference"] = str(reservation.id)

            # Set default email language (could be made configurable)
            chekin_data["default_email_language"] = "eng"

            logger.debug(f"Mapped booking {booking.id} to Chekin format: {chekin_data}")
            return chekin_data

        except Exception as e:
            logger.error(
                f"Error mapping booking {booking.id} to Chekin format: {str(e)}"
            )
            raise

    @staticmethod
    def map_reservation_to_chekin_reservation(
        reservation: Reservation,
        customer: Customer,
        property_checkin_date: date,
        property_checkout_date: date,
        chekin_housing_id: str,
        source_name: str = "Heibooky",
    ) -> Dict[str, Any]:
        """
        Map a Reservation instance directly to Chekin reservation format.
        This is useful when we have reservation data without a full booking.

        Args:
            reservation: The Reservation instance to map
            customer: The Customer instance
            property_checkin_date: Check-in date for the property
            property_checkout_date: Check-out date for the property
            chekin_housing_id: The Chekin housing ID for the property
            source_name: The source name to use (default: "Heibooky")

        Returns:
            Dict containing the mapped reservation data for Chekin API
        """
        try:
            # Calculate total number of guests
            total_guests = (
                reservation.number_of_adults
                + reservation.number_of_children
                + reservation.number_of_infants
            )

            # Ensure we have at least 1 guest
            if total_guests == 0:
                total_guests = reservation.number_of_guests or 1

            # Build the reservation data
            chekin_data = {
                "housing": chekin_housing_id,
                "check_in_date": property_checkin_date.strftime("%Y-%m-%d"),
                "check_out_date": property_checkout_date.strftime("%Y-%m-%d"),
                "guest_group": {"known_number_of_guests": total_guests},
                "external_id": str(reservation.id),
                "source_name": source_name,
            }

            # Add optional fields if available
            if reservation.guest_name:
                chekin_data["default_leader_full_name"] = reservation.guest_name
            elif customer:
                chekin_data["default_leader_full_name"] = customer.get_full_name()

            if customer and customer.email:
                chekin_data["default_invite_email"] = customer.email

            if customer and customer.telephone:
                chekin_data["default_phone_number"] = customer.telephone

            # Add booking reference
            chekin_data["booking_reference"] = str(reservation.id)

            # Set default email language (could be made configurable)
            chekin_data["default_email_language"] = "eng"

            logger.debug(
                f"Mapped reservation {reservation.id} to Chekin format: {chekin_data}"
            )
            return chekin_data

        except Exception as e:
            logger.error(
                f"Error mapping reservation {reservation.id} to Chekin format: {str(e)}"
            )
            raise

    @staticmethod
    def map_booking_update_to_chekin(
        booking: Booking, chekin_housing_id: str
    ) -> Dict[str, Any]:
        """
        Map a Booking update to Chekin reservation update format.
        Only includes fields that can be updated.

        Args:
            booking: The updated Booking instance
            chekin_housing_id: The Chekin housing ID for the property

        Returns:
            Dict containing the mapped update data for Chekin API
        """
        try:
            reservation = booking.reservation_data
            customer = booking.customer

            # Calculate total number of guests
            total_guests = (
                reservation.number_of_adults
                + reservation.number_of_children
                + reservation.number_of_infants
            )

            # Ensure we have at least 1 guest
            if total_guests == 0:
                total_guests = reservation.number_of_guests or 1

            # Build the update data (only updatable fields)
            update_data = {
                "housing": chekin_housing_id,
                "check_in_date": booking.checkin_date.strftime("%Y-%m-%d"),
                "check_out_date": booking.checkout_date.strftime("%Y-%m-%d"),
                "guest_group": {"known_number_of_guests": total_guests},
            }

            # Add optional updatable fields
            if reservation.guest_name:
                update_data["default_leader_full_name"] = reservation.guest_name
            elif customer:
                update_data["default_leader_full_name"] = customer.get_full_name()

            if customer and customer.telephone:
                update_data["default_phone_number"] = customer.telephone

            # Note: email and external_id typically cannot be updated

            logger.debug(
                f"Mapped booking {booking.id} update to Chekin format: {update_data}"
            )
            return update_data

        except Exception as e:
            logger.error(
                f"Error mapping booking {booking.id} update to Chekin format: {str(e)}"
            )
            raise

    @staticmethod
    def validate_required_fields(booking: Booking) -> bool:
        """
        Validate that a booking has all required fields for Chekin integration.

        Args:
            booking: The Booking instance to validate

        Returns:
            True if all required fields are present, False otherwise
        """
        try:
            # Check basic booking fields
            if not booking.checkin_date or not booking.checkout_date:
                logger.error(
                    f"Booking {booking.id} missing check-in or check-out dates"
                )
                return False

            # Check property has Chekin housing ID
            if (
                not hasattr(booking.property, "chekin_housing_id")
                or not booking.property.chekin_housing_id
            ):
                logger.error(
                    f"Property {booking.property.id} missing Chekin housing ID"
                )
                return False

            # Check reservation data
            if not booking.reservation_data:
                logger.error(f"Booking {booking.id} missing reservation data")
                return False

            reservation = booking.reservation_data

            # Check guest count
            total_guests = (
                getattr(reservation, "number_of_adults", 0)
                + getattr(reservation, "number_of_children", 0)
                + getattr(reservation, "number_of_infants", 0)
            )

            if total_guests == 0:
                total_guests = getattr(reservation, "number_of_guests", 0)

            if total_guests == 0:
                logger.error(f"Booking {booking.id} has no guests")
                return False

            logger.debug(f"Booking {booking.id} validation passed")
            return True

        except Exception as e:
            logger.error(f"Error validating booking {booking.id}: {str(e)}")
            return False

    @staticmethod
    def get_country_code(country_name: str) -> str:
        """
        Map country name to ISO alpha-2 country code.

        Args:
            country_name: Full country name

        Returns:
            ISO alpha-2 country code (defaults to 'IT' for Italy)
        """
        # Common country mappings (can be extended)
        country_mapping = {
            "italy": "IT",
            "italia": "IT",
            "spain": "ES",
            "españa": "ES",
            "france": "FR",
            "germany": "DE",
            "deutschland": "DE",
            "united kingdom": "GB",
            "uk": "GB",
            "portugal": "PT",
            "netherlands": "NL",
            "belgium": "BE",
            "austria": "AT",
            "switzerland": "CH",
            "greece": "GR",
            "croatia": "HR",
            "slovenia": "SI",
            "czech republic": "CZ",
            "poland": "PL",
            "hungary": "HU",
            "romania": "RO",
            "bulgaria": "BG",
            "slovakia": "SK",
            "lithuania": "LT",
            "latvia": "LV",
            "estonia": "EE",
            "finland": "FI",
            "sweden": "SE",
            "norway": "NO",
            "denmark": "DK",
            "ireland": "IE",
            "malta": "MT",
            "cyprus": "CY",
            "luxembourg": "LU",
        }

        # Normalize country name for lookup
        normalized_name = country_name.lower().strip()
        return country_mapping.get(normalized_name, "IT")  # Default to Italy

    @staticmethod
    def map_property_to_chekin_housing(property_instance: Property) -> Dict[str, Any]:
        """
        Map a Property instance to Chekin housing format.

        Args:
            property_instance: The Property instance to map

        Returns:
            Dict containing the mapped housing data for Chekin API
        """
        try:
            location = property_instance.location

            # Determine property type based on is_multi_unit
            property_type = "HOT" if property_instance.is_multi_unit else "HOU"

            # Build the housing data
            housing_data = {
                "name": property_instance.name,
                "external_id": str(property_instance.id),
                "commercial_name": property_instance.name,  # Use same as name for now
                "type": property_type,
                "is_deactivated": not property_instance.is_active,
                "location": {
                    "country": ChekinDataMapper.get_country_code(location.country),
                    "city": location.city,
                },
            }

            # Add rooms quantity for hotel type properties
            if property_type == "HOT":
                room_count = property_instance.rooms.filter(is_active=True).count()
                housing_data["rooms_quantity"] = max(room_count, 1)  # At least 1 room

            # Add optional fields if available
            if hasattr(property_instance, "ownership") and property_instance.ownership:
                # Use property ownership info if available
                pass  # Can be extended with VATIN/tax info

            logger.debug(
                f"Mapped property {property_instance.id} to Chekin housing format: {housing_data}"
            )
            return housing_data

        except Exception as e:
            logger.error(
                f"Error mapping property {property_instance.id} to Chekin housing format: {str(e)}"
            )
            raise

    @staticmethod
    def map_room_to_chekin_room(
        room_instance: Room, chekin_housing_id: str
    ) -> Dict[str, Any]:
        """
        Map a Room instance to Chekin room format.

        Args:
            room_instance: The Room instance to map
            chekin_housing_id: The Chekin housing ID for the property

        Returns:
            Dict containing the mapped room data for Chekin API
        """
        try:
            # Build the room data
            room_data = {
                "housing": chekin_housing_id,
                "external_id": str(room_instance.id),
                "number": f"Room-{room_instance.get_room_type_display()}",  # Use room type as number
                "external_name": f"{room_instance.get_room_type_display()} - {room_instance.property.name}",
            }

            logger.debug(
                f"Mapped room {room_instance.id} to Chekin room format: {room_data}"
            )
            return room_data

        except Exception as e:
            logger.error(
                f"Error mapping room {room_instance.id} to Chekin room format: {str(e)}"
            )
            raise

    @staticmethod
    def validate_property_for_chekin(property_instance: Property) -> bool:
        """
        Validate that a property has all required fields for Chekin housing creation.

        Args:
            property_instance: The Property instance to validate

        Returns:
            True if all required fields are present, False otherwise
        """
        try:
            # Check required property fields
            if not property_instance.name:
                logger.warning(f"Property {property_instance.id} missing name")
                return False

            # Check if we have location data
            if (
                not hasattr(property_instance, "location")
                or not property_instance.location
            ):
                logger.warning(f"Property {property_instance.id} missing location data")
                return False

            location = property_instance.location

            # Check required location fields
            if not location.city or not location.country:
                logger.warning(
                    f"Property {property_instance.id} missing city or country in location"
                )
                return False

            logger.debug(
                f"Property {property_instance.id} passed validation for Chekin housing"
            )
            return True

        except Exception as e:
            logger.error(
                f"Error validating property {property_instance.id} for Chekin: {str(e)}"
            )
            return False

    @staticmethod
    def validate_room_for_chekin(room_instance: Room) -> bool:
        """
        Validate that a room has all required fields for Chekin room creation.

        Args:
            room_instance: The Room instance to validate

        Returns:
            True if all required fields are present, False otherwise
        """
        try:
            # Check if room has a property
            if not hasattr(room_instance, "property") or not room_instance.property:
                logger.warning(f"Room {room_instance.id} missing property")
                return False

            # Check if property is Domorent and has Chekin housing ID
            property_instance = room_instance.property
            if not property_instance.is_domorent:
                logger.debug(
                    f"Room {room_instance.id} belongs to non-Domorent property"
                )
                return False

            if not property_instance.chekin_housing_id:
                logger.warning(
                    f"Room {room_instance.id} property missing Chekin housing ID"
                )
                return False

            logger.debug(f"Room {room_instance.id} passed validation for Chekin room")
            return True

        except Exception as e:
            logger.error(
                f"Error validating room {room_instance.id} for Chekin: {str(e)}"
            )
            return False
