import hashlib
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, Optional

import requests
from django.conf import settings
from django.core.cache import cache
from requests.exceptions import ConnectionError, HTTPError, RequestException, Timeout
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

logger = logging.getLogger(__name__)


class ChekinAPIError(Exception):
    """Custom exception for Chekin API errors"""

    pass


class ChekinAuthenticationError(ChekinAPIError):
    """Exception raised when authentication fails"""

    pass


class ChekinAPIService:
    """
    Service class for interacting with the Chekin API.
    Handles JWT token authentication, reservation management, and error handling.
    """

    # API endpoints for different environments
    SANDBOX_BASE_URL = "https://api-ng.chekintest.xyz/public/api/v1"
    PRODUCTION_BASE_URL = "https://api-ng.chekin.io/public/api/v1"

    def __init__(self, api_key: str = None, environment: str = "sandbox"):
        """
        Initialize the Chekin API service.

        Args:
            api_key: The API key for authentication (defaults to global API key from settings)
            environment: 'sandbox' or 'production'
        """
        # Use global API key for Domorent properties if not provided
        self.api_key = api_key or getattr(settings, "CHEKIN_API_KEY", "")
        self.environment = environment
        self.base_url = (
            self.SANDBOX_BASE_URL
            if environment == "sandbox"
            else self.PRODUCTION_BASE_URL
        )

        # JWT token will be set during authentication
        self._jwt_token = None
        self._token_expires_at = None

        # Cache key for storing JWT token
        self._cache_key = (
            f"chekin_jwt_token_{environment}_"
            f"{hashlib.sha256(self.api_key.encode()).hexdigest()}"
        )

        logger.info(f"Initialized Chekin API service for {environment} environment")

    def _get_cached_token(self) -> Optional[str]:
        """Get cached JWT token if still valid. Robust to string-serialized datetimes."""
        token_data = cache.get(self._cache_key)
        if not token_data:
            return None
        token, expires_at = token_data
        # Normalize expires_at to a datetime
        try:
            from django.utils import timezone as dj_tz

            if isinstance(expires_at, str):
                # Handle possible ISO string with or without Z
                expires_at = datetime.fromisoformat(expires_at.replace("Z", "+00:00"))
            now = dj_tz.now() if getattr(expires_at, "tzinfo", None) else datetime.now()
            if now < expires_at:
                return token
            # Expired
            cache.delete(self._cache_key)
            return None
        except Exception:
            # Corrupt cache entry; clear it
            cache.delete(self._cache_key)
            return None

    def _cache_token(self, token: str, expires_in_seconds: int = 3600):
        """Cache JWT token with expiration."""
        expires_at = datetime.now() + timedelta(
            seconds=expires_in_seconds - 60
        )  # 1 minute buffer
        cache.set(self._cache_key, (token, expires_at), expires_in_seconds - 60)

    def _authenticate(self) -> str:
        """
        Authenticate with Chekin API and get JWT token.

        Returns:
            JWT token string

        Raises:
            ChekinAuthenticationError: When authentication fails
        """
        # Check for cached token first
        cached_token = self._get_cached_token()
        if cached_token:
            return cached_token

        if not self.api_key:
            raise ChekinAuthenticationError("No API key provided")

        auth_url = f"{self.base_url}/auth/api-key/"
        auth_payload = {"api_key": self.api_key}

        try:
            logger.info(f"Authenticating with Chekin API at {auth_url}")
            response = requests.post(
                auth_url,
                json=auth_payload,
                headers={"Content-Type": "application/json"},
                timeout=30,
            )

            if response.status_code == 401:
                logger.error("Chekin API authentication failed - invalid API key")
                raise ChekinAuthenticationError("Invalid API key")

            if not response.ok:
                logger.error(
                    f"Chekin authentication failed with status {response.status_code}: {response.text}"
                )
                raise ChekinAuthenticationError(
                    f"Authentication failed: {response.text}"
                )

            auth_data = response.json()
            token = auth_data.get("token")

            if not token:
                logger.error("No token received in authentication response")
                raise ChekinAuthenticationError("No token received from authentication")

            # Cache the token (expires in 1 hour)
            self._cache_token(token, 3600)

            logger.info("Successfully authenticated with Chekin API")
            return token

        except requests.exceptions.Timeout:
            logger.error("Timeout during Chekin authentication")
            raise ChekinAuthenticationError("Authentication timeout")
        except requests.exceptions.ConnectionError:
            logger.error("Connection error during Chekin authentication")
            raise ChekinAuthenticationError("Authentication connection error")
        except requests.exceptions.RequestException as e:
            logger.error(f"Request exception during authentication: {str(e)}")
            raise ChekinAuthenticationError(f"Authentication failed: {str(e)}")

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get headers with JWT authentication."""
        token = self._authenticate()
        return {
            "Content-Type": "application/json",
            "Authorization": f"JWT {token}",
        }

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(
            (RequestException, Timeout, ConnectionError, ChekinAuthenticationError)
        ),
        reraise=True,
    )
    def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Make an HTTP request to the Chekin API with retry logic and JWT authentication.

        Args:
            method: HTTP method (GET, POST, PATCH, DELETE)
            endpoint: API endpoint (without base URL)
            data: Request payload for POST/PATCH requests
            params: Query parameters for GET requests

        Returns:
            Dict containing the API response

        Raises:
            ChekinAuthenticationError: When authentication fails
            ChekinAPIError: For other API errors
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        try:
            logger.debug(f"Making {method} request to {url}")
            if data:
                logger.debug(f"Request payload: {data}")

            # Get fresh headers with JWT token
            headers = self._get_auth_headers()

            # Track response time
            start_time = time.time()
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                json=data,
                params=params,
                timeout=getattr(settings, "CHEKIN_API_TIMEOUT", 30),
            )
            response_time_ms = int((time.time() - start_time) * 1000)

            # Log response details
            logger.debug(f"Response status: {response.status_code}")
            logger.debug(f"Response time: {response_time_ms}ms")
            logger.debug(f"Response headers: {dict(response.headers)}")

            # Handle authentication errors
            if response.status_code == 401:
                logger.error(
                    "Chekin API authentication failed - token might be expired"
                )
                # Clear cached token and retry once
                cache.delete(self._cache_key)
                raise ChekinAuthenticationError(
                    "JWT token invalid or expired - authentication failed"
                )

            # Handle other HTTP errors
            if not response.ok:
                error_msg = (
                    f"Chekin API request failed with status {response.status_code}"
                )
                try:
                    error_data = response.json()
                    error_msg += f": {error_data}"
                    logger.error(f"API error response: {error_data}")
                except ValueError:
                    error_msg += f": {response.text}"
                    logger.error(f"API error response (non-JSON): {response.text}")

                raise ChekinAPIError(error_msg)

            # Parse and return response
            response_data = response.json()
            logger.debug(f"Response data: {response_data}")

            # Add metadata to response
            response_data["_metadata"] = {
                "status_code": response.status_code,
                "response_time_ms": response_time_ms,
                "headers": dict(response.headers),
            }

            return response_data

        except requests.exceptions.Timeout:
            logger.error(f"Timeout occurred while calling Chekin API: {url}")
            raise
        except requests.exceptions.ConnectionError:
            logger.error(f"Connection error occurred while calling Chekin API: {url}")
            raise
        except requests.exceptions.RequestException as e:
            logger.error(f"Request exception occurred: {str(e)}")
            raise ChekinAPIError(f"Request failed: {str(e)}")

    def create_reservation(self, reservation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new reservation in Chekin.

        Args:
            reservation_data: Dictionary containing reservation details

        Returns:
            Dict containing the created reservation data
        """
        logger.info(
            f"Creating Chekin reservation with external_id: {reservation_data.get('external_id')}"
        )

        try:
            response = self._make_request(
                "POST", "/reservations", data=reservation_data
            )
            logger.info(
                f"Successfully created Chekin reservation: {response.get('id')}"
            )
            return response
        except Exception as e:
            logger.error(f"Failed to create Chekin reservation: {str(e)}")
            raise

    def update_reservation(
        self, reservation_id: str, reservation_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update an existing reservation in Chekin.

        Args:
            reservation_id: The Chekin reservation ID
            reservation_data: Dictionary containing updated reservation details

        Returns:
            Dict containing the updated reservation data
        """
        logger.info(f"Updating Chekin reservation: {reservation_id}")

        try:
            response = self._make_request(
                "PATCH", f"/reservations/{reservation_id}", data=reservation_data
            )
            logger.info(f"Successfully updated Chekin reservation: {reservation_id}")
            return response
        except Exception as e:
            logger.error(
                f"Failed to update Chekin reservation {reservation_id}: {str(e)}"
            )
            raise

    def delete_reservation(self, reservation_id: str) -> bool:
        """
        Delete a reservation from Chekin.

        Args:
            reservation_id: The Chekin reservation ID

        Returns:
            True if deletion was successful
        """
        logger.info(f"Deleting Chekin reservation: {reservation_id}")

        try:
            self._make_request("DELETE", f"/reservations/{reservation_id}")
            logger.info(f"Successfully deleted Chekin reservation: {reservation_id}")
            return True
        except Exception as e:
            logger.error(
                f"Failed to delete Chekin reservation {reservation_id}: {str(e)}"
            )
            raise

    def create_housing(self, housing_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new housing in Chekin.

        Args:
            housing_data: Dictionary containing housing details

        Returns:
            Dict containing the created housing data
        """
        logger.info(
            f"Creating Chekin housing with external_id: {housing_data.get('external_id')}"
        )

        try:
            response = self._make_request("POST", "/housings", data=housing_data)
            logger.info(f"Successfully created Chekin housing: {response.get('id')}")
            return response
        except Exception as e:
            logger.error(f"Failed to create Chekin housing: {str(e)}")
            raise

    def update_housing(
        self, housing_id: str, housing_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update an existing housing in Chekin.

        Args:
            housing_id: The Chekin housing ID
            housing_data: Dictionary containing updated housing details

        Returns:
            Dict containing the updated housing data
        """
        logger.info(f"Updating Chekin housing: {housing_id}")

        try:
            # Do not allow updating external_id via PATCH to avoid validation errors
            safe_data = dict(housing_data or {})
            safe_data.pop("external_id", None)
            response = self._make_request(
                "PATCH", f"/housings/{housing_id}", data=safe_data
            )
            logger.info(f"Successfully updated Chekin housing: {housing_id}")
            return response
        except Exception as e:
            logger.error(f"Failed to update Chekin housing {housing_id}: {str(e)}")
            raise

    def delete_housing(self, housing_id: str) -> bool:
        """
        Delete a housing from Chekin.

        Args:
            housing_id: The Chekin housing ID

        Returns:
            True if deletion was successful
        """
        logger.info(f"Deleting Chekin housing: {housing_id}")

        try:
            self._make_request("DELETE", f"/housings/{housing_id}")
            logger.info(f"Successfully deleted Chekin housing: {housing_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete Chekin housing {housing_id}: {str(e)}")
            raise

    def find_housing_by_external_id(self, external_id: str) -> Optional[Dict[str, Any]]:
        """
        Attempt to locate an existing housing by external_id, for idempotent create.
        Returns the first match or None.
        """
        try:
            resp = self._make_request(
                "GET", "/housings", params={"external_id": external_id, "limit": 1}
            )
            # Chekin responses may be list or dict with results; support both
            if isinstance(resp, dict):
                items = resp.get("results") or resp.get("data") or []
            else:
                items = resp
            if items:
                return items[0]
            return None
        except Exception as e:
            logger.warning(
                f"Failed to lookup housing by external_id {external_id}: {e}"
            )
            return None

    def create_room(self, room_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new room in Chekin.

        Args:
            room_data: Dictionary containing room details

        Returns:
            Dict containing the created room data
        """
        logger.info(
            f"Creating Chekin room with external_id: {room_data.get('external_id')}"
        )

        try:
            response = self._make_request("POST", "/rooms", data=room_data)
            logger.info(f"Successfully created Chekin room: {response.get('id')}")
            return response
        except Exception as e:
            logger.error(f"Failed to create Chekin room: {str(e)}")
            raise

    def update_room(self, room_id: str, room_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing room in Chekin.

        Args:
            room_id: The Chekin room ID
            room_data: Dictionary containing updated room details

        Returns:
            Dict containing the updated room data
        """
        logger.info(f"Updating Chekin room: {room_id}")

        try:
            # Do not allow updating external_id via PATCH to avoid validation errors
            safe_data = dict(room_data or {})
            safe_data.pop("external_id", None)
            response = self._make_request("PATCH", f"/rooms/{room_id}", data=safe_data)
            logger.info(f"Successfully updated Chekin room: {room_id}")
            return response
        except Exception as e:
            logger.error(f"Failed to update Chekin room {room_id}: {str(e)}")
            raise

    def delete_room(self, room_id: str) -> bool:
        """
        Delete a room from Chekin.

        Args:
            room_id: The Chekin room ID

        Returns:
            True if deletion was successful
        """
        logger.info(f"Deleting Chekin room: {room_id}")

        try:
            self._make_request("DELETE", f"/rooms/{room_id}")
            logger.info(f"Successfully deleted Chekin room: {room_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete Chekin room {room_id}: {str(e)}")
            raise

    def find_room_by_external_id(self, external_id: str) -> Optional[Dict[str, Any]]:
        """
        Attempt to locate an existing room by external_id, for idempotent create.
        Returns the first match or None.
        """
        try:
            resp = self._make_request(
                "GET", "/rooms", params={"external_id": external_id, "limit": 1}
            )
            # Chekin responses may be list or dict with results; support both
            if isinstance(resp, dict):
                items = resp.get("results") or resp.get("data") or []
            else:
                items = resp
            if items:
                return items[0]
            return None
        except Exception as e:
            logger.warning(f"Failed to lookup room by external_id {external_id}: {e}")
            return None

    def test_connection(self) -> Dict[str, Any]:
        """
        Test the connection to Chekin API.

        Returns:
            Dict with connection test results
        """
        try:
            # Try to make a simple request to test authentication
            # We'll use a GET request to reservations with a limit to minimize data transfer
            self._make_request("GET", "/reservations", params={"limit": 1})
            return {"success": True, "error": None}
        except ChekinAuthenticationError as e:
            return {"success": False, "error": f"Authentication failed: {str(e)}"}
        except Exception as e:
            return {"success": False, "error": f"Connection test failed: {str(e)}"}
