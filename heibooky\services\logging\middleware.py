import logging
import time

from django.conf import settings
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)


class RequestLogMiddleware(MiddlewareMixin):
    """
    Optimized middleware for logging HTTP requests with essential timing metrics.
    Only logs errors, warnings, and slow requests to reduce overhead.
    """

    def __init__(self, get_response):
        super().__init__(get_response)
        # Cache settings to avoid repeated lookups
        self.debug_mode = getattr(settings, "DEBUG", False)
        self.slow_request_threshold = 2.0  # seconds

    def process_request(self, request):
        """Start timing - minimal overhead."""
        request._start_time = time.time()

    def process_response(self, request, response):
        """Log only important events to reduce overhead."""
        if not hasattr(request, "_start_time"):
            return response

        duration = time.time() - request._start_time

        # Only log if it's an error, warning, or slow request
        should_log = (
            response.status_code >= 400  # Client/server errors
            or duration > self.slow_request_threshold  # Slow requests
            or self.debug_mode  # All requests in debug mode
        )

        if should_log:
            # Determine log level
            if response.status_code >= 500:
                log_level = logging.ERROR
            elif response.status_code >= 400 or duration > self.slow_request_threshold:
                log_level = logging.WARNING
            else:
                log_level = logging.INFO

            # Simplified logging with essential info only
            logger.log(
                log_level,
                f"{request.method} {request.path} - {response.status_code} - {duration:.3f}s",
                extra={
                    "event_type": "request_completed",
                    "status_code": response.status_code,
                    "duration": duration,
                    "method": request.method,
                    "path": request.path,
                },
            )

        return response

    def process_exception(self, request, exception):
        """Log exceptions - always important."""
        duration = time.time() - getattr(request, "_start_time", time.time())

        logger.error(
            f"{request.method} {request.path} - {exception.__class__.__name__}: {str(exception)} - {duration:.3f}s",
            extra={
                "event_type": "request_exception",
                "exception_type": exception.__class__.__name__,
                "duration": duration,
                "method": request.method,
                "path": request.path,
            },
            exc_info=True,
        )


class SecurityLogMiddleware(MiddlewareMixin):
    """
    Optimized middleware for logging critical security events only.
    Reduced pattern matching and focused on high-priority threats.
    """

    def __init__(self, get_response):
        super().__init__(get_response)
        # Pre-compile critical patterns for faster matching
        self.critical_patterns = {
            "/admin/",
            "/wp-admin/",
            "/phpmyadmin/",
            "../",
            "script>",
            "SELECT * FROM",
        }

    def process_request(self, request):
        """Log only critical security threats to reduce overhead."""
        path_lower = (
            request.path.lower()
        )  # Use path instead of get_full_path() for performance

        # Quick check for critical patterns only
        for pattern in self.critical_patterns:
            if pattern in path_lower:
                logger.warning(
                    f"Critical security threat: {request.method} {request.path}",
                    extra={
                        "event_type": "security_threat",
                        "pattern_matched": pattern,
                        "method": request.method,
                        "path": request.path,
                        "ip": self._get_client_ip(request),
                    },
                )
                break

    def process_response(self, request, response):
        """Log only authentication failures - most critical security events."""
        if response.status_code in (401, 403):
            event_type = (
                "auth_failed" if response.status_code == 401 else "access_denied"
            )
            logger.warning(
                f"Security event: {request.method} {request.path} - {response.status_code}",
                extra={
                    "event_type": event_type,
                    "status_code": response.status_code,
                    "method": request.method,
                    "path": request.path,
                    "ip": self._get_client_ip(request),
                },
            )
        return response

    def _get_client_ip(self, request):
        """Fast IP extraction."""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            return x_forwarded_for.split(",")[0].strip()
        return request.META.get("REMOTE_ADDR", "unknown")


class PerformanceLogMiddleware(MiddlewareMixin):
    """
    Lightweight performance monitoring focused on critical issues only.
    Removed expensive psutil operations for better performance.
    """

    def __init__(self, get_response):
        super().__init__(get_response)
        self.critical_threshold = 5.0  # Only log requests taking more than 5 seconds
        self.warning_threshold = 2.0  # Warning for requests over 2 seconds

    def process_request(self, request):
        """Minimal performance tracking - just timing."""
        request._perf_start_time = time.time()

    def process_response(self, request, response):
        """Log only critical performance issues."""
        if not hasattr(request, "_perf_start_time"):
            return response

        duration = time.time() - request._perf_start_time

        # Only log if request is significantly slow
        if duration > self.warning_threshold:
            log_level = (
                logging.ERROR if duration > self.critical_threshold else logging.WARNING
            )
            performance_level = (
                "critical" if duration > self.critical_threshold else "warning"
            )

            logger.log(
                log_level,
                f"Slow request: {request.method} {request.path} - {duration:.3f}s",
                extra={
                    "event_type": "performance_issue",
                    "duration": duration,
                    "performance_level": performance_level,
                    "method": request.method,
                    "path": request.path,
                },
            )

        return response
