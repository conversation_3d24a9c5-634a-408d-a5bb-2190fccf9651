# Heibooky Monitoring and Observability Stack

This document describes the comprehensive monitoring and observability solution implemented for the Heibooky Django application.

## Overview

The monitoring stack includes:

- **Prometheus** - Metrics collection and alerting
- **Grafana** - Visualization and dashboards
- **Alertmanager** - Alert routing and management
- **Loki** - Log aggregation
- **Promtail** - Log collection agent
- **Redis Exporter** - Redis metrics
- **Node Exporter** - System metrics
- **cAdvisor** - Container metrics

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Django App    │    │     Redis       │    │     Nginx       │
│                 │    │                 │    │                 │
│ /metrics        │◄───┤ Redis Exporter  │    │                 │
│ /health         │    │                 │    │                 │
│ /ready          │    └─────────────────┘    └─────────────────┘
│ /alive          │                                    │
└─────────────────┘                                    │
         │                                             │
         │                                             │
         ▼                                             ▼
┌─────────────────┐                           ┌─────────────────┐
│   Prometheus    │                           │   Node Exporter │
│                 │                           │                 │
│ - Metrics       │                           │ - System Stats  │
│ - Alerts        │                           │                 │
└─────────────────┘                           └─────────────────┘
         │                                             │
         │                                             │
         ▼                                             │
┌─────────────────┐                                    │
│  Alertmanager   │                                    │
│                 │                                    │
│ - Route Alerts  │                                    │
│ - Notifications │                                    │
└─────────────────┘                                    │
         │                                             │
         │                                             │
         ▼                                             ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Grafana      │    │      Loki       │    │    cAdvisor     │
│                 │    │                 │    │                 │
│ - Dashboards    │    │ - Log Storage   │    │ - Container     │
│ - Visualization │    │ - Log Query     │    │   Metrics       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                │
                                ▼
                       ┌─────────────────┐
                       │    Promtail     │
                       │                 │
                       │ - Log Collection│
                       │ - Log Parsing   │
                       └─────────────────┘
```

## Features

### Application Metrics
- HTTP request rate, latency, and error rates
- Active request count
- Database connection metrics
- Cache operation metrics
- Memory and CPU usage
- Custom business metrics

### Infrastructure Metrics
- System resource utilization (CPU, memory, disk)
- Container metrics and resource limits
- Redis performance and memory usage
- Network I/O and disk I/O

### Logs
- Structured JSON logging
- Request/response logging with timing
- Security event logging
- Performance issue detection
- Error tracking with stack traces

### Alerting
- Application health alerts
- Performance degradation alerts
- Resource utilization alerts
- Error rate thresholds
- Custom business logic alerts

## Quick Start

### 1. Start the Monitoring Stack

```bash
# Using the management script (Linux/macOS)
./scripts/monitoring.sh start

# Or using the Windows batch file
scripts\monitoring.bat start

# Or manually with Docker Compose
docker-compose up -d prometheus grafana alertmanager redis-exporter node-exporter cadvisor loki promtail
```

### 2. Access the Services

- **Grafana**: http://localhost:3000 (admin/admin123)
- **Prometheus**: http://localhost:9090
- **Alertmanager**: http://localhost:9093
- **cAdvisor**: http://localhost:8080

### 3. Application Endpoints

- **Metrics**: http://localhost:8000/monitoring/metrics/
- **Health Check**: http://localhost:8000/monitoring/health/
- **Readiness Check**: http://localhost:8000/monitoring/ready/
- **Liveness Check**: http://localhost:8000/monitoring/alive/

## Configuration

### Environment Variables

```bash
# Grafana
GRAFANA_ADMIN_PASSWORD=your-secure-password
GRAFANA_SECRET_KEY=your-secret-key

# Redis (for monitoring user)
REDIS_PASSWORD_FILE=/run/secrets/redis_password
```

### Prometheus Configuration

Located in `monitoring/prometheus/prometheus.yml`:

- Scrape intervals and timeouts
- Target definitions
- Alert rule files
- Alertmanager configuration

### Grafana Dashboards

Pre-configured dashboards include:

1. **Application Overview** - Request rates, error rates, response times
2. **Infrastructure** - System resources, container metrics
3. **Redis** - Memory usage, connection counts, operations
4. **Logs** - Log analysis and search

### Alert Rules

Defined in `monitoring/prometheus/alert_rules.yml`:

- **Critical**: Application down, high error rates, system failures
- **Warning**: High latency, resource usage, performance issues

## Metrics Reference

### Application Metrics

| Metric | Type | Description |
|--------|------|-------------|
| `django_http_requests_total` | Counter | Total HTTP requests by method, status, endpoint |
| `django_http_request_duration_seconds` | Histogram | Request duration distribution |
| `django_http_requests_active` | Gauge | Currently active requests |
| `django_process_memory_usage_bytes` | Gauge | Process memory usage |
| `django_process_cpu_percent` | Gauge | Process CPU usage percentage |
| `celery_tasks_total` | Counter | Celery tasks by name and state |
| `celery_queue_length` | Gauge | Queue length by queue name |
| `celery_workers_total` | Gauge | Active Celery workers |

### Infrastructure Metrics

| Metric | Type | Description |
|--------|------|-------------|
| `node_cpu_seconds_total` | Counter | CPU time by mode |
| `node_memory_MemTotal_bytes` | Gauge | Total system memory |
| `node_memory_MemAvailable_bytes` | Gauge | Available system memory |
| `node_filesystem_size_bytes` | Gauge | Filesystem size |
| `node_filesystem_free_bytes` | Gauge | Free filesystem space |
| `redis_memory_used_bytes` | Gauge | Redis memory usage |
| `redis_connected_clients` | Gauge | Connected Redis clients |

## Log Structure

### Structured Logs

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "logger": "django.request",
  "message": "Request completed successfully",
  "module": "views",
  "function": "user_list",
  "line": 42,
  "process_id": 1234,
  "thread_id": 5678,
  "request": {
    "method": "GET",
    "path": "/users/",
    "remote_addr": "*************",
    "user_agent": "Mozilla/5.0...",
    "user_id": 123,
    "session_key": "abc123"
  },
  "extra": {
    "duration": 0.156,
    "status_code": 200
  }
}
```

### Log Types

1. **Application Logs** (`django.log`) - General application events
2. **Error Logs** (`error.log`) - Errors and exceptions
3. **Security Logs** (`security.log`) - Security-related events
4. **Performance Logs** (`performance.log`) - Performance issues
5. **Structured Logs** (`structured.log`) - Machine-readable JSON logs

## Alerting

### Alert Routing

Alerts are routed based on severity:

- **Critical**: Email + webhook to application
- **Warning**: Email notification
- **Info**: Logged only

### Webhook Integration

The application provides a webhook endpoint at `/monitoring/webhook/` for receiving alerts from Alertmanager. This can be extended to:

- Send Slack/Discord notifications
- Create tickets in issue tracking systems
- Trigger auto-scaling
- Send SMS for critical alerts

## Security

### Access Control

- Grafana: Admin user with configurable password
- Prometheus: Internal network access only
- Alertmanager: Internal network access only

### Network Segmentation

- Application network: `backend` (**********/16)
- Monitoring network: `monitoring` (**********/16)
- Services communicate only as needed

### Secrets Management

- Redis passwords stored in Docker secrets
- Grafana credentials via environment variables
- TLS certificates via Let's Encrypt

## Maintenance

### Backup

```bash
# Backup monitoring data
./scripts/monitoring.sh backup
```

Backs up:
- Grafana dashboards and settings
- Prometheus metrics data
- Configuration files

### Updates

```bash
# Update monitoring stack
./scripts/monitoring.sh update
```

### Health Checks

```bash
# Check service status
./scripts/monitoring.sh status

# View resource usage
./scripts/monitoring.sh resources

# Generate health report
./scripts/monitoring.sh report
```

## Performance Tuning

### Prometheus

- **Retention**: 15 days (configurable)
- **Scrape Interval**: 15s for most services, 30s for heavy exporters
- **Storage**: Efficient TSDB with compression

### Grafana

- **Refresh Rate**: 30s default, configurable per dashboard
- **Query Optimization**: Use recording rules for expensive queries
- **Caching**: Built-in query result caching

### Resource Limits

- **Prometheus**: 1GB RAM, 0.5 CPU
- **Grafana**: 512MB RAM, 0.5 CPU
- **Alertmanager**: 256MB RAM, 0.25 CPU
- **Exporters**: 128MB RAM, 0.1 CPU each

## Troubleshooting

### Common Issues

1. **High Memory Usage**
   - Check retention policies
   - Review query complexity
   - Monitor cardinality

2. **Missing Metrics**
   - Verify scrape targets
   - Check network connectivity
   - Review service discovery

3. **Alert Fatigue**
   - Adjust thresholds
   - Implement alert grouping
   - Use inhibition rules

### Debugging

```bash
# View logs for specific service
./scripts/monitoring.sh logs prometheus

# Check container status
docker-compose ps

# Verify metrics endpoint
curl http://localhost:8000/monitoring/metrics/
```

## Development

### Adding Custom Metrics

1. Create metrics in `apps/monitoring/metrics.py`
2. Export via middleware or views
3. Add to Prometheus scrape config
4. Create Grafana panels

### Custom Alerts

1. Add rules to `monitoring/prometheus/alert_rules.yml`
2. Configure routing in `monitoring/alertmanager/alertmanager.yml`
3. Test with Prometheus expression browser

### Dashboard Development

1. Create dashboards in Grafana UI
2. Export JSON configuration
3. Add to `monitoring/grafana/provisioning/dashboards/`
4. Version control the changes

## Support

For issues with the monitoring setup:

1. Check the logs: `./scripts/monitoring.sh logs`
2. Verify service status: `./scripts/monitoring.sh status`
3. Review the configuration files
4. Consult the official documentation for each component

## References

- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [Alertmanager Documentation](https://prometheus.io/docs/alerting/latest/alertmanager/)
- [Loki Documentation](https://grafana.com/docs/loki/latest/)
- [Django Prometheus Integration](https://django-prometheus.readthedocs.io/)
