#!/bin/bash

# Network Conflict Checker for Docker Compose Monitoring Setup

set -euo pipefail

echo "Docker Network Conflict Checker"
echo "==============================="

# Default subnet ranges
MONITORING_SUBNET=${MONITORING_SUBNET:-**********/16}
BACKEND_SUBNET=${BACKEND_SUBNET:-**********/16}

echo "Checking for potential conflicts with:"
echo "  Monitoring subnet: ${MONITORING_SUBNET}"
echo "  Backend subnet: ${BACKEND_SUBNET}"
echo ""

# Build simple regexes from the first two octets (works well for /16 defaults).
# Note: for non-/16 CIDRs this is an approximation; see later comments for robust overlap checks.
MON_NET=$(echo "$MONITORING_SUBNET" | cut -d/ -f1)
BACK_NET=$(echo "$BACKEND_SUBNET" | cut -d/ -f1)
MON_REGEX=$(echo "$MON_NET" | awk -F. '{printf "%s\\.%s\\.", $1, $2}')
BACK_REGEX=$(echo "$BACK_NET" | awk -F. '{printf "%s\\.%s\\.", $1, $2}')
COMBINED_REGEX="(${MON_REGEX}|${BACK_REGEX})"

# Init vars used in conditions to avoid set -u aborts when sections are skipped.
MONITORING_NETWORKS=""
EXISTING_NETWORKS=""
FOUND_DOCKER_SUBNET_CONFLICT=""
CONFLICTING_ROUTES=""
INTERFACES=""

# Check system routes
echo "1. Checking system routing table..."
if command -v ip >/dev/null 2>&1; then
    echo "   Existing routes that might conflict:"
    CONFLICTING_ROUTES=$(ip route | grep -E "$COMBINED_REGEX" || true)
    if [ -z "$CONFLICTING_ROUTES" ]; then
            echo "   ✓ No conflicting routes found"
    else
            echo "   ⚠ Found potentially conflicting routes:"
            echo "$CONFLICTING_ROUTES" | sed 's/^/     /'
    fi
else
    echo "   ⏭ 'ip' command not found; skipping route checks"
fi
echo ""

# Check Docker networks
echo "2. Checking existing Docker networks..."
if command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
    EXISTING_NETWORKS=$(docker network ls --format "{{.Name}}\t{{.Driver}}\t{{.Scope}}" || true)
    if [ -n "$EXISTING_NETWORKS" ]; then
        echo "   Current Docker networks:"
        echo "$EXISTING_NETWORKS" | sed 's/^/     /'
    else
        echo "   (no Docker networks listed)"
    fi

    MONITORING_NETWORKS=$(docker network ls --format "{{.Name}}" | grep -E "(monitoring|backend)" || true)
    if [ -z "$MONITORING_NETWORKS" ]; then
            echo "   ✓ No existing monitoring/backend networks found"
    else
            echo "   ⚠ Found existing monitoring/backend networks:"
            echo "$MONITORING_NETWORKS" | sed 's/^/     /'
    fi
else
    echo "   ⏭ Docker not available or daemon not running; skipping Docker network listing"
fi
echo ""

# Check network interface conflicts
echo "3. Checking network interfaces..."
if command -v ip >/dev/null 2>&1; then
    INTERFACES=$(ip addr show | grep -E "$COMBINED_REGEX" || true)
    if [ -z "$INTERFACES" ]; then
            echo "   ✓ No conflicting interface addresses found"
    else
            echo "   ⚠ Found potentially conflicting interface addresses:"
            echo "$INTERFACES" | sed 's/^/     /'
    fi
else
    echo "   ⏭ 'ip' command not found; skipping interface checks"
fi
echo ""

# Check Docker bridge networks specifically
echo "4. Checking Docker network subnet details..."
if command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
        DOCKER_NETS=$(docker network ls --format "{{.Name}}" || true)
        for network in $DOCKER_NETS; do
                SUBNET_INFO=$(docker network inspect "$network" --format "{{range .IPAM.Config}}{{.Subnet}} {{end}}" 2>/dev/null || true)
                if [ -n "$SUBNET_INFO" ] && [[ "$SUBNET_INFO" =~ $COMBINED_REGEX ]]; then
                        echo "   ⚠ Network '$network' uses subnet(s): $SUBNET_INFO"
                        FOUND_DOCKER_SUBNET_CONFLICT="1"
                fi
        done
else
        echo "   ⏭ Docker not available or daemon not running; skipping Docker subnet inspection"
fi
echo ""
# Recommendations
echo "5. Recommendations:"
if [ -n "${CONFLICTING_ROUTES}" ] || [ -n "${MONITORING_NETWORKS}" ] || [ -n "${INTERFACES}" ] || [ -n "${FOUND_DOCKER_SUBNET_CONFLICT}" ]; then
        echo "   ⚠ Potential conflicts detected!"
        echo "   Consider using different subnet ranges by setting environment variables:"
        echo "     export MONITORING_SUBNET=**********/16"
        echo "     export BACKEND_SUBNET=**********/16"
        echo ""
        echo "   Or choose from these available private ranges:"
        echo "     - 10.0.0.0/8 (if not used by your infrastructure)"
        echo "     - **********/12 (avoid 172.20-21 if conflicts exist)"
        echo "     - ***********/16 (if not used by your infrastructure)"
else
        echo "   ✓ No conflicts detected - safe to proceed with default subnets"
fi

echo ""
echo "Network conflict check complete!"
