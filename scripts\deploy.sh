#!/bin/bash
set -e

# Heibooky Zero-Downtime Deployment Script
# Usage: ./deploy.sh [staging|production] [--force] [--init-ssl]

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_FILE="/var/log/heibooky/deploy.log"

# Default values
ENVIRONMENT=""
FORCE=false
INIT_SSL=false
SKIP_CLEANUP=false
IMAGE_TAG="${IMAGE_TAG:-latest}"
SSL_DOMAIN="${SSL_DOMAIN:-backend.heibooky.com}"
SSL_EMAIL="${SSL_EMAIL:-<EMAIL>}"

# Zero-downtime deployment configuration
CONTAINER_START_TIMEOUT=60
CONTAINER_HEALTH_TIMEOUT=60
CONTAINER_HEALTH_INTERVAL=5
GRACEFUL_STOP_TIMEOUT=30
TRAFFIC_SHIFT_DELAY=10

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_FILE"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            staging|production)
                ENVIRONMENT="$1"
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            --init-ssl)
                INIT_SSL=true
                shift
                ;;
            --skip-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            --image-tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            --ssl-domain)
                SSL_DOMAIN="$2"
                shift 2
                ;;
            --ssl-email)
                SSL_EMAIL="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    if [ -z "$ENVIRONMENT" ]; then
        error "Environment must be specified (staging or production)"
        show_help
        exit 1
    fi
}

show_help() {
    cat << EOF
Heibooky Deployment Script

Usage: $0 [staging|production] [OPTIONS]

Options:
    --force             Force deployment without confirmation
    --init-ssl          Initialize SSL certificates before deployment
    --skip-cleanup      Skip Docker cleanup after deployment
    --image-tag TAG     Specify Docker image tag (default: latest)
    --ssl-domain DOMAIN Specify SSL domain (default: backend.heibooky.com)
    --ssl-email EMAIL   Specify SSL email (default: <EMAIL>)
    --help              Show this help message

Environment Variables:
    SSL_DOMAIN          SSL domain name (overrides default)
    SSL_EMAIL           SSL email address (overrides default)
    IMAGE_TAG           Docker image tag (overrides default)

Examples:
    $0 staging
    $0 production --force
    $0 production --init-ssl
    $0 staging --image-tag v1.2.3
    $0 production --init-ssl --ssl-domain example.com --ssl-email <EMAIL>
    SSL_DOMAIN=example.com SSL_EMAIL=<EMAIL> $0 production --init-ssl
EOF
}
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if running as correct user (restrict to a fixed set of service accounts)
    ALLOWED_USERS=("root" "ubuntu" "ec2-user")
    if [[ ! " ${ALLOWED_USERS[@]} " =~ " ${USER} " ]] && [ "$FORCE" != true ]; then
        error "This script should be run as one of: ${ALLOWED_USERS[*]}. Use --force to override."
        exit 1
    fi
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker is not running"
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if Doppler is installed
    if ! command -v doppler &> /dev/null; then
        error "Doppler CLI is not installed"
        exit 1
    fi
    
    # Verify Doppler authentication
    if ! doppler me &> /dev/null; then
        error "Doppler is not authenticated. Please run 'doppler login' first."
        exit 1
    fi

    # Check Docker Hub authentication (optional but recommended)
    if ! docker info | grep -q "Username:"; then
        warn "Docker Hub authentication not detected. Images will be pulled anonymously."
        warn "For private repositories, ensure Docker Hub credentials are configured."
        warn "Run 'docker login docker.io' if needed."
    else
        log "Docker Hub authentication detected"
    fi

    log "Prerequisites check passed"
}

# Set Doppler configuration based on environment
setup_doppler_config() {
    log "Setting up Doppler configuration for $ENVIRONMENT..."

    case $ENVIRONMENT in
        staging)
            export DOPPLER_PROJECT="heibooky-backend"
            export DOPPLER_CONFIG="stg"
            ;;
        production)
            export DOPPLER_PROJECT="heibooky-backend"
            export DOPPLER_CONFIG="prd"
            ;;
        *)
            error "Unknown environment: $ENVIRONMENT"
            exit 1
            ;;
    esac

    log "Doppler configured: Project=$DOPPLER_PROJECT, Config=$DOPPLER_CONFIG"
}

# Initialize SSL certificates
initialize_ssl() {
    log "Initializing SSL certificates..."

    # Check if SSL initialization script exists
    if [ ! -f "scripts/init-ssl.sh" ]; then
        error "SSL initialization script not found: scripts/init-ssl.sh"
        exit 1
    fi

    # Validate SSL configuration
    if [ -z "$SSL_DOMAIN" ] || [ -z "$SSL_EMAIL" ]; then
        error "SSL domain and email must be specified"
        error "Set SSL_DOMAIN and SSL_EMAIL environment variables or use --ssl-domain and --ssl-email options"
        exit 1
    fi

    # Make script executable
    chmod +x scripts/init-ssl.sh

    # Run SSL initialization
    if [ "$ENVIRONMENT" = "production" ]; then
        log "Running SSL initialization for production..."
        log "Domain: $SSL_DOMAIN, Email: $SSL_EMAIL"
        ./scripts/init-ssl.sh --domain "$SSL_DOMAIN" --email "$SSL_EMAIL"
    else
        log "Running SSL initialization for staging (using staging certificates)..."
        log "Domain: $SSL_DOMAIN, Email: $SSL_EMAIL"
        ./scripts/init-ssl.sh --domain "$SSL_DOMAIN" --email "$SSL_EMAIL" --staging
    fi

    log "SSL initialization completed"
}

# Check specific container health
check_container_health() {
    local container_name="$1"
    local timeout="${2:-60}"
    local interval="${3:-5}"
    
    log "Checking health of container: $container_name"
    
    local elapsed=0
    while [ $elapsed -lt $timeout ]; do
        local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")
        
        if [ "$health_status" = "healthy" ]; then
            log "Container $container_name is healthy"
            return 0
        elif [ "$health_status" = "unhealthy" ]; then
            error "Container $container_name is unhealthy"
            return 1
        fi
        
        info "Container $container_name health status: $health_status, waiting... (${elapsed}/${timeout}s elapsed)"
        sleep $interval
        elapsed=$((elapsed + interval))
    done
    
    error "Container $container_name health check timeout"
    return 1
}

# Get running web container IDs
get_web_containers() {
    docker-compose -f docker-compose.prod.yml ps -q web 2>/dev/null || echo ""
}

# Get container port mapping
get_container_port() {
    local container_id="$1"
    docker port "$container_id" 8000 2>/dev/null | head -n1 | cut -d: -f2
}

# Test container endpoint
test_container_endpoint() {
    local container_id="$1"
    local port=$(get_container_port "$container_id")
    
    if [ -z "$port" ]; then
        warn "Could not determine port for container $container_id"
        return 1
    fi
    
    local endpoint="http://localhost:$port"
    log "Testing container endpoint: $endpoint"
    
    # Test health endpoint with timeout
    if timeout 10s curl -f -s "$endpoint/health/" > /dev/null 2>&1; then
        log "Container $container_id endpoint test passed"
        return 0
    else
        warn "Container $container_id endpoint test failed"
        return 1
    fi
}

# Pre-deployment validation
validate_deployment() {
    log "Validating deployment prerequisites..."
    
    # Check if docker-compose.prod.yml exists
    if [ ! -f "docker-compose.prod.yml" ]; then
        error "docker-compose.prod.yml not found"
        return 1
    fi
    
    # Validate docker-compose file syntax
    if ! docker-compose -f docker-compose.prod.yml config > /dev/null 2>&1; then
        error "Invalid docker-compose.prod.yml syntax"
        return 1
    fi
    
    # Check if required services are defined
    local required_services="web redis"
    for service in $required_services; do
        if ! docker-compose -f docker-compose.prod.yml config --services | grep -q "^${service}$"; then
            error "Required service '$service' not found in docker-compose.prod.yml"
            return 1
        fi
    done
    
    # Check Docker system resources
    local available_memory=$(docker system df --format "{{.Reclaimable}}" | head -n1 | sed 's/[^0-9.]//g')
    local available_disk=$(df / | awk 'NR==2 {print $4}')
    
    log "Docker system check - Available disk: ${available_disk}KB"
    
    # Verify Doppler configuration (already set by setup_doppler_config)
    if ! doppler configs --project="$DOPPLER_PROJECT" | grep -q "$DOPPLER_CONFIG"; then
        warn "Doppler config '$DOPPLER_CONFIG' not found in project '$DOPPLER_PROJECT', deployment may fail"
    fi
    
    log "Pre-deployment validation completed"
    return 0
}

# Deploy function with blue-green strategy
deploy() {
    log "Starting zero-downtime deployment to $ENVIRONMENT..."
    
    # Get current running containers
    local current_containers=$(get_web_containers)
    local current_count=$(echo "$current_containers" | wc -w)
    
    if [ $current_count -eq 0 ]; then
        log "No running containers found, performing initial deployment"
        deploy_initial
        return $?
    fi
    
    log "Found $current_count running web container(s), performing rolling update"
    
    # Pull latest images without affecting running containers
    log "Pulling latest Docker images..."
    export IMAGE_TAG="$IMAGE_TAG"

    # Retry logic for image pulling with fallback to local build
    local max_retries=3
    local retry_count=0
    local pull_success=false

    while [ $retry_count -lt $max_retries ]; do
        if doppler run --project="$DOPPLER_PROJECT" --config="$DOPPLER_CONFIG" -- docker-compose -f docker-compose.prod.yml pull web; then
            log "✅ Successfully pulled Docker images from Docker Hub"
            pull_success=true
            break
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                warn "Failed to pull images (attempt $retry_count/$max_retries). Retrying in 10 seconds..."
                sleep 10
            else
                warn "Failed to pull Docker images after $max_retries attempts"
                break
            fi
        fi
    done

    # If pull failed, try to build locally as fallback
    if [ "$pull_success" = false ]; then
        warn "⚠️  Docker Hub image pull failed after $max_retries attempts"
        warn "⚠️  Falling back to local image building as secondary strategy"
        warn "⚠️  This may indicate network issues or missing images on Docker Hub"

        log "Attempting to build Docker images locally..."
        if doppler run --project="$DOPPLER_PROJECT" --config="$DOPPLER_CONFIG" -- docker-compose -f docker-compose.prod.yml build web; then
            log "✅ Successfully built Docker images locally as fallback"
            warn "⚠️  Local build completed, but consider investigating Docker Hub connectivity"
        else
            error "❌ Failed to build Docker images locally"
            error "❌ Both Docker Hub pull and local build have failed"
            error "Please check Docker Hub connectivity and local build environment"
            exit 1
        fi
    fi

    # Perform rolling deployment
    deploy_rolling_update "$current_count"
}

# Initial deployment (no existing containers)
deploy_initial() {
    log "Performing initial deployment..."

    # Check if Doppler is configured
    if ! doppler me >/dev/null 2>&1; then
        error "Doppler not authenticated. Setting up Doppler..."
        if [ -n "$DOPPLER_TOKEN" ]; then
            echo "$DOPPLER_TOKEN" | doppler configure set token --scope /opt/heibooky
        else
            error "DOPPLER_TOKEN environment variable not set"
            exit 1
        fi
    fi

    # Verify required environment variables are available through Doppler
    log "Verifying Doppler configuration..."
    doppler secrets --project="$DOPPLER_PROJECT" --config="$DOPPLER_CONFIG" | head -5

    # Pull latest images first
    log "Pulling latest Docker images..."
    export IMAGE_TAG="$IMAGE_TAG"

    # Retry logic for image pulling with fallback to local build
    local max_retries=3
    local retry_count=0
    local pull_success=false

    while [ $retry_count -lt $max_retries ]; do
        if doppler run --project="$DOPPLER_PROJECT" --config="$DOPPLER_CONFIG" -- docker-compose -f docker-compose.prod.yml pull; then
            log "✅ Successfully pulled Docker images"
            pull_success=true
            break
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                warn "Failed to pull images (attempt $retry_count/$max_retries). Retrying in 10 seconds..."
                sleep 10
            else
                warn "Failed to pull Docker images after $max_retries attempts"
                break
            fi
        fi
    done

    # If pull failed, try to build locally as fallback
    if [ "$pull_success" = false ]; then
        warn "⚠️  Docker Hub image pull failed after $max_retries attempts"
        warn "⚠️  Falling back to local image building as secondary strategy"
        warn "⚠️  This may indicate network issues or missing images on Docker Hub"
        warn "⚠️  Consider running: ./scripts/migrate-to-dockerhub.sh to ensure images are available"

        log "Attempting to build Docker images locally..."
        if doppler run --project="$DOPPLER_PROJECT" --config="$DOPPLER_CONFIG" -- docker-compose -f docker-compose.prod.yml build; then
            log "✅ Successfully built Docker images locally as fallback"
            warn "⚠️  Local build completed, but consider investigating Docker Hub connectivity"
        else
            error "❌ Failed to build Docker images locally"
            error "❌ Both Docker Hub pull and local build have failed"
            error "Please check:"
            error "  1. Network connectivity to Docker Hub"
            error "  2. Docker Hub image availability"
            error "  3. Local build dependencies and Dockerfile"
            error "  4. Run: ./scripts/migrate-to-dockerhub.sh to create/update Docker Hub images"
            exit 1
        fi
    fi

    # Start services with proper dependency order
    log "Starting services..."
    doppler run --project="$DOPPLER_PROJECT" --config="$DOPPLER_CONFIG" -- docker-compose -f docker-compose.prod.yml up -d --remove-orphans

    # Wait for containers to start
    log "Waiting for containers to initialize..."
    sleep 30

    # Verify all services are running (without health checks)
    local all_containers=$(get_web_containers)
    local total_containers=$(echo "$all_containers" | wc -w)

    if [ $total_containers -eq 0 ]; then
        error "No web containers are running"
        exit 1
    fi

    log "Found $total_containers running web container(s)"
    log "Initial deployment completed successfully!"

    # Run Docker cleanup after successful initial deployment
    docker_cleanup

    return 0
}

# Rolling update deployment
deploy_rolling_update() {
    local current_count="$1"

    log "Starting rolling update with $current_count existing containers"

    # Step 1: Use docker-compose up with --force-recreate for rolling update
    # This will recreate containers one by one while maintaining service availability
    log "Performing rolling update with new image..."
    doppler run -- docker-compose -f docker-compose.prod.yml up -d --force-recreate --no-deps web

    # Step 2: Wait for containers to be ready
    log "Waiting for containers to be ready..."
    sleep 30

    # Step 3: Verify containers are running (without health checks)
    local web_containers=$(get_web_containers)
    local total_containers=$(echo "$web_containers" | wc -w)

    if [ $total_containers -eq 0 ]; then
        error "No web containers found after update"
        return 1
    fi

    log "Found $total_containers running web container(s) after update"

    log "Rolling deployment completed successfully!"

    # Run Docker cleanup after successful rolling deployment
    docker_cleanup

    return 0
}

# Docker cleanup function
docker_cleanup() {
    if [ "$SKIP_CLEANUP" = true ]; then
        log "Skipping Docker cleanup (--skip-cleanup flag specified)"
        return 0
    fi

    log "Starting Docker cleanup to reclaim disk space..."

    # Get disk usage before cleanup
    local disk_before=$(df / | awk 'NR==2 {print $3}')
    local docker_space_before=$(docker system df --format "table {{.Type}}\t{{.Size}}" | tail -n +2 | awk '{sum += $2} END {print sum}' || echo "0")

    # Remove dangling images (untagged images)
    log "Removing dangling Docker images..."
    if docker image prune -f > /dev/null 2>&1; then
        log "✅ Successfully removed dangling images"
    else
        warn "Failed to remove dangling images"
    fi

    # Remove unused containers
    log "Removing stopped containers..."
    if docker container prune -f > /dev/null 2>&1; then
        log "✅ Successfully removed stopped containers"
    else
        warn "Failed to remove stopped containers"
    fi

    # Remove unused networks
    log "Removing unused networks..."
    if docker network prune -f > /dev/null 2>&1; then
        log "✅ Successfully removed unused networks"
    else
        warn "Failed to remove unused networks"
    fi

    # Remove unused volumes (be careful with this)
    log "Removing unused volumes..."
    if docker volume prune -f > /dev/null 2>&1; then
        log "✅ Successfully removed unused volumes"
    else
        warn "Failed to remove unused volumes"
    fi

    # Clean up old application images (keep only current version)
    log "Cleaning up old application images..."
    cleanup_old_app_images

    # Get disk usage after cleanup
    local disk_after=$(df / | awk 'NR==2 {print $3}')
    local docker_space_after=$(docker system df --format "table {{.Type}}\t{{.Size}}" | tail -n +2 | awk '{sum += $2} END {print sum}' || echo "0")

    # Calculate space reclaimed
    local disk_reclaimed=$((disk_before - disk_after))
    local docker_reclaimed=$((docker_space_before - docker_space_after))

    if [ $disk_reclaimed -gt 0 ]; then
        log "✅ Docker cleanup completed - Disk space reclaimed: ${disk_reclaimed}KB"
    else
        log "✅ Docker cleanup completed - No significant disk space reclaimed"
    fi

    # Show current Docker system usage
    log "Current Docker system usage:"
    docker system df
}

# Clean up old application images while preserving current ones
cleanup_old_app_images() {
    local app_image_name="heibooky/backend"
    local current_tag="$IMAGE_TAG"

    log "Cleaning up old versions of $app_image_name (keeping current tag: $current_tag)"

    # Get all images for the application
    local old_images=$(docker images --format "{{.Repository}}:{{.Tag}}" "$app_image_name" | grep -v ":$current_tag" | grep -v ":<none>" || true)

    if [ -n "$old_images" ]; then
        log "Found old application images to remove:"
        echo "$old_images" | while read -r image; do
            log "  - $image"
        done

        # Remove old images
        echo "$old_images" | while read -r image; do
            if [ -n "$image" ]; then
                if docker rmi "$image" > /dev/null 2>&1; then
                    log "✅ Removed old image: $image"
                else
                    warn "Failed to remove image: $image (may be in use)"
                fi
            fi
        done
    else
        log "No old application images found to clean up"
    fi

    # Also clean up any untagged versions of the app image
    local untagged_images=$(docker images --filter "dangling=true" --filter "reference=$app_image_name" --format "{{.ID}}" || true)
    if [ -n "$untagged_images" ]; then
        log "Removing untagged application images..."
        echo "$untagged_images" | while read -r image_id; do
            if [ -n "$image_id" ]; then
                if docker rmi "$image_id" > /dev/null 2>&1; then
                    log "✅ Removed untagged image: $image_id"
                else
                    warn "Failed to remove untagged image: $image_id"
                fi
            fi
        done
    fi
}

# Main execution
main() {
    # Create log directory
    mkdir -p "$(dirname "$LOG_FILE")"
    
    log "=== Heibooky Deployment Started ==="
    log "Environment: $ENVIRONMENT"
    log "Image Tag: $IMAGE_TAG"
    log "Force: $FORCE"
    log "Initialize SSL: $INIT_SSL"
    log "Skip Cleanup: $SKIP_CLEANUP"
    if [ "$INIT_SSL" = true ]; then
        log "SSL Domain: $SSL_DOMAIN"
        log "SSL Email: $SSL_EMAIL"
    fi

    # Change to project directory
    cd "$PROJECT_DIR"

    # Check prerequisites
    check_prerequisites

    # Setup Doppler configuration
    setup_doppler_config

    # Initialize SSL certificates if requested
    if [ "$INIT_SSL" = true ]; then
        initialize_ssl
    fi
    
    # Validation before deployment
    if ! validate_deployment; then
        error "Pre-deployment validation failed"
        exit 1
    fi
    
    # Confirmation for production
    if [ "$ENVIRONMENT" = "production" ] && [ "$FORCE" != true ]; then
        echo -n "Are you sure you want to deploy to PRODUCTION? (yes/no): "
        read -r confirmation
        if [ "$confirmation" != "yes" ]; then
            log "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Perform deployment
    if deploy; then
        log "✅ Deployment completed successfully"
        log "=== Heibooky Deployment Completed ==="
    else
        error "❌ Deployment failed"
        exit 1
    fi
}

# Parse arguments and run main function
parse_args "$@"
main
