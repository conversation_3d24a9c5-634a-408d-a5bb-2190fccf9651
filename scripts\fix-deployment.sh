#!/bin/bash
set -e

# Quick fix script for Docker Hub migration deployment issues
# This script resolves the immediate deployment problem

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log "=== Quick Deployment Fix ==="

# Stop any running containers
log "Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down || true

# Remove any problematic images
log "Cleaning up problematic images..."
docker rmi davysongs/heibooky:latest || true
docker system prune -f

# Build the image locally
log "Building image locally..."
export DOPPLER_TOKEN_PRD="${DOPPLER_TOKEN_PRD}"
doppler run --project="heibooky-backend" --config="prd" -- docker-compose -f docker-compose.prod.yml build

# Start the services
log "Starting services..."
doppler run --project="heibooky-backend" --config="prd" -- docker-compose -f docker-compose.prod.yml up -d

# Wait for services to start
log "Waiting for services to initialize..."
sleep 30

# Check status
log "Checking service status..."
docker-compose -f docker-compose.prod.yml ps

# Check logs
log "Checking web service logs..."
docker-compose -f docker-compose.prod.yml logs --tail=20 web

log "=== Fix completed! ==="
log "If services are running correctly, you can now:"
log "1. Run ./scripts/migrate-to-dockerhub.sh to push to Docker Hub"
log "2. Update docker-compose.prod.yml to remove pull_policy: build"
log "3. Test future deployments with ./scripts/deploy.sh production"
