#!/bin/bash

# Secure Health Monitoring Script for Heibooky Monitoring Stack
# This script monitors service health using API endpoints without Docker socket access

set -euo pipefail

# Preflight checks
if (( BASH_VERSINFO[0] < 4 )); then
  echo "Bash 4+ is required (associative arrays used). Current: ${BASH_VERSINFO[*]}" >&2
  exit 1
fi
for cmd in curl timeout tee date sed awk; do
  command -v "$cmd" >/dev/null 2>&1 || { echo "Missing dependency: $cmd" >&2; exit 1; }
done
# Optional: only warn if redis-cli is absent; Redis checks will fail otherwise.
if ! command -v redis-cli >/dev/null 2>&1; then
  echo "Warning: redis-cli not found; Redis health checks will fail." >&2
fi

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
HEALTH_LOG="$PROJECT_DIR/logs/health-monitor.log"
ALERT_LOG="$PROJECT_DIR/logs/alerts.log"

# Service configuration - using API endpoints only
SERVICES=(
    "prometheus:9090:/-/healthy:GET"
    "grafana:3000:/api/health:GET"
    "loki:3100:/ready:GET"
    "alertmanager:9093:/-/healthy:GET"
    "redis:6379:ping:REDIS"
)

# Health check configuration
HEALTH_CHECK_TIMEOUT=10
CONSECUTIVE_FAILURES_THRESHOLD=3
HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-60}
RECOVERY_ENABLED=${RECOVERY_ENABLED:-false}
HEALTH_CHECK_METHOD=${HEALTH_CHECK_METHOD:-api}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
NC='\033[0m'
# Ensure log directory exists
mkdir -p "$(dirname "$HEALTH_LOG")"

# Logging functions
log() {
    local msg="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    if [ -t 1 ]; then
        echo -e "${BLUE}${msg}${NC}"
    else
        echo "$msg"
    fi
    echo "$msg" >> "$HEALTH_LOG"
}

error() {
    local msg="[ERROR] $1"
    if [ -t 1 ]; then
        echo -e "${RED}${msg}${NC}"
    else
        echo "$msg"
    fi
    echo "$msg" >> "$HEALTH_LOG"
    echo "$msg" >> "$ALERT_LOG"
}

success() {
    local msg="[SUCCESS] $1"
    if [ -t 1 ]; then
        echo -e "${GREEN}${msg}${NC}"
    else
        echo "$msg"
    fi
    echo "$msg" >> "$HEALTH_LOG"
}

warning() {
    local msg="[WARNING] $1"
    if [ -t 1 ]; then
        echo -e "${YELLOW}${msg}${NC}"
    else
        echo "$msg"
    fi
    echo "$msg" >> "$HEALTH_LOG"
}

### Helpers

check_http_health() {
    local host=$1
    local port=$2
    local path=$3
    local url="http://${host}:${port}${path}"
    local code
    code=$(timeout "$HEALTH_CHECK_TIMEOUT" curl -s -o /dev/null -w "%{http_code}" "$url" || true)
    if [[ "$code" =~ ^2[0-9]{2}$|^3[0-9]{2}$ ]]; then
        return 0
    fi
    return 1
}

check_redis_health() {
    local host=$1
    local port=$2
    # Try to ping Redis without authentication first
    if timeout "$HEALTH_CHECK_TIMEOUT" redis-cli -h "$host" -p "$port" ping >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

declare -A failure_counts
declare -A last_success

init_failure_tracking() {
    for service_config in "${SERVICES[@]}"; do
        local name
        name=$(echo "$service_config" | cut -d':' -f1)
        failure_counts["$name"]=0
        last_success["$name"]=$(date +%s)
    done
}

# Main health check function
check_service_health() {
    local service_config=$1
    local service_name port path method
    IFS=':' read -r service_name port path method <<< "$service_config"

    # Allow per-service host override via env: HOST_OVERRIDE_<SERVICE>
    local svc_upper host_var host
    svc_upper="$(echo "$service_name" | tr '[:lower:]' '[:upper:]')"
    host_var="HOST_OVERRIDE_${svc_upper}"
    host="${!host_var:-$service_name}"

    local is_healthy=false

    case "$method" in
        "GET")
            if check_http_health "$host" "$port" "$path"; then
                is_healthy=true
            fi
            ;;
        "REDIS")
            if check_redis_health "$host" "$port"; then
                is_healthy=true
            fi
            ;;
        *)
            warning "Unknown health check method: $method for service: $service_name"
            return 1
            ;;
    esac

    if [ "$is_healthy" = true ]; then
        if [ "${failure_counts[$service_name]}" -gt 0 ]; then
            success "Service $service_name recovered after ${failure_counts[$service_name]} failures"
        fi
        failure_counts["$service_name"]=0
        last_success["$service_name"]=$(date +%s)
        return 0
    else
        failure_counts["$service_name"]=$((failure_counts["$service_name"] + 1))
        error "Service $service_name health check failed (attempt ${failure_counts[$service_name]})"

        if [ "${failure_counts[$service_name]}" -ge "$CONSECUTIVE_FAILURES_THRESHOLD" ]; then
            handle_service_failure "$service_name"
        fi
        return 1
    fi
}

check_redis_health() {
    local host=$1
    local port=$2
    
    # Try to ping Redis without authentication first
    if timeout "$HEALTH_CHECK_TIMEOUT" redis-cli -h "$host" -p "$port" ping >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Main health check function
check_service_health() {
    local service_config=$1
    local service_name=$(echo "$service_config" | cut -d':' -f1)
    local port=$(echo "$service_config" | cut -d':' -f2)
    local path=$(echo "$service_config" | cut -d':' -f3)
    local method=$(echo "$service_config" | cut -d':' -f4)
    
    local is_healthy=false
    
    case "$method" in
        "GET")
            if check_http_health "$service_name" "$port" "$path"; then
                is_healthy=true
            fi
            ;;
        "REDIS")
            if check_redis_health "$service_name" "$port"; then
                is_healthy=true
            fi
            ;;
        *)
            warning "Unknown health check method: $method for service: $service_name"
            return 1
            ;;
    esac
    
    if [ "$is_healthy" = true ]; then
        if [ "${failure_counts[$service_name]}" -gt 0 ]; then
            success "Service $service_name recovered after ${failure_counts[$service_name]} failures"
        fi
        failure_counts["$service_name"]=0
        last_success["$service_name"]=$(date +%s)
        return 0
    else
        failure_counts["$service_name"]=$((failure_counts["$service_name"] + 1))
        error "Service $service_name health check failed (attempt ${failure_counts[$service_name]})"
        
        if [ "${failure_counts[$service_name]}" -ge "$CONSECUTIVE_FAILURES_THRESHOLD" ]; then
            handle_service_failure "$service_name"
        fi
        return 1
    fi
}

# Handle service failures
handle_service_failure() {
    local service_name=$1
    local failure_count=${failure_counts[$service_name]}

    error "Service $service_name has failed $failure_count consecutive health checks"

    # Calculate downtime
    local current_time=$(date +%s)
    local downtime=$((current_time - last_success["$service_name"]))

    # Cross-platform fallback for human-readable duration (avoid GNU date -d on macOS/BusyBox)
    local downtime_hms
    if date -u -d @0 +%H:%M:%S >/dev/null 2>&1; then
        downtime_hms=$(date -u -d @"$downtime" +%H:%M:%S)
    else
        # Fallback: manual formatting
        local h=$((downtime/3600)) m=$(((downtime%3600)/60)) s=$((downtime%60))
        printf -v downtime_hms '%02d:%02d:%02d' "$h" "$m" "$s"
    fi

    cat >> "$ALERT_LOG" << EOF
$(date +'%Y-%m-%d %H:%M:%S') - SERVICE FAILURE ALERT
Service: $service_name
Consecutive Failures: $failure_count
Downtime: ${downtime}s (${downtime_hms})
Last Success (epoch): ${last_success[$service_name]}
Recovery Enabled: $RECOVERY_ENABLED
EOF

    if [ "$RECOVERY_ENABLED" = "false" ]; then
        warning "Recovery is disabled. Manual intervention required for service: $service_name"
        warning "Consider using Docker's built-in restart policies or external orchestration"
        return
    fi

    warning "Automatic recovery not implemented without Docker socket access"
}

# Generate health report
generate_health_report() {
    local all_healthy=true
    local report_file="$PROJECT_DIR/logs/health-report-$(date +%Y%m%d-%H%M%S).json"

    cat > "$report_file" << EOF
{
    "timestamp": "$(date -Iseconds)",
    "health_check_method": "$HEALTH_CHECK_METHOD",
    "recovery_enabled": $RECOVERY_ENABLED,
    "services": {
EOF

    local first=true
    for service_config in "${SERVICES[@]}"; do
        local service_name failure_count is_healthy
        service_name=$(echo "$service_config" | cut -d':' -f1)
        failure_count=${failure_counts[$service_name]}
        if [ "$failure_count" -eq 0 ]; then
            is_healthy=true
        else
            is_healthy=false
            all_healthy=false
        fi

        if [ "$first" = true ]; then
            first=false
        else
            echo "," >> "$report_file"
        fi

        cat >> "$report_file" << EOF
        "$service_name": {
            "healthy": $is_healthy,
            "consecutive_failures": $failure_count,
            "last_success": "${last_success[$service_name]}"
        }
EOF
    done

    cat >> "$report_file" << EOF
    },
    "overall_healthy": $all_healthy
}
EOF

    log "Health report generated: $report_file"

    if [ "$all_healthy" = true ]; then
        success "All services are healthy"
    else
        warning "Some services are unhealthy - check $report_file for details"
    fi
}

# Main monitoring loop
main() {
    log "Starting secure health monitoring (PID: $$)"
    log "Health check method: $HEALTH_CHECK_METHOD"
    log "Recovery enabled: $RECOVERY_ENABLED"
    log "Check interval: ${HEALTH_CHECK_INTERVAL}s"

    init_failure_tracking

    while true; do
        log "Starting health check cycle..."

        for service_config in "${SERVICES[@]}"; do
            check_service_health "$service_config"
        done

        generate_health_report

        log "Health check cycle completed. Sleeping for ${HEALTH_CHECK_INTERVAL}s..."
        sleep "$HEALTH_CHECK_INTERVAL"
    done
}

# Handle signals gracefully
trap 'log "Health monitor shutting down..."; exit 0' SIGTERM SIGINT

# Run main function
main "$@"
