#!/bin/bash
set -e

# Docker Hub Migration Script
# This script builds and pushes the current image to Docker Hub

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_HUB_REPO="davysongs/heibooky"
IMAGE_TAG="${IMAGE_TAG:-latest}"
DOCKERFILE_PATH="."

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Function to check Docker Hub authentication
check_docker_auth() {
    log "Checking Docker Hub authentication..."
    
    if ! docker info | grep -q "Username:"; then
        error "Docker Hub authentication required. Please run:"
        echo "docker login docker.io"
        exit 1
    else
        local username=$(docker info | grep "Username:" | awk '{print $2}')
        log "Authenticated as: $username"
    fi
}

# Function to build the image
build_image() {
    log "Building Docker image..."
    
    # Build the production image
    docker build \
        --target production \
        --tag "${DOCKER_HUB_REPO}:${IMAGE_TAG}" \
        --tag "${DOCKER_HUB_REPO}:latest" \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse HEAD)" \
        --build-arg VERSION="${IMAGE_TAG}" \
        "${DOCKERFILE_PATH}"
    
    if [ $? -eq 0 ]; then
        log "✅ Image built successfully"
    else
        error "❌ Failed to build image"
        exit 1
    fi
}

# Function to test the image
test_image() {
    log "Testing the built image..."
    
    # Test that the entrypoint script exists
    if docker run --rm "${DOCKER_HUB_REPO}:${IMAGE_TAG}" ls -la /usr/local/bin/docker-entrypoint.sh; then
        log "✅ Entrypoint script found in image"
    else
        error "❌ Entrypoint script missing from image"
        exit 1
    fi
    
    # Test that the image can start (basic test)
    log "Testing image startup..."
    local container_id=$(docker run -d --rm "${DOCKER_HUB_REPO}:${IMAGE_TAG}" echo "test")
    if [ $? -eq 0 ]; then
        log "✅ Image startup test passed"
        docker stop "$container_id" 2>/dev/null || true
    else
        error "❌ Image startup test failed"
        exit 1
    fi
}

# Function to push the image
push_image() {
    log "Pushing image to Docker Hub..."
    
    # Push both tags
    docker push "${DOCKER_HUB_REPO}:${IMAGE_TAG}"
    if [ "${IMAGE_TAG}" != "latest" ]; then
        docker push "${DOCKER_HUB_REPO}:latest"
    fi
    
    if [ $? -eq 0 ]; then
        log "✅ Image pushed successfully to Docker Hub"
        log "Image available at: ${DOCKER_HUB_REPO}:${IMAGE_TAG}"
    else
        error "❌ Failed to push image to Docker Hub"
        exit 1
    fi
}

# Function to verify the push
verify_push() {
    log "Verifying image on Docker Hub..."
    
    # Try to pull the image to verify it's available
    docker pull "${DOCKER_HUB_REPO}:${IMAGE_TAG}"
    
    if [ $? -eq 0 ]; then
        log "✅ Image successfully verified on Docker Hub"
    else
        error "❌ Failed to verify image on Docker Hub"
        exit 1
    fi
}

# Function to update docker-compose to use pull policy
update_compose_config() {
    log "Updating docker-compose.prod.yml to use pull policy..."
    
    # Remove pull_policy: build from docker-compose.prod.yml
    if grep -q "pull_policy: build" docker-compose.prod.yml; then
        sed -i '/pull_policy: build/d' docker-compose.prod.yml
        log "✅ Removed local build policy from docker-compose.prod.yml"
        log "Docker Compose will now pull from Docker Hub by default"
    else
        log "No pull policy changes needed"
    fi
}

# Main execution
main() {
    log "=== Docker Hub Migration Script ==="
    log "Repository: ${DOCKER_HUB_REPO}"
    log "Tag: ${IMAGE_TAG}"
    
    # Check prerequisites
    check_docker_auth
    
    # Build and test the image
    build_image
    test_image
    
    # Push to Docker Hub
    push_image
    verify_push
    
    # Update configuration
    update_compose_config
    
    log "=== Migration completed successfully! ==="
    log "Next steps:"
    log "1. Test deployment: ./scripts/deploy.sh production"
    log "2. Verify services are running correctly"
    log "3. Monitor logs for any issues"
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [IMAGE_TAG]"
        echo ""
        echo "This script builds and pushes the Heibooky image to Docker Hub."
        echo ""
        echo "Environment variables:"
        echo "  IMAGE_TAG    - Docker image tag (default: latest)"
        echo ""
        echo "Prerequisites:"
        echo "  - Docker must be installed and running"
        echo "  - Must be authenticated with Docker Hub (docker login docker.io)"
        echo "  - Must be in the project root directory"
        exit 0
        ;;
    *)
        if [ -n "${1:-}" ]; then
            IMAGE_TAG="$1"
        fi
        main
        ;;
esac
