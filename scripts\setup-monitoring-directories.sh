#!/bin/bash

# Setup script for monitoring directories and permissions
# Run this script before starting the monitoring stack

set -euo pipefail

# Require root for chown/chmod on host-mounted volumes
if [[ "${EUID}" -ne 0 ]]; then
  echo "Please run this script as root (e.g., sudo $0)" >&2
  exit 1
fi

# Check if running inside Docker
if [ ! -f "/.dockerenv" ] && ! grep -qE '/docker|/kubepods' /proc/1/cgroup 2>/dev/null; then
  echo "This script must be run inside a Docker container or the intended deployment environment." >&2
  exit 1
fi
echo "Setting up monitoring directories and permissions..."

# Default paths (can be overridden by environment variables)
PROMETHEUS_DATA_PATH=${PROMETHEUS_DATA_PATH:-./monitoring/data/prometheus}
GRAFANA_DATA_PATH=${GRAFANA_DATA_PATH:-./monitoring/data/grafana}
ALERTMANAGER_DATA_PATH=${ALERTMANAGER_DATA_PATH:-./monitoring/data/alertmanager}
LOKI_DATA_PATH=${LOKI_DATA_PATH:-./monitoring/data/loki}

# Create base monitoring data directory
echo "Creating base monitoring data directory..."
mkdir -p ./monitoring/data
chmod 755 ./monitoring/data

# Create service-specific directories
echo "Creating service-specific directories..."
mkdir -p "${PROMETHEUS_DATA_PATH}"
mkdir -p "${GRAFANA_DATA_PATH}"
mkdir -p "${ALERTMANAGER_DATA_PATH}"
mkdir -p "${LOKI_DATA_PATH}"

# Create backup directories
echo "Creating backup directories..."
mkdir -p ./monitoring/reliability/prometheus-backup
mkdir -p ./monitoring/reliability/grafana-backup
mkdir -p ./monitoring/reliability/alertmanager-backup
mkdir -p ./monitoring/reliability/loki-backup

# Ensure consistent permissions on backup directories
chmod -R 755 ./monitoring/reliability/prometheus-backup
chmod -R 755 ./monitoring/reliability/grafana-backup
chmod -R 755 ./monitoring/reliability/alertmanager-backup
chmod -R 755 ./monitoring/reliability/loki-backup

# Set proper permissions
echo "Setting proper permissions..."

# Prometheus (default: nobody 65534:65534)
PROMETHEUS_UID="${PROMETHEUS_UID:-65534}"
PROMETHEUS_GID="${PROMETHEUS_GID:-65534}"
chown -R "${PROMETHEUS_UID}:${PROMETHEUS_GID}" "${PROMETHEUS_DATA_PATH}"
chmod -R 750 "${PROMETHEUS_DATA_PATH}"

# Grafana (default: 472:472)
GRAFANA_UID="${GRAFANA_UID:-472}"
GRAFANA_GID="${GRAFANA_GID:-472}"
chown -R "${GRAFANA_UID}:${GRAFANA_GID}" "${GRAFANA_DATA_PATH}"
chmod -R 750 "${GRAFANA_DATA_PATH}"

# Alertmanager (default: nobody 65534:65534)
ALERTMANAGER_UID="${ALERTMANAGER_UID:-65534}"
ALERTMANAGER_GID="${ALERTMANAGER_GID:-65534}"
chown -R "${ALERTMANAGER_UID}:${ALERTMANAGER_GID}" "${ALERTMANAGER_DATA_PATH}"
chmod -R 750 "${ALERTMANAGER_DATA_PATH}"

# Loki (default: 10001:10001)
LOKI_UID="${LOKI_UID:-10001}"
LOKI_GID="${LOKI_GID:-10001}"
chown -R "${LOKI_UID}:${LOKI_GID}" "${LOKI_DATA_PATH}"
chmod -R 750 "${LOKI_DATA_PATH}"

# Set permissions for backup directories
chown -R 65534:65534 ./monitoring/reliability/prometheus-backup
chown -R 472:472 ./monitoring/reliability/grafana-backup
chown -R 65534:65534 ./monitoring/reliability/alertmanager-backup
chown -R 10001:10001 ./monitoring/reliability/loki-backup

echo "Directory setup complete!"

# Check for potential network conflicts
echo "Checking for potential network conflicts..."
MONITORING_SUBNET=${MONITORING_SUBNET:-**********/16}
BACKEND_SUBNET=${BACKEND_SUBNET:-**********/16}

echo "Configured subnets:"
echo "  Monitoring: ${MONITORING_SUBNET}"
echo "  Backend: ${BACKEND_SUBNET}"

echo "Existing routes that might conflict with configured subnets:"
# Grep by the literal CIDR (ip route typically shows networks as CIDR)
ip route | grep -F "${MONITORING_SUBNET}" || echo "  No conflicts for MONITORING_SUBNET (${MONITORING_SUBNET})"
ip route | grep -F "${BACKEND_SUBNET}" || echo "  No conflicts for BACKEND_SUBNET (${BACKEND_SUBNET})"

echo "Existing Docker networks:"
docker network ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}" | grep -E "(monitoring|backend)" || echo "  No existing monitoring/backend networks found"

echo ""
echo "Setup complete! You can now start the monitoring stack with:"
echo "  docker-compose -f docker-compose.yml -f monitoring/docker-compose.reliability.yml up -d"
